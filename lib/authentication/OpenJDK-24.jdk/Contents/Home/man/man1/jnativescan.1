.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JNATIVESCAN" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jnativescan - static analysis tool that scans one or more jar files for
uses of native functionalities, such as restricted method calls or
\f[V]native\f[R] method declarations.
.SH SYNOPSIS
.PP
\f[V]jnativescan\f[R] [\f[I]options\f[R]]
.TP
\f[I]options\f[R]
See \f[B]Options for the jnativescan Command\f[R]
.SH DESCRIPTION
.PP
The \f[V]jnative\f[R] tool is a static analysis tool provided by the JDK
that scans a JAR file for uses of native functionalities, such as
restricted method calls or \f[V]native\f[R] method declarations.
.PP
\f[V]jnativescan\f[R] accepts a runtime class path and module path
configuration, as well as a set of root modules, and a target release.
It scans the jars on the class and module paths, and reports uses of
native functionalities either in a tree like structure, which also
identifies that calling classes and methods, or as a list of module
names when the \f[V]--print-native-access\f[R] flag is specified.
.SH OPTIONS FOR THE JNATIVESCAN COMMAND
.PP
The following options are available:
.TP
\f[V]--class-path\f[R] \f[I]path\f[R]
Used to specify a list of paths pointing to jar files to be scanned.
.PP
All jar files specified through this list will be scanned.
If a jar file contains a \f[V]Class-Path\f[R] attribute in its manifest,
jar files listed there will be scanned as well.
Jar files listed in the \f[V]Class-Path\f[R] manifest attribute that can
not be found are ignored.
All the jar files found are treated as if they belonged to the unnamed
module.
.TP
\f[V]--module-path\f[R] \f[I]path\f[R]
Used to specify a list of paths pointing to jar files or directories
containing jar files, that the tool can use to find modules that need to
be scanned.
The list of jar files that will be scanned depends on the
\f[V]--add-modules\f[R] option.
.RS
.PP
For both the \f[V]--class-path\f[R] and \f[V]--module-path\f[R] options,
\f[I]path\f[R] should be a search path that consists of one or more jar
files, separated by the system-specific path separator.
For example:
.IP \[bu] 2
\f[B]Linux and macOS:\f[R]
.RS 2
.RS
.PP
\f[V]--class-path /some/foo.jar:/another/different/bar.jar\f[R]
.RE
.RE
.PP
\f[B]Note:\f[R]
.PP
On Windows, use a semicolon (\f[V];\f[R]) as the separator instead of a
colon (\f[V]:\f[R]).
.IP \[bu] 2
\f[B]Windows:\f[R]
.RS 2
.RS
.PP
\f[V]--class-path C:\[rs]some\[rs]foo.jar;C:\[rs]another\[rs]different\[rs]bar.jar\f[R]
.RE
.RE
.RE
.TP
\f[V]--add-modules\f[R] \f[I]module[,module...]\f[R]
Used to specify a comma-separated list of module names that indicate the
root modules to scan.
All the root modules will be scanned, as well as any modules that they
depend on.
This includes dependencies on service implementations specified through
the \f[V]uses\f[R] directive in a module\[aq]s \f[V]module-info\f[R]
file.
All modules found on the module path that provide an implementation of
such a service will be scanned as well.
.TP
\f[V]--release\f[R] \f[I]version\f[R]
Used to specify the Java SE release that specifies the set of restricted
methods to scan for.
For multi-release jar files, this option also indicates the version of
class file that should be loaded from the jar.
This option should be set to the version of the runtime under which the
application is eventually intended to be run.
If this flag is omitted, the version of \f[V]jnativescan\f[R] is used as
release version, which is the same as the version of the JDK that the
tool belongs to.
.TP
\f[V]--print-native-access\f[R]
Print a comma-separated list of module names that use native
functionalities, instead of the default tree structure.
.TP
\f[V]--help\f[R] or \f[V]-h\f[R]
Prints out a full help message.
.TP
\f[V]--version\f[R]
Prints out the abbreviated version string of the tool.
.SH EXAMPLE OF \f[V]jnativescan\f[R] USE
.PP
\f[V]jnativescan\f[R] accepts a runtime configuration in the form of a
class path, module path, set of root modules, and a target release
version.
For the class path, the tool will scan all jar files, including those
found recursively through the \f[V]Class-Path\f[R] manifest attribute.
For the module path, the tool scans all root modules specified through
\f[V]--add-modules\f[R], and any (transitive) dependence of the root
modules, including any modules that contain service implementations that
are used by a scanned module.
.PP
By default, the tool prints out which jars, classes, and methods use
native functionalities, in a tree-like structure.
The following is an example output:
.IP
.nf
\f[CB]
$ jnativescan --class-path app.jar
app.jar (ALL-UNNAMED):
  foo.Main:
    foo.Main::main(String[])void references restricted methods:
      java.lang.foreign.MemorySegment::reinterpret(long)MemorySegment
    foo.Main::nativeMethod()void is a native method declaration
\f[R]
.fi
.PP
\f[V]app.jar (ALL-UNNAMED)\f[R] is the path to the jar file, with the
module name in parentheses behind it.
Since in this case the jar file appears on the class path,
\f[V]ALL-UNNAMED\f[R] is printed to indicate the unnamed module.
The second line of the output, \f[V]foo.Main\f[R], indicates that
methods using native functionalities were found in the
\f[V]foo.Main\f[R] class.
The next line:
.IP
.nf
\f[CB]
    foo.Main::main(String[])void references restricted methods:
\f[R]
.fi
.PP
Indicates that the \f[V]main(String[])\f[R] method in the
\f[V]foo.Main\f[R] class references a restricted method, which is listed
on the following line as:
.IP
.nf
\f[CB]
      java.lang.foreign.MemorySegment::reinterpret(long)MemorySegment
\f[R]
.fi
.PP
Lastly, the text:
.IP
.nf
\f[CB]
    foo.Main::nativeMethod()void is a native method declaration
\f[R]
.fi
.PP
Indicates that the \f[V]foo.Main\f[R] class contains a declaration of a
\f[V]native\f[R] method named \f[V]nativeMethod\f[R].
.PP
If we add \f[V]--print-native-access\f[R] to the example command line,
we instead get a list of the names of modules that contain accesses to
native functionalities:
.IP
.nf
\f[CB]
$ jnativescan --class-path app.jar --print-native-access
ALL-UNNAMED
\f[R]
.fi
.PP
In this case the output consists of just \f[V]ALL-UNNAMED\f[R], which
indicates a jar file on the class path, that is, in the unnamed module,
contains an access to native functionalities.
