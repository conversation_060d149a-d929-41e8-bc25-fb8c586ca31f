.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JSTAT" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jstat - monitor JVM statistics
.SH SYNOPSIS
.PP
\f[B]Note:\f[R] This command is experimental and unsupported.
.PP
\f[V]jstat\f[R] \f[I]generalOptions\f[R]
.PP
\f[V]jstat\f[R] \f[I]outputOptions\f[R] [\f[V]-t\f[R]] [\f[V]-h\f[R]
\f[I]lines\f[R]] \f[I]vmid\f[R] [\f[I]interval\f[R] [\f[I]count\f[R]]]
.TP
\f[I]generalOptions\f[R]
A single general command-line option.
See \f[B]General Options\f[R].
.TP
\f[I]outputOptions\f[R]
An option reported by the \f[V]-options\f[R] option.
One or more output options that consist of a single
\f[V]statOption\f[R], plus any of the \f[V]-t\f[R], \f[V]-h\f[R], and
\f[V]-J\f[R] options.
See \f[B]Output Options for the jstat Command\f[R].
.TP
\f[V]-t\f[R]
Displays a time-stamp column as the first column of output.
The time stamp is the time since the start time of the target JVM.
.TP
\f[V]-h\f[R] \f[I]n\f[R]
Displays a column header every \f[I]n\f[R] samples (output rows), where
\f[I]n\f[R] is a positive integer.
The default value is \f[V]0\f[R], which displays the column header of
the first row of data.
.TP
\f[I]vmid\f[R]
A virtual machine identifier, which is a string that indicates the
target JVM.
See \f[B]Virtual Machine Identifier\f[R].
.TP
\f[I]interval\f[R]
The sampling interval in the specified units, seconds (s) or
milliseconds (ms).
Default units are milliseconds.
This must be a positive integer.
When specified, the \f[V]jstat\f[R] command produces its output at each
interval.
.TP
\f[I]count\f[R]
The number of samples to display.
The default value is infinity, which causes the \f[V]jstat\f[R] command
to display statistics until the target JVM terminates or the
\f[V]jstat\f[R] command is terminated.
This value must be a positive integer.
.SH DESCRIPTION
.PP
The \f[V]jstat\f[R] command displays performance statistics for an
instrumented Java HotSpot VM.
The target JVM is identified by its virtual machine identifier, or
\f[V]vmid\f[R] option.
.PP
The \f[V]jstat\f[R] command supports two types of options, general
options and output options.
General options cause the \f[V]jstat\f[R] command to display simple
usage and version information.
Output options determine the content and format of the statistical
output.
.PP
All options and their functionality are subject to change or removal in
future releases.
.SH GENERAL OPTIONS
.PP
If you specify one of the general options, then you can\[aq]t specify
any other option or parameter.
.TP
\f[V]-help\f[R]
Displays a help message.
.TP
\f[V]-options\f[R]
Displays a list of static options.
See \f[B]Output Options for the jstat Command\f[R].
.SH OUTPUT OPTIONS FOR THE JSTAT COMMAND
.PP
If you don\[aq]t specify a general option, then you can specify output
options.
Output options determine the content and format of the \f[V]jstat\f[R]
command\[aq]s output, and consist of a single \f[V]statOption\f[R], plus
any of the other output options (\f[V]-h\f[R], \f[V]-t\f[R], and
\f[V]-J\f[R]).
The \f[V]statOption\f[R] must come first.
.PP
Output is formatted as a table, with columns that are separated by
spaces.
A header row with titles describes the columns.
Use the \f[V]-h\f[R] option to set the frequency at which the header is
displayed.
Column header names are consistent among the different options.
In general, if two options provide a column with the same name, then the
data source for the two columns is the same.
.PP
Use the \f[V]-t\f[R] option to display a time-stamp column, labeled
Timestamp as the first column of output.
The Timestamp column contains the elapsed time, in seconds, since the
target JVM started.
The resolution of the time stamp is dependent on various factors and is
subject to variation due to delayed thread scheduling on heavily loaded
systems.
.PP
Use the interval and count parameters to determine how frequently and
how many times, respectively, the \f[V]jstat\f[R] command displays its
output.
.PP
\f[B]Note:\f[R]
.PP
Don\[aq]t write scripts to parse the \f[V]jstat\f[R] command\[aq]s
output because the format might change in future releases.
If you write scripts that parse the \f[V]jstat\f[R] command output, then
expect to modify them for future releases of this tool.
.TP
\f[V]-statOption\f[R]
Determines the statistics information that the \f[V]jstat\f[R] command
displays.
The following lists the available options.
Use the \f[V]-options\f[R] general option to display the list of options
for a particular platform installation.
See \f[B]Stat Options and Output\f[R].
.RS
.PP
\f[V]class\f[R]: Displays statistics about the behavior of the class
loader.
.PP
\f[V]compiler\f[R]: Displays statistics about the behavior of the Java
HotSpot VM Just-in-Time compiler.
.PP
\f[V]gc\f[R]: Displays statistics about the behavior of the garbage
collected heap.
.PP
\f[V]gccapacity\f[R]: Displays statistics about the capacities of the
generations and their corresponding spaces.
.PP
\f[V]gccause\f[R]: Displays a summary about garbage collection
statistics (same as \f[V]-gcutil\f[R]), with the cause of the last and
current (when applicable) garbage collection events.
.PP
\f[V]gcnew\f[R]: Displays statistics about the behavior of the new
generation.
.PP
\f[V]gcnewcapacity\f[R]: Displays statistics about the sizes of the new
generations and their corresponding spaces.
.PP
\f[V]gcold\f[R]: Displays statistics about the behavior of the old
generation and metaspace statistics.
.PP
\f[V]gcoldcapacity\f[R]: Displays statistics about the sizes of the old
generation.
.PP
\f[V]gcmetacapacity\f[R]: Displays statistics about the sizes of the
metaspace.
.PP
\f[V]gcutil\f[R]: Displays a summary about garbage collection
statistics.
.PP
\f[V]printcompilation\f[R]: Displays Java HotSpot VM compilation method
statistics.
.RE
.TP
\f[V]-J\f[R]\f[I]javaOption\f[R]
Passes \f[I]javaOption\f[R] to the Java application launcher.
For example, \f[V]-J-Xms48m\f[R] sets the startup memory to 48 MB.
For a complete list of options, see \f[B]java\f[R].
.SH STAT OPTIONS AND OUTPUT
.PP
The following information summarizes the columns that the
\f[V]jstat\f[R] command outputs for each \f[I]statOption\f[R].
.TP
\f[V]-class\f[R] \f[I]option\f[R]
Class loader statistics.
.RS
.PP
\f[V]Loaded\f[R]: Number of classes loaded.
.PP
\f[V]Bytes\f[R]: Number of KB loaded.
.PP
\f[V]Unloaded\f[R]: Number of classes unloaded.
.PP
\f[V]Bytes\f[R]: Number of KB unloaded.
.PP
\f[V]Time\f[R]: Time spent performing class loading and unloading
operations.
.RE
.TP
\f[V]-compiler\f[R] \f[I]option\f[R]
Java HotSpot VM Just-in-Time compiler statistics.
.RS
.PP
\f[V]Compiled\f[R]: Number of compilation tasks performed.
.PP
\f[V]Failed\f[R]: Number of compilations tasks failed.
.PP
\f[V]Invalid\f[R]: Number of compilation tasks that were invalidated.
.PP
\f[V]Time\f[R]: Time spent performing compilation tasks.
.PP
\f[V]FailedType\f[R]: Compile type of the last failed compilation.
.PP
\f[V]FailedMethod\f[R]: Class name and method of the last failed
compilation.
.RE
.TP
\f[V]-gc\f[R] \f[I]option\f[R]
Garbage collected heap statistics.
.RS
.PP
\f[V]S0C\f[R]: Current survivor space 0 capacity (KB).
.PP
\f[V]S1C\f[R]: Current survivor space 1 capacity (KB).
.PP
\f[V]S0U\f[R]: Survivor space 0 utilization (KB).
.PP
\f[V]S1U\f[R]: Survivor space 1 utilization (KB).
.PP
\f[V]EC\f[R]: Current eden space capacity (KB).
.PP
\f[V]EU\f[R]: Eden space utilization (KB).
.PP
\f[V]OC\f[R]: Current old space capacity (KB).
.PP
\f[V]OU\f[R]: Old space utilization (KB).
.PP
\f[V]MC\f[R]: Metaspace Committed Size (KB).
.PP
\f[V]MU\f[R]: Metaspace utilization (KB).
.PP
\f[V]CCSC\f[R]: Compressed class committed size (KB).
.PP
\f[V]CCSU\f[R]: Compressed class space used (KB).
.PP
\f[V]YGC\f[R]: Number of young generation garbage collection (GC)
events.
.PP
\f[V]YGCT\f[R]: Young generation garbage collection time.
.PP
\f[V]FGC\f[R]: Number of full GC events.
.PP
\f[V]FGCT\f[R]: Full garbage collection time.
.PP
\f[V]GCT\f[R]: Total garbage collection time.
.RE
.TP
\f[V]-gccapacity\f[R] \f[I]option\f[R]
Memory pool generation and space capacities.
.RS
.PP
\f[V]NGCMN\f[R]: Minimum new generation capacity (KB).
.PP
\f[V]NGCMX\f[R]: Maximum new generation capacity (KB).
.PP
\f[V]NGC\f[R]: Current new generation capacity (KB).
.PP
\f[V]S0C\f[R]: Current survivor space 0 capacity (KB).
.PP
\f[V]S1C\f[R]: Current survivor space 1 capacity (KB).
.PP
\f[V]EC\f[R]: Current eden space capacity (KB).
.PP
\f[V]OGCMN\f[R]: Minimum old generation capacity (KB).
.PP
\f[V]OGCMX\f[R]: Maximum old generation capacity (KB).
.PP
\f[V]OGC\f[R]: Current old generation capacity (KB).
.PP
\f[V]OC\f[R]: Current old space capacity (KB).
.PP
\f[V]MCMN\f[R]: Minimum metaspace capacity (KB).
.PP
\f[V]MCMX\f[R]: Maximum metaspace capacity (KB).
.PP
\f[V]MC\f[R]: Metaspace Committed Size (KB).
.PP
\f[V]CCSMN\f[R]: Compressed class space minimum capacity (KB).
.PP
\f[V]CCSMX\f[R]: Compressed class space maximum capacity (KB).
.PP
\f[V]CCSC\f[R]: Compressed class committed size (KB).
.PP
\f[V]YGC\f[R]: Number of young generation GC events.
.PP
\f[V]FGC\f[R]: Number of full GC events.
.RE
.TP
\f[V]-gccause\f[R] \f[I]option\f[R]
This option displays the same summary of garbage collection statistics
as the \f[V]-gcutil\f[R] option, but includes the causes of the last
garbage collection event and (when applicable), the current garbage
collection event.
In addition to the columns listed for \f[V]-gcutil\f[R], this option
adds the following columns:
.RS
.PP
\f[V]LGCC\f[R]: Cause of last garbage collection
.PP
\f[V]GCC\f[R]: Cause of current garbage collection
.RE
.TP
\f[V]-gcnew\f[R] \f[I]option\f[R]
New generation statistics.
.RS
.PP
\f[V]S0C\f[R]: Current survivor space 0 capacity (KB).
.PP
\f[V]S1C\f[R]: Current survivor space 1 capacity (KB).
.PP
\f[V]S0U\f[R]: Survivor space 0 utilization (KB).
.PP
\f[V]S1U\f[R]: Survivor space 1 utilization (KB).
.PP
\f[V]TT\f[R]: Tenuring threshold.
.PP
\f[V]MTT\f[R]: Maximum tenuring threshold.
.PP
\f[V]DSS\f[R]: Desired survivor size (KB).
.PP
\f[V]EC\f[R]: Current eden space capacity (KB).
.PP
\f[V]EU\f[R]: Eden space utilization (KB).
.PP
\f[V]YGC\f[R]: Number of young generation GC events.
.PP
\f[V]YGCT\f[R]: Young generation garbage collection time.
.RE
.TP
\f[V]-gcnewcapacity\f[R] \f[I]option\f[R]
New generation space size statistics.
.RS
.PP
\f[V]NGCMN\f[R]: Minimum new generation capacity (KB).
.PP
\f[V]NGCMX\f[R]: Maximum new generation capacity (KB).
.PP
\f[V]NGC\f[R]: Current new generation capacity (KB).
.PP
\f[V]S0CMX\f[R]: Maximum survivor space 0 capacity (KB).
.PP
\f[V]S0C\f[R]: Current survivor space 0 capacity (KB).
.PP
\f[V]S1CMX\f[R]: Maximum survivor space 1 capacity (KB).
.PP
\f[V]S1C\f[R]: Current survivor space 1 capacity (KB).
.PP
\f[V]ECMX\f[R]: Maximum eden space capacity (KB).
.PP
\f[V]EC\f[R]: Current eden space capacity (KB).
.PP
\f[V]YGC\f[R]: Number of young generation GC events.
.PP
\f[V]FGC\f[R]: Number of full GC events.
.RE
.TP
\f[V]-gcold\f[R] \f[I]option\f[R]
Old generation size statistics.
.RS
.PP
\f[V]MC\f[R]: Metaspace Committed Size (KB).
.PP
\f[V]MU\f[R]: Metaspace utilization (KB).
.PP
\f[V]CCSC\f[R]: Compressed class committed size (KB).
.PP
\f[V]CCSU\f[R]: Compressed class space used (KB).
.PP
\f[V]OC\f[R]: Current old space capacity (KB).
.PP
\f[V]OU\f[R]: Old space utilization (KB).
.PP
\f[V]YGC\f[R]: Number of young generation GC events.
.PP
\f[V]FGC\f[R]: Number of full GC events.
.PP
\f[V]FGCT\f[R]: Full garbage collection time.
.PP
\f[V]GCT\f[R]: Total garbage collection time.
.RE
.TP
\f[V]-gcoldcapacity\f[R] \f[I]option\f[R]
Old generation statistics.
.RS
.PP
\f[V]OGCMN\f[R]: Minimum old generation capacity (KB).
.PP
\f[V]OGCMX\f[R]: Maximum old generation capacity (KB).
.PP
\f[V]OGC\f[R]: Current old generation capacity (KB).
.PP
\f[V]OC\f[R]: Current old space capacity (KB).
.PP
\f[V]YGC\f[R]: Number of young generation GC events.
.PP
\f[V]FGC\f[R]: Number of full GC events.
.PP
\f[V]FGCT\f[R]: Full garbage collection time.
.PP
\f[V]GCT\f[R]: Total garbage collection time.
.RE
.TP
\f[V]-gcmetacapacity\f[R] \f[I]option\f[R]
Metaspace size statistics.
.RS
.PP
\f[V]MCMN\f[R]: Minimum metaspace capacity (KB).
.PP
\f[V]MCMX\f[R]: Maximum metaspace capacity (KB).
.PP
\f[V]MC\f[R]: Metaspace Committed Size (KB).
.PP
\f[V]CCSMN\f[R]: Compressed class space minimum capacity (KB).
.PP
\f[V]CCSMX\f[R]: Compressed class space maximum capacity (KB).
.PP
\f[V]YGC\f[R]: Number of young generation GC events.
.PP
\f[V]FGC\f[R]: Number of full GC events.
.PP
\f[V]FGCT\f[R]: Full garbage collection time.
.PP
\f[V]GCT\f[R]: Total garbage collection time.
.RE
.TP
\f[V]-gcutil\f[R] \f[I]option\f[R]
Summary of garbage collection statistics.
.RS
.PP
\f[V]S0\f[R]: Survivor space 0 utilization as a percentage of the
space\[aq]s current capacity.
.PP
\f[V]S1\f[R]: Survivor space 1 utilization as a percentage of the
space\[aq]s current capacity.
.PP
\f[V]E\f[R]: Eden space utilization as a percentage of the space\[aq]s
current capacity.
.PP
\f[V]O\f[R]: Old space utilization as a percentage of the space\[aq]s
current capacity.
.PP
\f[V]M\f[R]: Metaspace utilization as a percentage of the space\[aq]s
current capacity.
.PP
\f[V]CCS\f[R]: Compressed class space utilization as a percentage.
.PP
\f[V]YGC\f[R]: Number of young generation GC events.
.PP
\f[V]YGCT\f[R]: Young generation garbage collection time.
.PP
\f[V]FGC\f[R]: Number of full GC events.
.PP
\f[V]FGCT\f[R]: Full garbage collection time.
.PP
\f[V]GCT\f[R]: Total garbage collection time.
.RE
.TP
\f[V]-printcompilation\f[R] \f[I]option\f[R]
Java HotSpot VM compiler method statistics.
.RS
.PP
\f[V]Compiled\f[R]: Number of compilation tasks performed by the most
recently compiled method.
.PP
\f[V]Size\f[R]: Number of bytes of byte code of the most recently
compiled method.
.PP
\f[V]Type\f[R]: Compilation type of the most recently compiled method.
.PP
\f[V]Method\f[R]: Class name and method name identifying the most
recently compiled method.
Class name uses a slash (/) instead of a dot (.)
as a name space separator.
The method name is the method within the specified class.
The format for these two fields is consistent with the HotSpot
\f[V]-XX:+PrintCompilation\f[R] option.
.RE
.SH VIRTUAL MACHINE IDENTIFIER
.PP
The syntax of the \f[V]vmid\f[R] string corresponds to the syntax of a
URI:
.RS
.PP
[\f[I]protocol\f[R]\f[V]:\f[R]][\f[V]//\f[R]]\f[I]lvmid\f[R][\f[V]\[at]\f[R]\f[I]hostname\f[R][\f[V]:\f[R]\f[I]port\f[R]][\f[V]/\f[R]\f[I]servername\f[R]]
.RE
.PP
The syntax of the \f[V]vmid\f[R] string corresponds to the syntax of a
URI.
The \f[V]vmid\f[R] string can vary from a simple integer that represents
a local JVM to a more complex construction that specifies a
communications protocol, port number, and other implementation-specific
values.
.TP
\f[I]protocol\f[R]
The communications protocol.
If the \f[I]protocol\f[R] value is omitted and a host name isn\[aq]t
specified, then the default protocol is a platform-specific optimized
local protocol.
If the \f[I]protocol\f[R] value is omitted and a host name is specified,
then the default protocol is \f[V]rmi\f[R].
.TP
\f[I]lvmid\f[R]
The local virtual machine identifier for the target JVM.
The \f[I]lvmid\f[R] is a platform-specific value that uniquely
identifies a JVM on a system.
The \f[I]lvmid\f[R] is the only required component of a virtual machine
identifier.
The \f[I]lvmid\f[R] is typically, but not necessarily, the operating
system\[aq]s process identifier for the target JVM process.
You can use the \f[V]jps\f[R] command to determine the \f[I]lvmid\f[R]
provided the JVM processes is not running in a separate docker instance.
You can also determine the \f[I]lvmid\f[R] on Linux and macOS platforms
with the \f[V]ps\f[R] command, and on Windows with the Windows Task
Manager.
.TP
\f[I]hostname\f[R]
A host name or IP address that indicates the target host.
If the \f[I]hostname\f[R] value is omitted, then the target host is the
local host.
.TP
\f[I]port\f[R]
The default port for communicating with the remote server.
If the \f[I]hostname\f[R] value is omitted or the \f[I]protocol\f[R]
value specifies an optimized, local protocol, then the \f[I]port\f[R]
value is ignored.
Otherwise, treatment of the \f[I]port\f[R] parameter is
implementation-specific.
For the default \f[V]rmi\f[R] protocol, the port value indicates the
port number for the \f[V]rmiregistry\f[R] on the remote host.
If the \f[I]port\f[R] value is omitted and the \f[I]protocol\f[R] value
indicates \f[V]rmi\f[R], then the default rmiregistry port (1099) is
used.
.TP
\f[I]servername\f[R]
The treatment of the \f[I]servername\f[R] parameter depends on
implementation.
For the optimized local protocol, this field is ignored.
For the \f[V]rmi\f[R] protocol, it represents the name of the RMI remote
object on the remote host.
.SH EXAMPLES
.PP
This section presents some examples of monitoring a local JVM with an
\f[I]lvmid\f[R] of 21891.
.SH THE GCUTIL OPTION
.PP
This example attaches to lvmid 21891 and takes 7 samples at 250
millisecond intervals and displays the output as specified by the
\f[V]-gcutil\f[R] option.
.PP
The output of this example shows that a young generation collection
occurred between the third and fourth sample.
The collection took 0.078 seconds and promoted objects from the eden
space (E) to the old space (O), resulting in an increase of old space
utilization from 66.80% to 68.19%.
Before the collection, the survivor space was 97.02% utilized, but after
this collection it\[aq]s 91.03% utilized.
.IP
.nf
\f[CB]
jstat -gcutil 21891 250 7
  S0     S1     E      O      M     CCS    YGC     YGCT    FGC    FGCT     GCT
  0.00  97.02  70.31  66.80  95.52  89.14      7    0.300     0    0.000    0.300
  0.00  97.02  86.23  66.80  95.52  89.14      7    0.300     0    0.000    0.300
  0.00  97.02  96.53  66.80  95.52  89.14      7    0.300     0    0.000    0.300
 91.03   0.00   1.98  68.19  95.89  91.24      8    0.378     0    0.000    0.378
 91.03   0.00  15.82  68.19  95.89  91.24      8    0.378     0    0.000    0.378
 91.03   0.00  17.80  68.19  95.89  91.24      8    0.378     0    0.000    0.378
 91.03   0.00  17.80  68.19  95.89  91.24      8    0.378     0    0.000    0.378
\f[R]
.fi
.SH REPEAT THE COLUMN HEADER STRING
.PP
This example attaches to lvmid 21891 and takes samples at 250
millisecond intervals and displays the output as specified by
\f[V]-gcnew\f[R] option.
In addition, it uses the \f[V]-h3\f[R] option to output the column
header after every 3 lines of data.
.PP
In addition to showing the repeating header string, this example shows
that between the second and third samples, a young GC occurred.
Its duration was 0.001 seconds.
The collection found enough active data that the survivor space 0
utilization (S0U) would have exceeded the desired survivor size (DSS).
As a result, objects were promoted to the old generation (not visible in
this output), and the tenuring threshold (TT) was lowered from 31 to 2.
.PP
Another collection occurs between the fifth and sixth samples.
This collection found very few survivors and returned the tenuring
threshold to 31.
.IP
.nf
\f[CB]
jstat -gcnew -h3 21891 250
 S0C    S1C    S0U    S1U   TT MTT  DSS      EC       EU     YGC     YGCT
  64.0   64.0    0.0   31.7 31  31   32.0    512.0    178.6    249    0.203
  64.0   64.0    0.0   31.7 31  31   32.0    512.0    355.5    249    0.203
  64.0   64.0   35.4    0.0  2  31   32.0    512.0     21.9    250    0.204
 S0C    S1C    S0U    S1U   TT MTT  DSS      EC       EU     YGC     YGCT
  64.0   64.0   35.4    0.0  2  31   32.0    512.0    245.9    250    0.204
  64.0   64.0   35.4    0.0  2  31   32.0    512.0    421.1    250    0.204
  64.0   64.0    0.0   19.0 31  31   32.0    512.0     84.4    251    0.204
 S0C    S1C    S0U    S1U   TT MTT  DSS      EC       EU     YGC     YGCT
  64.0   64.0    0.0   19.0 31  31   32.0    512.0    306.7    251    0.204
\f[R]
.fi
.SH INCLUDE A TIME STAMP FOR EACH SAMPLE
.PP
This example attaches to lvmid 21891 and takes 3 samples at 250
millisecond intervals.
The \f[V]-t\f[R] option is used to generate a time stamp for each sample
in the first column.
.PP
The Timestamp column reports the elapsed time in seconds since the start
of the target JVM.
In addition, the \f[V]-gcoldcapacity\f[R] output shows the old
generation capacity (OGC) and the old space capacity (OC) increasing as
the heap expands to meet allocation or promotion demands.
The old generation capacity (OGC) has grown from 11,696 KB to 13,820 KB
after the eighty-first full garbage collection (FGC).
The maximum capacity of the generation (and space) is 60,544 KB (OGCMX),
so it still has room to expand.
.IP
.nf
\f[CB]
Timestamp      OGCMN    OGCMX     OGC       OC       YGC   FGC    FGCT    GCT
          150.1   1408.0  60544.0  11696.0  11696.0   194    80    2.874   3.799
          150.4   1408.0  60544.0  13820.0  13820.0   194    81    2.938   3.863
          150.7   1408.0  60544.0  13820.0  13820.0   194    81    2.938   3.863
\f[R]
.fi
.SH MONITOR INSTRUMENTATION FOR A REMOTE JVM
.PP
This example attaches to lvmid 40496 on the system named
\f[V]remote.domain\f[R] using the \f[V]-gcutil\f[R] option, with samples
taken every second indefinitely.
.PP
The lvmid is combined with the name of the remote host to construct a
vmid of \f[V]40496\[at]remote.domain\f[R].
This vmid results in the use of the \f[V]rmi\f[R] protocol to
communicate to the default \f[V]jstatd\f[R] server on the remote host.
The \f[V]jstatd\f[R] server is located using the \f[V]rmiregistry\f[R]
command on \f[V]remote.domain\f[R] that\[aq]s bound to the default port
of the \f[V]rmiregistry\f[R] command (port 1099).
.IP
.nf
\f[CB]
jstat -gcutil 40496\[at]remote.domain 1000
\&... output omitted
\f[R]
.fi
