.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JMAP" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jmap - print details of a specified process
.SH SYNOPSIS
.PP
\f[B]Note:\f[R] This command is experimental and unsupported.
.PP
\f[V]jmap\f[R] [\f[I]options\f[R]] \f[I]pid\f[R]
.TP
\f[I]options\f[R]
This represents the \f[V]jmap\f[R] command-line options.
See \f[B]Options for the jmap Command\f[R].
.TP
\f[I]pid\f[R]
The process ID for which the information specified by the
\f[I]options\f[R] is to be printed.
The process must be a Java process.
To get a list of Java processes running on a machine, use either the
\f[V]ps\f[R] command or, if the JVM processes are not running in a
separate docker instance, the \f[B]jps\f[R] command.
.SH DESCRIPTION
.PP
The \f[V]jmap\f[R] command prints details of a specified running
process.
.PP
\f[B]Note:\f[R]
.PP
This command is unsupported and might not be available in future
releases of the JDK.
On Windows Systems where the \f[V]dbgeng.dll\f[R] file isn\[aq]t
present, the Debugging Tools for Windows must be installed to make these
tools work.
The \f[V]PATH\f[R] environment variable should contain the location of
the \f[V]jvm.dll\f[R] file that\[aq]s used by the target process or the
location from which the core dump file was produced.
.SH OPTIONS FOR THE JMAP COMMAND
.TP
\f[V]-clstats\f[R] \f[I]pid\f[R]
Connects to a running process and prints class loader statistics of Java
heap.
.TP
\f[V]-finalizerinfo\f[R] \f[I]pid\f[R]
Connects to a running process and prints information on objects awaiting
finalization.
.TP
\f[V]-histo\f[R][\f[V]:live\f[R]] \f[I]pid\f[R]
Connects to a running process and prints a histogram of the Java object
heap.
If the \f[V]live\f[R] suboption is specified, it then counts only live
objects.
.TP
\f[V]-dump:\f[R]\f[I]dump_options\f[R] \f[I]pid\f[R]
Connects to a running process and dumps the Java heap.
The \f[I]dump_options\f[R] include:
.RS
.IP \[bu] 2
\f[V]live\f[R] --- When specified, dumps only the live objects; if not
specified, then dumps all objects in the heap.
.IP \[bu] 2
\f[V]format=b\f[R] --- Dumps the Java heap in \f[V]hprof\f[R] binary
format
.IP \[bu] 2
\f[V]file=\f[R]\f[I]filename\f[R] --- Dumps the heap to
\f[I]filename\f[R]
.PP
Example: \f[V]jmap -dump:live,format=b,file=heap.bin\f[R] \f[I]pid\f[R]
.RE
