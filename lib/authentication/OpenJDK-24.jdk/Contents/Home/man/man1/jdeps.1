.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JDEPS" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jdeps - launch the Java class dependency analyzer
.SH SYNOPSIS
.PP
\f[V]jdeps\f[R] [\f[I]options\f[R]] \f[I]path\f[R] ...
.TP
\f[I]options\f[R]
Command-line options.
For detailed descriptions of the options that can be used, see
.RS
.IP \[bu] 2
\f[B]Possible Options\f[R]
.IP \[bu] 2
\f[B]Module Dependence Analysis Options\f[R]
.IP \[bu] 2
\f[B]Options to Filter Dependences\f[R]
.IP \[bu] 2
\f[B]Options to Filter Classes to be Analyzed\f[R]
.RE
.TP
\f[I]path\f[R]
A pathname to the \f[V].class\f[R] file, directory, or JAR file to
analyze.
.SH DESCRIPTION
.PP
The \f[V]jdeps\f[R] command shows the package-level or class-level
dependencies of Java class files.
The input class can be a path name to a \f[V].class\f[R] file, a
directory, a JAR file, or it can be a fully qualified class name to
analyze all class files.
The options determine the output.
By default, the \f[V]jdeps\f[R] command writes the dependencies to the
system output.
The command can generate the dependencies in DOT language (see the
\f[V]-dotoutput\f[R] option).
.SH POSSIBLE OPTIONS
.TP
\f[V]-?\f[R] or \f[V]-h\f[R] or \f[V]--help\f[R]
Prints the help message.
.TP
\f[V]-dotoutput\f[R] \f[I]dir\f[R] or \f[V]--dot-output\f[R] \f[I]dir\f[R]
Specifies the destination directory for DOT file output.
If this option is specified, then the \f[V]jdeps\f[R]command generates
one \f[V].dot\f[R] file for each analyzed archive named
\f[V]archive-file-name.dot\f[R] that lists the dependencies, and also a
summary file named \f[V]summary.dot\f[R] that lists the dependencies
among the archive files.
.TP
\f[V]-s\f[R] or \f[V]-summary\f[R]
Prints a dependency summary only.
.TP
\f[V]-v\f[R] or \f[V]-verbose\f[R]
Prints all class-level dependencies.
This is equivalent to
.RS
.RS
.PP
\f[V]-verbose:class -filter:none\f[R]
.RE
.RE
.TP
\f[V]-verbose:package\f[R]
Prints package-level dependencies excluding, by default, dependences
within the same package.
.TP
\f[V]-verbose:class\f[R]
Prints class-level dependencies excluding, by default, dependencies
within the same archive.
.TP
\f[V]-apionly\f[R] or \f[V]--api-only\f[R]
Restricts the analysis to APIs, for example, dependences from the
signature of \f[V]public\f[R] and \f[V]protected\f[R] members of public
classes including field type, method parameter types, returned type, and
checked exception types.
.TP
\f[V]-jdkinternals\f[R] or \f[V]--jdk-internals\f[R]
Finds class-level dependences in the JDK internal APIs.
By default, this option analyzes all classes specified in the
\f[V]--classpath\f[R] option and input files unless you specified the
\f[V]-include\f[R] option.
You can\[aq]t use this option with the \f[V]-p\f[R], \f[V]-e\f[R], and
\f[V]-s\f[R] options.
.RS
.PP
\f[B]Warning\f[R]: The JDK internal APIs are inaccessible.
.RE
.TP
\f[V]-cp\f[R] \f[I]path\f[R], \f[V]-classpath\f[R] \f[I]path\f[R], or \f[V]--class-path\f[R] \f[I]path\f[R]
Specifies where to find class files.
.TP
\f[V]--module-path\f[R] \f[I]module-path\f[R]
Specifies the module path.
.TP
\f[V]--upgrade-module-path\f[R] \f[I]module-path\f[R]
Specifies the upgrade module path.
.TP
\f[V]--system\f[R] \f[I]java-home\f[R]
Specifies an alternate system module path.
.TP
\f[V]--add-modules\f[R] \f[I]module-name\f[R][\f[V],\f[R] \f[I]module-name\f[R]...]
Adds modules to the root set for analysis.
.TP
\f[V]--multi-release\f[R] \f[I]version\f[R]
Specifies the version when processing multi-release JAR files.
\f[I]version\f[R] should be an integer >=9 or base.
.TP
\f[V]-q\f[R] or \f[V]-quiet\f[R]
Doesn\[aq]t show missing dependencies from
\f[V]-generate-module-info\f[R] output.
.TP
\f[V]-version\f[R] or \f[V]--version\f[R]
Prints version information.
.SH MODULE DEPENDENCE ANALYSIS OPTIONS
.TP
\f[V]-m\f[R] \f[I]module-name\f[R] or \f[V]--module\f[R] \f[I]module-name\f[R]
Specifies the root module for analysis.
.TP
\f[V]--generate-module-info\f[R] \f[I]dir\f[R]
Generates \f[V]module-info.java\f[R] under the specified directory.
The specified JAR files will be analyzed.
This option cannot be used with \f[V]--dot-output\f[R] or
\f[V]--class-path\f[R] options.
Use the \f[V]--generate-open-module\f[R] option for open modules.
.TP
\f[V]--generate-open-module\f[R] \f[I]dir\f[R]
Generates \f[V]module-info.java\f[R] for the specified JAR files under
the specified directory as open modules.
This option cannot be used with the \f[V]--dot-output\f[R] or
\f[V]--class-path\f[R] options.
.TP
\f[V]--check\f[R] \f[I]module-name\f[R] [\f[V],\f[R] \f[I]module-name\f[R]...]
Analyzes the dependence of the specified modules.
It prints the module descriptor, the resulting module dependences after
analysis and the graph after transition reduction.
It also identifies any unused qualified exports.
.TP
\f[V]--list-deps\f[R]
Lists the module dependences and also the package names of JDK internal
APIs (if referenced).
This option transitively analyzes libraries on class path and module
path if referenced.
Use \f[V]--no-recursive\f[R] option for non-transitive dependency
analysis.
.TP
\f[V]--list-reduced-deps\f[R]
Same as \f[V]--list-deps\f[R] without listing the implied reads edges
from the module graph.
If module M1 reads M2, and M2 requires transitive on M3, then M1 reading
M3 is implied and is not shown in the graph.
.TP
\f[V]--print-module-deps\f[R]
Same as \f[V]--list-reduced-deps\f[R] with printing a comma-separated
list of module dependences.
The output can be used by \f[V]jlink --add-modules\f[R] to create a
custom image that contains those modules and their transitive
dependences.
.TP
\f[V]--ignore-missing-deps\f[R]
Ignore missing dependences.
.SH OPTIONS TO FILTER DEPENDENCES
.TP
\f[V]-p\f[R] \f[I]pkg_name\f[R], \f[V]-package\f[R] \f[I]pkg_name\f[R], or \f[V]--package\f[R] \f[I]pkg_name\f[R]
Finds dependences matching the specified package name.
You can specify this option multiple times for different packages.
The \f[V]-p\f[R] and \f[V]-e\f[R] options are mutually exclusive.
.TP
\f[V]-e\f[R] \f[I]regex\f[R], \f[V]-regex\f[R] \f[I]regex\f[R], or \f[V]--regex\f[R] \f[I]regex\f[R]
Finds dependences matching the specified pattern.
The \f[V]-p\f[R] and \f[V]-e\f[R] options are mutually exclusive.
.TP
\f[V]--require\f[R] \f[I]module-name\f[R]
Finds dependences matching the given module name (may be given multiple
times).
The \f[V]--package\f[R], \f[V]--regex\f[R], and \f[V]--require\f[R]
options are mutually exclusive.
.TP
\f[V]-f\f[R] \f[I]regex\f[R] or \f[V]-filter\f[R] \f[I]regex\f[R]
Filters dependences matching the given pattern.
If give multiple times, the last one will be selected.
.TP
\f[V]-filter:package\f[R]
Filters dependences within the same package.
This is the default.
.TP
\f[V]-filter:archive\f[R]
Filters dependences within the same archive.
.TP
\f[V]-filter:module\f[R]
Filters dependences within the same module.
.TP
\f[V]-filter:none\f[R]
No \f[V]-filter:package\f[R] and \f[V]-filter:archive\f[R] filtering.
Filtering specified via the \f[V]-filter\f[R] option still applies.
.TP
\f[V]--missing-deps\f[R]
Finds missing dependences.
This option cannot be used with \f[V]-p\f[R], \f[V]-e\f[R] and
\f[V]-s\f[R] options.
.SH OPTIONS TO FILTER CLASSES TO BE ANALYZED
.TP
\f[V]-include\f[R] \f[I]regex\f[R]
Restricts analysis to the classes matching pattern.
This option filters the list of classes to be analyzed.
It can be used together with \f[V]-p\f[R] and \f[V]-e\f[R], which apply
the pattern to the dependencies.
.TP
\f[V]-R\f[R] or \f[V]--recursive\f[R]
Recursively traverses all run-time dependences.
The \f[V]-R\f[R] option implies \f[V]-filter:none\f[R].
If \f[V]-p\f[R], \f[V]-e\f[R], or \f[V]-f\f[R] options are specified,
only the matching dependences are analyzed.
.TP
\f[V]--no-recursive\f[R]
Do not recursively traverse dependences.
.TP
\f[V]-I\f[R] or \f[V]--inverse\f[R]
Analyzes the dependences per other given options and then finds all
artifacts that directly and indirectly depend on the matching nodes.
This is equivalent to the inverse of the compile-time view analysis and
the print dependency summary.
This option must be used with the \f[V]--require\f[R],
\f[V]--package\f[R], or \f[V]--regex\f[R] options.
.TP
\f[V]--compile-time\f[R]
Analyzes the compile-time view of transitive dependencies, such as the
compile-time view of the \f[V]-R\f[R] option.
Analyzes the dependences per other specified options.
If a dependency is found from a directory, a JAR file or a module, all
classes in that containing archive are analyzed.
.SH EXAMPLE OF ANALYZING DEPENDENCIES
.PP
The following example demonstrates analyzing the dependencies of the
\f[V]Notepad.jar\f[R] file.
.PP
\f[B]Linux and macOS:\f[R]
.IP
.nf
\f[CB]
$ jdeps demo/jfc/Notepad/Notepad.jar
Notepad.jar -> java.base
Notepad.jar -> java.desktop
Notepad.jar -> java.logging
   <unnamed> (Notepad.jar)
      -> java.awt
      -> java.awt.event
      -> java.beans
      -> java.io
      -> java.lang
      -> java.net
      -> java.util
      -> java.util.logging
      -> javax.swing
      -> javax.swing.border
      -> javax.swing.event
      -> javax.swing.text
      -> javax.swing.tree
      -> javax.swing.undo
\f[R]
.fi
.PP
\f[B]Windows:\f[R]
.IP
.nf
\f[CB]
C:\[rs]Java\[rs]jdk1.9.0>jdeps demo\[rs]jfc\[rs]Notepad\[rs]Notepad.jar
Notepad.jar -> java.base
Notepad.jar -> java.desktop
Notepad.jar -> java.logging
   <unnamed> (Notepad.jar)
      -> java.awt
      -> java.awt.event
      -> java.beans
      -> java.io
      -> java.lang
      -> java.net
      -> java.util
      -> java.util.logging
      -> javax.swing
      -> javax.swing.border
      -> javax.swing.event
      -> javax.swing.text
      -> javax.swing.tree
      -> javax.swing.undo
\f[R]
.fi
.SH EXAMPLE USING THE --INVERSE OPTION
.IP
.nf
\f[CB]
 $ jdeps --inverse --require java.xml.bind
Inverse transitive dependences on [java.xml.bind]
java.xml.bind <- java.se.ee
java.xml.bind <- jdk.xml.ws
java.xml.bind <- java.xml.ws <- java.se.ee
java.xml.bind <- java.xml.ws <- jdk.xml.ws
java.xml.bind <- jdk.xml.bind <- jdk.xml.ws
\f[R]
.fi
