.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JACCESSINSPECTOR" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jaccessinspector - examine accessible information about the objects in
the Java Virtual Machine using the Java Accessibility Utilities API
.SH DESCRIPTION
.PP
The \f[V]jaccessinspector\f[R] tool lets you select different methods
for examining the object accessibility information:
.IP \[bu] 2
When events occur such as a change of focus, mouse movement, property
change, menu selection, and the display of a popup menu
.IP \[bu] 2
When you press the F1 key when the mouse is over an object, or F2 when
the mouse is over a window
.PP
After an object has been selected for examination, the
\f[V]jaccessinspector\f[R] tool displays the results of calling Java
Accessibility API methods on that object.
.SH RUNNING THE JACCESSINSPECTOR TOOL
.PP
To use the \f[V]jaccessinspector\f[R] tool, launch the
\f[V]jaccessinspector\f[R] tool after launching a Java application.
To launch \f[V]jaccessinspector\f[R], run the following command:
.PP
\f[B]Note:\f[R]
.PP
\f[V]JAVA_HOME\f[R] is an environment variable and should be set to the
path of the JDK or JRE, such as
\f[V]c:\[rs]Program Files\[rs]Java\[rs]jdk-10\f[R].
.RS
.PP
\f[V]%JAVA_HOME%\[rs]bin\[rs]jaccessinspector.exe\f[R]
.RE
.PP
You now have two windows open: The Java application window and the
\f[V]jaccessinspector\f[R] window.
The \f[V]jaccessinspector\f[R] window contains five menus:
.IP \[bu] 2
\f[B]File Menu\f[R]
.IP \[bu] 2
\f[B]UpdateSettings Menu\f[R]
.IP \[bu] 2
\f[B]JavaEvents Menu\f[R]
.IP \[bu] 2
\f[B]AccessibilityEvents Menu\f[R]
.IP \[bu] 2
\f[B]Options Menu\f[R]
.PP
The items in \f[B]UpdateSettings\f[R], \f[B]JavaEvents\f[R], and
\f[B]AccessibilityEvents\f[R] menus let you query Java applications in a
variety of ways.
.SH FILE MENU
.PP
This section describes the \f[B]File\f[R] menu items.
.TP
AccessBridge DLL Loaded
Enables and disables AccessBridge DLL Loaded.
.TP
Exit
Exits from the tool.
.SH UPDATESETTINGS MENU
.PP
This section describes the \f[B]UpdateSettings\f[R] menu items.
.TP
Update from Mouse
Determines the x- and y-coordinates of the mouse (assuming the
\f[V]jaccessinspector\f[R] tool window is topmost) when the mouse has
stopped moving, and then queries the Java application for the accessible
object underneath the mouse, dumping the output into the
\f[V]jaccessinspector\f[R] window.
.TP
Update with F2 (Mouse HWND)
Determines the x- and y-coordinates of the mouse (assuming the
\f[V]jaccessinspector\f[R] tool window is topmost), and then queries the
Java application for the accessible object of the HWND underneath the
mouse, dumping the output into the \f[V]jaccessinspector\f[R] window.
.TP
Update with F1 (Mouse Point)
Determines the x- and y-coordinates of the mouse (assuming the
\f[V]jaccessinspector\f[R] tool window is topmost), and then queries the
Java application for the accessible object underneath the cursor,
dumping the output into the \f[V]jaccessinspector\f[R] window.
.SH JAVAEVENTS MENU
.PP
This section describes the \f[B]JavaEvents\f[R] menu items.
.TP
Track Mouse Events
Registers with the Java application all Java Mouse Entered events, and
upon receiving one, queries the object that was entered by the cursor
and dumps the output into the \f[V]jaccessinspector\f[R] window.
.RS
.PP
\f[B]Note:\f[R] If the mouse is moved quickly, then there may be some
delay before the displayed information is updated.
.RE
.TP
Track Focus Events
Registers with the Java application all Java Focus Gained events, and
upon receiving an event, queries the object that received the focus and
dumps the output into the \f[V]jaccessinspector\f[R] window.
.TP
Track Caret Events
Register with the Java application all Java Caret Update events, and
upon receiving an event, queries the object in which the caret was
updated, and dumps the output into the \f[V]jaccessinspector\f[R]
window.
.RS
.PP
\f[B]Note:\f[R] Because objects that contain carets are almost by
definition objects that are rich text objects, this won\[aq]t seem as
responsive as the other event tracking options.
In real use, one would make fewer accessibility calls in Caret Update
situations (for example, just get the new letter, word, sentence at the
caret location), which would be significantly faster.
.RE
.TP
Track Menu Selected | Deselected | Cancelled Events
Registers with the Java application all Menu events, and upon receiving
an event, queries the object in which the caret was updated, and dumps
the output into the \f[V]jaccessinspector\f[R] window.
.TP
Track Popup Visible | Invisible | Cancelled Events
Registers with the Java application all Popup Menu events, and upon
receiving an event, queries the object in which the caret was updated,
and dumps the output into the \f[V]jaccessinspector\f[R] window.
.TP
Track Shutdown Events
Registers with the Java application to receive a Property Changed event
when a Java application terminates.
.SH ACCESSIBILITYEVENTS MENU
.PP
This section describes the \f[B]AccessibilityEvents\f[R] menu items.
.PP
\f[B]Note:\f[R] The items listed in the \f[B]AccessibilityEvents\f[R]
menu are the most important for testing applications, especially for
assistive technology applications.
.TP
Track Name Property Events
Registers with the Java application all Java Property Changed events
specifically on accessible objects in which the Name property has
changed, and upon receiving an event, dumps the output into the
scrolling window, along with information about the property that
changed.
.TP
Track Description Property Events
Register with the Java application for all Java Property Changed events
specifically on accessible objects in which the Description property has
changed, and upon receiving an event, dumps the output into the
\f[V]jaccessinspector\f[R] window, along with information about the
property that changed.
.TP
Track State Property Events
Register with the Java application all Java Property Changed events
specifically on accessible objects in which the State property has
changed, and upon receiving an event, dumps the output into the
\f[V]jaccessinspector\f[R] window, along with information about the
property that changed.
.TP
Track Value Property Events
Register with the Java application all Java Property Changed events
specifically on accessible objects in which the Value property has
changed, and upon receiving an event, dumps the output into the
scrolling window, along with information about the property that
changed.
.TP
Track Selection Property Events
Register with the Java application all Java Property Changed events
specifically on accessible objects in which the Selection property has
changed, and upon receiving an event, dumps the output into the
\f[V]jaccessinspector\f[R] window, along with information about the
property that changed.
.TP
Track Text Property Events
Register with the Java application all Java Property Changed events
specifically on accessible objects in which the Text property has
changed, and upon receiving one event, dump the output into the
\f[V]jaccessinspector\f[R] window, along with information about the
property that changed.
.TP
Track Caret Property Events
Register with the Java application all Java Property Changed events
specifically on accessible objects in which the Caret property has
changed, and upon receiving an event, dumps the output into the
\f[V]jaccessinspector\f[R] window, along with information about the
property that changed.
.TP
Track VisibleData Property Events
Register with the Java application all Java Property Changed events
specifically on accessible objects in which the VisibleData property has
changed, and upon receiving an event, dumps the output into the
\f[V]jaccessinspector\f[R] window, along with information about the
property that changed.
.TP
Track Child Property Events
Register with the Java application all Java Property Changed events
specifically on accessible objects in which the Child property has
changed, and upon receiving an event, dumps the output into the
\f[V]jaccessinspector\f[R] window, along with information about the
property that changed.
.TP
Track Active Descendent Property Events
Register with the Java application all Java Property Changed events
specifically on accessible objects in which the Active Descendent
property has changed, and upon receiving an event, dumps the output into
the \f[V]jaccessinspector\f[R] window, along with information about the
property that changed.
.TP
Track Table Model Change Property Events
Register with the Java application all Property Changed events
specifically on accessible objects in which the Table Model Change
property has changed, and upon receiving an event, dumps the output into
the \f[V]jaccessinspector\f[R] window, along with information about the
property that changed.
.SH OPTIONS MENU
.PP
This section describes the \f[B]Options\f[R] menu items.
.TP
Monitor the same events as JAWS
Enables monitoring of only the events also monitored by JAWS.
.TP
Monitor All Events
Enables monitoring of all events in the \f[V]jaccessinspector\f[R]
window.
.TP
Reset All Events
Resets the selected Options to the default settings.
.TP
Go To Message
Opens the \f[B]Go To Message\f[R] dialog that lets you display a logged
message by entering its message number.
.TP
Clear Message History
Clears the history of logged messages from the
\f[V]jaccessinspector\f[R] window.
