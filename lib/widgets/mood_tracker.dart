import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MoodTracker extends StatefulWidget {
  final String timeOfDay;

  const MoodTracker({super.key, required this.timeOfDay});

  @override
  State<MoodTracker> createState() => _MoodTrackerState();
}

class _MoodTrackerState extends State<MoodTracker>
    with TickerProviderStateMixin {
  double _moodValue = 3.0; // Default to middle (Okay)
  String? selectedMood;
  bool hasTrackedToday = false;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  final List<Map<String, dynamic>> moods = [
    {
      'name': 'Struggling',
      'emoji': '😢',
      'color': const Color(0xFFF44336),
      'description': 'Having a tough time',
      'value': 1.0,
    },
    {
      'name': 'Low',
      'emoji': '😔',
      'color': const Color(0xFFFF9800),
      'description': 'Feeling down',
      'value': 2.0,
    },
    {
      'name': 'Okay',
      'emoji': '😐',
      'color': const Color(0xFFFFEB3B),
      'description': 'Just okay',
      'value': 3.0,
    },
    {
      'name': 'Good',
      'emoji': '😊',
      'color': const Color(0xFF8BC34A),
      'description': 'Pretty good day',
      'value': 4.0,
    },
    {
      'name': 'Amazing',
      'emoji': '😄',
      'color': const Color(0xFF4CAF50),
      'description': 'Feeling fantastic!',
      'value': 5.0,
    },
  ];

  @override
  void initState() {
    super.initState();
    _checkIfTrackedToday();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  Future<void> _checkIfTrackedToday() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final dateKey = '${today.year}-${today.month}-${today.day}';
    final timeKey = '${dateKey}_${widget.timeOfDay}';

    final trackedMood = prefs.getString(timeKey);
    final trackedValue = prefs.getDouble('${timeKey}_value');

    setState(() {
      hasTrackedToday = trackedMood != null;
      selectedMood = trackedMood;
      if (trackedValue != null) {
        _moodValue = trackedValue;
      }
    });
  }

  Future<void> _saveMood(double value) async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final dateKey = '${today.year}-${today.month}-${today.day}';
    final timeKey = '${dateKey}_${widget.timeOfDay}';

    // Find the mood that corresponds to this value
    final mood = moods.firstWhere((m) => m['value'] == value);

    await prefs.setString(timeKey, mood['name']);
    await prefs.setDouble('${timeKey}_value', value);

    setState(() {
      selectedMood = mood['name'];
      _moodValue = value;
      hasTrackedToday = true;
    });

    // Trigger celebration animation
    _pulseController.forward().then((_) {
      _pulseController.reverse();
    });

    // Show success message
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Text('Mood tracked for this ${widget.timeOfDay}!'),
            ],
          ),
          backgroundColor: const Color(0xFF4CAF50),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final currentMood = moods.firstWhere((m) => m['value'] == _moodValue);

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: hasTrackedToday ? _pulseAnimation.value : 1.0,
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  currentMood['color'].withValues(alpha: 0.1),
                  currentMood['color'].withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: currentMood['color'].withValues(alpha: 0.2),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: currentMood['color'].withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                // Header with mood display
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: currentMood['color'].withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        currentMood['emoji'],
                        style: const TextStyle(fontSize: 24),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            hasTrackedToday
                                ? 'Feeling ${currentMood['name']}'
                                : 'How are you feeling?',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: currentMood['color'],
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            hasTrackedToday
                                ? 'Tracked for this ${widget.timeOfDay}'
                                : currentMood['description'],
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (hasTrackedToday)
                      Icon(
                        Icons.check_circle,
                        color: const Color(0xFF4CAF50),
                        size: 20,
                      ),
                  ],
                ),

                if (!hasTrackedToday) ...[
                  const SizedBox(height: 16),

                  // Compact mood slider
                  SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: currentMood['color'],
                      inactiveTrackColor: currentMood['color'].withValues(
                        alpha: 0.3,
                      ),
                      thumbColor: currentMood['color'],
                      overlayColor: currentMood['color'].withValues(alpha: 0.2),
                      thumbShape: const RoundSliderThumbShape(
                        enabledThumbRadius: 10,
                      ),
                      trackHeight: 4,
                    ),
                    child: Slider(
                      value: _moodValue,
                      min: 1.0,
                      max: 5.0,
                      divisions: 4,
                      onChanged: (value) {
                        setState(() {
                          _moodValue = value;
                        });
                      },
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Compact track button
                  Align(
                    alignment: Alignment.centerRight,
                    child: ElevatedButton.icon(
                      onPressed: () => _saveMood(_moodValue),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: currentMood['color'],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        elevation: 2,
                      ),
                      icon: const Icon(Icons.check, size: 16),
                      label: Text(
                        'Track',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
