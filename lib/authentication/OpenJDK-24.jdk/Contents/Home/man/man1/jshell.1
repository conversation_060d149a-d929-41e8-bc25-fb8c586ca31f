'\" t
.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JSHELL" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jshell - interactively evaluate declarations, statements, and
expressions of the Java programming language in a read-eval-print loop
(REPL)
.SH SYNOPSIS
.PP
\f[V]jshell\f[R] [\f[I]options\f[R]] [\f[I]load-files\f[R]]
.TP
\f[I]options\f[R]
Command-line options, separated by spaces.
See \f[B]Options for jshell\f[R].
.TP
\f[I]load-files\f[R]
One or more scripts to run when the tool is started.
Scripts can contain any valid code snippets or JShell commands.
.RS
.PP
The script can be a local file or one of following predefined scripts:
.TP
\f[V]DEFAULT\f[R]
Loads the default entries, which are commonly used as imports.
.TP
\f[V]JAVASE\f[R]
Imports all Java SE packages.
.TP
\f[V]PRINTING\f[R]
Defines \f[V]print\f[R], \f[V]println\f[R], and \f[V]printf\f[R] as
\f[V]jshell\f[R] methods for use within the tool.
.TP
\f[V]TOOLING\f[R]
Defines \f[V]javac\f[R], \f[V]jar\f[R], and other methods for running
JDK tools via their command-line interface within the \f[V]jshell\f[R]
tool.
.PP
For more than one script, use a space to separate the names.
Scripts are run in the order in which they\[aq]re entered on the command
line.
Command-line scripts are run after startup scripts.
To run a script after JShell is started, use the \f[V]/open\f[R]
command.
.PP
To accept input from standard input and suppress the interactive I/O,
enter a hyphen (-) for \f[I]load-files\f[R].
This option enables the use of the \f[V]jshell\f[R] tool in pipe chains.
.RE
.SH DESCRIPTION
.PP
JShell provides a way to interactively evaluate declarations,
statements, and expressions of the Java programming language, making it
easier to learn the language, explore unfamiliar code and APIs, and
prototype complex code.
Java statements, variable definitions, method definitions, class
definitions, import statements, and expressions are accepted.
The bits of code entered are called snippets.
.PP
As snippets are entered, they\[aq]re evaluated, and feedback is
provided.
Feedback varies from the results and explanations of actions to nothing,
depending on the snippet entered and the feedback mode chosen.
Errors are described regardless of the feedback mode.
Start with the verbose mode to get the most feedback while learning the
tool.
.PP
Command-line options are available for configuring the initial
environment when JShell is started.
Within JShell, commands are available for modifying the environment as
needed.
.PP
Existing snippets can be loaded from a file to initialize a JShell
session, or at any time within a session.
Snippets can be modified within the session to try out different
variations and make corrections.
To keep snippets for later use, save them to a file.
.SH OPTIONS FOR JSHELL
.TP
\f[V]--add-exports\f[R] \f[I]module\f[R]/\f[I]package\f[R]
Specifies a package to be considered as exported from its defining
module.
.TP
\f[V]--add-modules\f[R] \f[I]module\f[R][\f[V],\f[R]\f[I]module\f[R]...]
Specifies the root modules to resolve in addition to the initial module.
.TP
\f[V]-C\f[R]\f[I]flag\f[R]
passes \f[I]flag\f[R] to the Java compiler inside JShell.
For example, \f[V]-C-Xlint\f[R] enables all the recommended lint
warnings, and \f[V]-C--release=<N>\f[R] compiles for Java SE N, as if
--release N was specified.
.TP
\f[V]--class-path\f[R] \f[I]path\f[R]
Specifies the directories and archives that are searched to locate class
files.
This option overrides the path in the \f[V]CLASSPATH\f[R] environment
variable.
If the environment variable isn\[aq]t set and this option isn\[aq]t
used, then the current directory is searched.
For Linux and macOS, use a colon (:) to separate items in the path.
For Windows, use a semicolon (;) to separate items.
.TP
\f[V]--enable-preview\f[R]
Allows code to depend on the preview features of this release.
.TP
\f[V]--execution\f[R] \f[I]specification\f[R]
Specifies an alternate execution engine, where \f[I]specification\f[R]
is an ExecutionControl spec.
See the documentation of the package jdk.jshell.spi for the syntax of
the spec.
.TP
\f[V]--feedback\f[R] \f[I]mode\f[R]
Sets the initial level of feedback provided in response to what\[aq]s
entered.
The initial level can be overridden within a session by using the
\f[V]/set feedback\f[R] \f[I]mode\f[R] command.
The default is \f[V]normal\f[R].
.RS
.PP
The following values are valid for \f[I]mode\f[R]:
.TP
\f[V]verbose\f[R]
Provides detailed feedback for entries.
Additional information about the action performed is displayed after the
result of the action.
The next prompt is separated from the feedback by a blank line.
.TP
\f[V]normal\f[R]
Provides an average amount of feedback.
The next prompt is separated from the feedback by a blank line.
.TP
\f[V]concise\f[R]
Provides minimal feedback.
The next prompt immediately follows the code snippet or feedback.
.TP
\f[V]silent\f[R]
Provides no feedback.
The next prompt immediately follows the code snippet.
.TP
\f[I]custom\f[R]
Provides custom feedback based on how the mode is defined.
Custom feedback modes are created within JShell by using the
\f[V]/set mode\f[R] command.
.RE
.TP
\f[V]--help\f[R] or \f[V]-h\f[R] or \f[V]-?\f[R]
Prints a summary of standard options and exits the tool.
.TP
\f[V]--help-extra\f[R] or \f[V]-X\f[R]
Prints a summary of nonstandard options and exits the tool.
Nonstandard options are subject to change without notice.
.TP
\f[V]-J\f[R]\f[I]flag\f[R]
passes \f[I]flag\f[R] to the runtime system, but has no effect on the
execution of code snippets.
To specify flags that affect the execution of code snippets, use
\f[V]-R\f[R]\f[I]flag\f[R].
Alternatively, use \f[V]-J\f[R]\f[I]flag\f[R] with
\f[V]--execution local\f[R].
.TP
\f[V]--module-path\f[R] \f[I]modulepath\f[R]
Specifies where to find application modules.
For Linux and macOS, use a colon (:) to separate items in the path.
For Windows, use a semicolon (;) to separate items.
.TP
\f[V]--no-startup\f[R]
Prevents startup scripts from running when JShell starts.
Use this option to run only the scripts entered on the command line when
JShell is started, or to start JShell without any preloaded information
if no scripts are entered.
This option can\[aq]t be used if the \f[V]--startup\f[R] option is used.
.TP
\f[V]-q\f[R]
Sets the feedback mode to \f[V]concise\f[R], which is the same as
entering \f[V]--feedback concise\f[R].
.TP
\f[V]-R\f[R]\f[I]flag\f[R]
passes \f[I]flag\f[R] to the runtime system only when code snippets are
executed.
For example, \f[V]-R-Dfoo=bar\f[R] means that execution of the snippet
\f[V]System.getProperty(\[dq]foo\[dq])\f[R] will return
\f[V]\[dq]bar\[dq]\f[R].
.TP
\f[V]-s\f[R]
Sets the feedback mode to \f[V]silent\f[R], which is the same as
entering \f[V]--feedback silent\f[R].
.TP
\f[V]--show-version\f[R]
Prints version information and enters the tool.
.TP
\f[V]--startup\f[R] \f[I]file\f[R]
Overrides the default startup script for this session.
The script can contain any valid code snippets or commands.
.RS
.PP
The script can be a local file or one of the following predefined
scripts:
.TP
\f[V]DEFAULT\f[R]
Loads the default entries, which are commonly used as imports.
.TP
\f[V]JAVASE\f[R]
Imports all Java SE packages.
.TP
\f[V]PRINTING\f[R]
Defines \f[V]print\f[R], \f[V]println\f[R], and \f[V]printf\f[R] as
\f[V]jshell\f[R] methods for use within the tool.
.TP
\f[V]TOOLING\f[R]
Defines \f[V]javac\f[R], \f[V]jar\f[R], and other methods for running
JDK tools via their command-line interface within the \f[V]jshell\f[R]
tool.
.PP
For more than one script, provide a separate instance of this option for
each script.
Startup scripts are run when JShell is first started and when the
session is restarted with the \f[V]/reset\f[R], \f[V]/reload\f[R], or
\f[V]/env\f[R] command.
Startup scripts are run in the order in which they\[aq]re entered on the
command line.
.PP
This option can\[aq]t be used if the \f[V]--no-startup\f[R] option is
used.
.RE
.TP
\f[V]-v\f[R]
Sets the feedback mode to \f[V]verbose\f[R], which is the same as
entering \f[V]--feedback verbose\f[R].
.TP
\f[V]--version\f[R]
Prints version information and exits the tool.
.SH JSHELL COMMANDS
.PP
Within the \f[V]jshell\f[R] tool, commands are used to modify the
environment and manage code snippets.
.TP
\f[V]/drop\f[R] {\f[I]name\f[R]|\f[I]id\f[R]|\f[I]startID\f[R]\f[V]-\f[R]\f[I]endID\f[R]} [{\f[I]name\f[R]|\f[I]id\f[R]|\f[I]startID\f[R]\f[V]-\f[R]\f[I]endID\f[R]}...]
Drops snippets identified by name, ID, or ID range, making them
inactive.
For a range of IDs, provide the starting ID and ending ID separated with
a hyphen.
To provide a list, separate the items in the list with a space.
Use the \f[V]/list\f[R] command to see the IDs of code snippets.
.TP
\f[V]/edit\f[R] [\f[I]option\f[R]]
Opens an editor.
If no option is entered, then the editor opens with the active snippets.
.RS
.PP
The following options are valid:
.TP
{\f[I]name\f[R]|\f[I]id\f[R]|\f[I]startID\f[R]\f[V]-\f[R]\f[I]endID\f[R]} [{\f[I]name\f[R]|\f[I]id\f[R]|\f[I]startID\f[R]\f[V]-\f[R]\f[I]endID\f[R]}...]
Opens the editor with the snippets identified by name, ID, or ID range.
For a range of IDs, provide the starting ID and ending ID separated with
a hyphen.
To provide a list, separate the items in the list with a space.
Use the \f[V]/list\f[R] command to see the IDs of code snippets.
.TP
\f[V]-all\f[R]
Opens the editor with all snippets, including startup snippets and
snippets that failed, were overwritten, or were dropped.
.TP
\f[V]-start\f[R]
Opens the editor with startup snippets that were evaluated when JShell
was started.
.PP
To exit edit mode, close the editor window, or respond to the prompt
provided if the \f[V]-wait\f[R] option was used when the editor was set.
.PP
Use the \f[V]/set editor\f[R] command to specify the editor to use.
If no editor is set, then the following environment variables are
checked in order: \f[V]JSHELLEDITOR\f[R], \f[V]VISUAL\f[R], and
\f[V]EDITOR\f[R].
If no editor is set in JShell and none of the editor environment
variables is set, then a simple default editor is used.
.RE
.TP
\f[V]/env\f[R] [\f[I]options\f[R]]
Displays the environment settings, or updates the environment settings
and restarts the session.
If no option is entered, then the current environment settings are
displayed.
If one or more options are entered, then the session is restarted as
follows:
.RS
.IP \[bu] 2
Updates the environment settings with the provided options.
.IP \[bu] 2
Resets the execution state.
.IP \[bu] 2
Runs the startup scripts.
.IP \[bu] 2
Silently replays the history in the order entered.
The history includes all valid snippets or \f[V]/drop\f[R] commands
entered at the \f[V]jshell\f[R] prompt, in scripts entered on the
command line, or scripts entered with the \f[V]/open\f[R] command.
.PP
Environment settings entered on the command line or provided with a
previous \f[V]/reset\f[R], \f[V]/env\f[R], or \f[V]/reload\f[R] command
are maintained unless an \f[I]option\f[R] is entered that overwrites the
setting.
.PP
The following options are valid:
.TP
\f[V]--add-modules\f[R] \f[I]module\f[R][\f[V],\f[R]\f[I]module\f[R]...]
Specifies the root modules to resolve in addition to the initial module.
.TP
\f[V]--add-exports\f[R] \f[I]source-module\f[R]\f[V]/\f[R]\f[I]package\f[R]\f[V]=\f[R]\f[I]target-module\f[R][\f[V],\f[R]\f[I]target-module\f[R]]*
Adds an export of \f[I]package\f[R] from \f[I]source-module\f[R] to
\f[I]target-module\f[R].
.TP
\f[V]--class-path\f[R] \f[I]path\f[R]
Specifies the directories and archives that are searched to locate class
files.
This option overrides the path in the \f[V]CLASSPATH\f[R] environment
variable.
If the environment variable isn\[aq]t set and this option isn\[aq]t
used, then the current directory is searched.
For Linux and macOS, use a colon (\f[V]:\f[R]) to separate items in the
path.
For Windows, use a semicolon (\f[V];\f[R]) to separate items.
.TP
\f[V]--module-path\f[R] \f[I]modulepath\f[R]
Specifies where to find application modules.
For Linux and macOS, use a colon (\f[V]:\f[R]) to separate items in the
path.
For Windows, use a semicolon (\f[V];\f[R]) to separate items.
.RE
.TP
\f[V]/exit\f[R] [\f[I]integer-expression-snippet\f[R]]
Exits the tool.
If no snippet is entered, the exit status is zero.
If a snippet is entered and the result of the snippet is an integer, the
result is used as the exit status.
If an error occurs, or the result of the snippet is not an integer, an
error is displayed and the tool remains active.
.TP
\f[V]/history\f[R]
Displays what was entered in this session.
.TP
\f[V]/help\f[R] [\f[I]command\f[R]|\f[I]subject\f[R]]
Displays information about commands and subjects.
If no options are entered, then a summary of information for all
commands and a list of available subjects are displayed.
If a valid command is provided, then expanded information for that
command is displayed.
If a valid subject is entered, then information about that subject is
displayed.
.RS
.PP
The following values for \f[I]subject\f[R] are valid:
.TP
\f[V]context\f[R]
Describes the options that are available for configuring the
environment.
.TP
\f[V]intro\f[R]
Provides an introduction to the tool.
.TP
\f[V]shortcuts\f[R]
Describes keystrokes for completing commands and snippets.
See \f[B]Input Shortcuts\f[R].
.RE
.TP
\f[V]/imports\f[R]
Displays the current active imports, including those from the startup
scripts and scripts that were entered on the command line when JShell
was started.
.TP
\f[V]/list\f[R] [\f[I]option\f[R]]
Displays a list of snippets and their IDs.
If no option is entered, then all active snippets are displayed, but
startup snippets aren\[aq]t.
.RS
.PP
The following options are valid:
.TP
{\f[I]name\f[R]|\f[I]id\f[R]|\f[I]startID\f[R]\f[V]-\f[R]\f[I]endID\f[R]} [{\f[I]name\f[R]|\f[I]id\f[R]|\f[I]startID\f[R]\f[V]-\f[R]\f[I]endID\f[R]}...]
Displays the snippets identified by name, ID, or ID range.
For a range of IDs, provide the starting ID and ending ID separated with
a hyphen.
To provide a list, separate the items in the list with a space.
.TP
\f[V]-all\f[R]
Displays all snippets, including startup snippets and snippets that
failed, were overwritten, or were dropped.
IDs that begin with \f[V]s\f[R] are startup snippets.
IDs that begin with \f[V]e\f[R] are snippets that failed.
.TP
\f[V]-start\f[R]
Displays startup snippets that were evaluated when JShell was started.
.RE
.TP
\f[V]/methods\f[R] [\f[I]option\f[R]]
Displays information about the methods that were entered.
If no option is entered, then the name, parameter types, and return type
of all active methods are displayed.
.RS
.PP
The following options are valid:
.TP
{\f[I]name\f[R]|\f[I]id\f[R]|\f[I]startID\f[R]\f[V]-\f[R]\f[I]endID\f[R]} [{\f[I]name\f[R]|\f[I]id\f[R]|\f[I]startID\f[R]\f[V]-\f[R]\f[I]endID\f[R]}...]
Displays information for methods identified by name, ID, or ID range.
For a range of IDs, provide the starting ID and ending ID separated with
a hyphen.
To provide a list, separate the items in the list with a space.
Use the \f[V]/list\f[R] command to see the IDs of code snippets.
.TP
\f[V]-all\f[R]
Displays information for all methods, including those added when JShell
was started, and methods that failed, were overwritten, or were dropped.
.TP
\f[V]-start\f[R]
Displays information for startup methods that were added when JShell was
started.
.RE
.TP
\f[V]/open\f[R] \f[I]file\f[R]
Opens the script specified and reads the snippets into the tool.
The script can be a local file or one of the following predefined
scripts:
.RS
.TP
\f[V]DEFAULT\f[R]
Loads the default entries, which are commonly used as imports.
.TP
\f[V]JAVASE\f[R]
Imports all Java SE packages.
.TP
\f[V]PRINTING\f[R]
Defines \f[V]print\f[R], \f[V]println\f[R], and \f[V]printf\f[R] as
\f[V]jshell\f[R] methods for use within the tool.
.TP
\f[V]TOOLING\f[R]
Defines \f[V]javac\f[R], \f[V]jar\f[R], and other methods for running
JDK tools via their command-line interface within the \f[V]jshell\f[R]
tool.
.RE
.TP
\f[V]/reload\f[R] [\f[I]options\f[R]]
Restarts the session as follows:
.RS
.IP \[bu] 2
Updates the environment settings with the provided options, if any.
.IP \[bu] 2
Resets the execution state.
.IP \[bu] 2
Runs the startup scripts.
.IP \[bu] 2
Replays the history in the order entered.
The history includes all valid snippets or \f[V]/drop\f[R] commands
entered at the \f[V]jshell\f[R] prompt, in scripts entered on the
command line, or scripts entered with the \f[V]/open\f[R] command.
.PP
Environment settings entered on the command line or provided with a
previous \f[V]/reset\f[R], \f[V]/env\f[R], or \f[V]/reload\f[R] command
are maintained unless an \f[I]option\f[R] is entered that overwrites the
setting.
.PP
The following options are valid:
.TP
\f[V]--add-modules\f[R] \f[I]module\f[R][\f[V],\f[R]\f[I]module\f[R]...]
Specifies the root modules to resolve in addition to the initial module.
.TP
\f[V]--add-exports\f[R] \f[I]source-module\f[R]\f[V]/\f[R]\f[I]package\f[R]\f[V]=\f[R]\f[I]target-module\f[R][\f[V],\f[R]\f[I]target-module\f[R]]*
Adds an export of \f[I]package\f[R] from \f[I]source-module\f[R] to
\f[I]target-module\f[R].
.TP
\f[V]--class-path\f[R] \f[I]path\f[R]
Specifies the directories and archives that are searched to locate class
files.
This option overrides the path in the \f[V]CLASSPATH\f[R] environment
variable.
If the environment variable isn\[aq]t set and this option isn\[aq]t
used, then the current directory is searched.
For Linux and macOS, use a colon (\f[V]:\f[R]) to separate items in the
path.
For Windows, use a semicolon (\f[V];\f[R]) to separate items.
.TP
\f[V]--module-path\f[R] \f[I]modulepath\f[R]
Specifies where to find application modules.
For Linux and macOS, use a colon (\f[V]:\f[R]) to separate items in the
path.
For Windows, use a semicolon (\f[V];\f[R]) to separate items.
.TP
\f[V]-quiet\f[R]
Replays the valid history without displaying it.
Errors are displayed.
.TP
\f[V]-restore\f[R]
Resets the environment to the state at the start of the previous run of
the tool or to the last time a \f[V]/reset\f[R], \f[V]/reload\f[R], or
\f[V]/env\f[R] command was executed in the previous run.
The valid history since that point is replayed.
Use this option to restore a previous JShell session.
.RE
.TP
\f[V]/reset\f[R] [\f[I]options\f[R]]
Discards all entered snippets and restarts the session as follows:
.RS
.IP \[bu] 2
Updates the environment settings with the provided options, if any.
.IP \[bu] 2
Resets the execution state.
.IP \[bu] 2
Runs the startup scripts.
.PP
History is not replayed.
All code that was entered is lost.
.PP
Environment settings entered on the command line or provided with a
previous \f[V]/reset\f[R], \f[V]/env\f[R], or \f[V]/reload\f[R] command
are maintained unless an \f[I]option\f[R] is entered that overwrites the
setting.
.PP
The following options are valid:
.TP
\f[V]--add-modules\f[R] \f[I]module\f[R][\f[V],\f[R]\f[I]module\f[R]...]
Specifies the root modules to resolve in addition to the initial module.
.TP
\f[V]--add-exports\f[R] \f[I]source-module\f[R]\f[V]/\f[R]\f[I]package\f[R]\f[V]=\f[R]\f[I]target-module\f[R][\f[V],\f[R]\f[I]target-module\f[R]]*
Adds an export of \f[I]package\f[R] from \f[I]source-module\f[R] to
\f[I]target-module\f[R].
.TP
\f[V]--class-path\f[R] \f[I]path\f[R]
Specifies the directories and archives that are searched to locate class
files.
This option overrides the path in the \f[V]CLASSPATH\f[R] environment
variable.
If the environment variable isn\[aq]t set and this option isn\[aq]t
used, then the current directory is searched.
For Linux and macOS, use a colon (\f[V]:\f[R]) to separate items in the
path.
For Windows, use a semicolon (\f[V];\f[R]) to separate items.
.TP
\f[V]--module-path\f[R] \f[I]modulepath\f[R]
Specifies where to find application modules.
For Linux and macOS, use a colon (\f[V]:\f[R]) to separate items in the
path.
For Windows, use a semicolon (\f[V];\f[R]) to separate items.
.RE
.TP
\f[V]/save\f[R] [\f[I]options\f[R]] \f[I]file\f[R]
Saves snippets and commands to the file specified.
If no options are entered, then active snippets are saved.
.RS
.PP
The following options are valid:
.TP
{\f[I]name\f[R]|\f[I]id\f[R]|\f[I]startID\f[R]\f[V]-\f[R]\f[I]endID\f[R]} [{\f[I]name\f[R]|\f[I]id\f[R]|\f[I]startID\f[R]\f[V]-\f[R]\f[I]endID\f[R]}...]
Saves the snippets and commands identified by name, ID, or ID range.
For a range of IDs, provide the starting ID and ending ID separated with
a hyphen.
To provide a list, separate the items in the list with a space.
Use the \f[V]/list\f[R] command to see the IDs of the code snippets.
.TP
\f[V]-all\f[R]
Saves all snippets, including startup snippets and snippets that were
overwritten or failed.
.TP
\f[V]-history\f[R]
Saves the sequential history of all commands and snippets entered in the
current session.
.TP
\f[V]-start\f[R]
Saves the current startup settings.
If no startup scripts were provided, then an empty file is saved.
.RE
.TP
\f[V]/set\f[R] [\f[I]setting\f[R]]
Sets configuration information, including the external editor, startup
settings, and feedback mode.
This command is also used to create a custom feedback mode with
customized prompt, format, and truncation values.
If no setting is entered, then the current setting for the editor,
startup settings, and feedback mode are displayed.
.RS
.PP
The following values are valid for \f[V]setting\f[R]:
.TP
\f[V]editor\f[R] [\f[I]options\f[R]] [\f[I]command\f[R]]
Sets the command used to start an external editor when the
\f[V]/edit\f[R] command is entered.
The command can include command arguments separated by spaces.
If no command or options are entered, then the current setting is
displayed.
.RS
.PP
The following options are valid:
.TP
\f[V]-default\f[R]
Sets the editor to the default editor provided with JShell.
This option can\[aq]t be used if a command for starting an editor is
entered.
.TP
\f[V]-delete\f[R]
Sets the editor to the one in effect when the session started.
If used with the \f[V]-retain\f[R] option, then the retained editor
setting is deleted and the editor is set to the first of the following
environment variables found: \f[V]JSHELLEDITOR\f[R], \f[V]VISUAL\f[R],
or \f[V]EDITOR\f[R].
If none of the editor environment variables are set, then this option
sets the editor to the default editor.
.RS
.PP
This option can\[aq]t be used if a command for starting an editor is
entered.
.RE
.TP
\f[V]-retain\f[R]
Saves the editor setting across sessions.
If no other option or a command is entered, then the current setting is
saved.
.TP
\f[V]-wait\f[R]
Prompts the user to indicate when editing is complete.
Otherwise control returns to JShell when the editor exits.
Use this option if the editor being used exits immediately, for example,
when an edit window already exists.
This option is valid only when a command for starting an editor is
entered.
.RE
.TP
\f[V]feedback\f[R] [\f[I]mode\f[R]]
Sets the feedback mode used to respond to input.
If no mode is entered, then the current mode is displayed.
.RS
.PP
The following modes are valid: \f[V]concise\f[R], \f[V]normal\f[R],
\f[V]silent\f[R], \f[V]verbose\f[R], and any custom mode created with
the \f[V]/set mode\f[R] command.
.RE
.TP
\f[V]format\f[R] \f[I]mode\f[R] \f[I]field\f[R] \f[V]\[dq]\f[R]\f[I]format-string\f[R]\f[V]\[dq]\f[R] \f[I]selector\f[R]
Sets the format of the feedback provided in response to input.
If no mode is entered, then the current formats for all fields for all
feedback modes are displayed.
If only a mode is entered, then the current formats for that mode are
displayed.
If only a mode and field are entered, then the current formats for that
field are displayed.
.RS
.PP
To define a format, the following arguments are required:
.TP
\f[I]mode\f[R]
Specifies a feedback mode to which the response format is applied.
Only custom modes created with the \f[V]/set mode\f[R] command can be
modified.
.TP
\f[I]field\f[R]
Specifies a context-specific field to which the response format is
applied.
The fields are described in the online help, which is accessed from
JShell using the \f[V]/help /set format\f[R] command.
.TP
\f[V]\[dq]\f[R]\f[I]format-string\f[R]\f[V]\[dq]\f[R]
Specifies the string to use as the response format for the specified
field and selector.
The structure of the format string is described in the online help,
which is accessed from JShell using the \f[V]/help /set format\f[R]
command.
.TP
\f[I]selector\f[R]
Specifies the context in which the response format is applied.
The selectors are described in the online help, which is accessed from
JShell using the \f[V]/help /set format\f[R] command.
.RE
.TP
\f[V]mode\f[R] [\f[I]mode-name\f[R]] [\f[I]existing-mode\f[R]] [\f[I]options\f[R]]
Creates a custom feedback mode with the mode name provided.
If no mode name is entered, then the settings for all modes are
displayed, which includes the mode, prompt, format, and truncation
settings.
If the name of an existing mode is provided, then the settings from the
existing mode are copied to the mode being created.
.RS
.PP
The following options are valid:
.TP
\f[V]-command\f[R]|\f[V]-quiet\f[R]
Specifies the level of feedback displayed for commands when using the
mode.
This option is required when creating a feedback mode.
Use \f[V]-command\f[R] to show information and verification feedback for
commands.
Use \f[V]-quiet\f[R] to show only essential feedback for commands, such
as error messages.
.TP
\f[V]-delete\f[R]
Deletes the named feedback mode for this session.
The name of the mode to delete is required.
To permanently delete a retained mode, use the \f[V]-retain\f[R] option
with this option.
Predefined modes can\[aq]t be deleted.
.TP
\f[V]-retain\f[R]
Saves the named feedback mode across sessions.
The name of the mode to retain is required.
.PP
Configure the new feedback mode using the \f[V]/set prompt\f[R],
\f[V]/set format\f[R], and \f[V]/set truncation\f[R] commands.
.PP
To start using the new mode, use the \f[V]/set feedback\f[R] command.
.RE
.TP
\f[V]prompt\f[R] \f[I]mode\f[R] \f[V]\[dq]\f[R]\f[I]prompt-string\f[R]\f[V]\[dq]\f[R] \f[V]\[dq]\f[R]\f[I]continuation-prompt-string\f[R]\f[V]\[dq]\f[R]
Sets the prompts for input within JShell.
If no mode is entered, then the current prompts for all feedback modes
are displayed.
If only a mode is entered, then the current prompts for that mode are
displayed.
.RS
.PP
To define a prompt, the following arguments are required:
.TP
\f[I]mode\f[R]
Specifies the feedback mode to which the prompts are applied.
Only custom modes created with the \f[V]/set mode\f[R] command can be
modified.
.TP
\f[V]\[dq]\f[R]\f[I]prompt-string\f[R]\f[V]\[dq]\f[R]
Specifies the string to use as the prompt for the first line of input.
.TP
\f[V]\[dq]\f[R]\f[I]continuation-prompt-string\f[R]\f[V]\[dq]\f[R]
Specifies the string to use as the prompt for the additional input lines
needed to complete a snippet.
.RE
.TP
\f[V]start\f[R] [\f[V]-retain\f[R]] [\f[I]file\f[R] [\f[I]file\f[R]...]|\f[I]option\f[R]]
Sets the names of the startup scripts used when the next
\f[V]/reset\f[R], \f[V]/reload\f[R], or \f[V]/env\f[R] command is
entered.
If more than one script is entered, then the scripts are run in the
order entered.
If no scripts or options are entered, then the current startup settings
are displayed.
.RS
.PP
The scripts can be local files or one of the following predefined
scripts:
.TP
\f[V]DEFAULT\f[R]
Loads the default entries, which are commonly used as imports.
.TP
\f[V]JAVASE\f[R]
Imports all Java SE packages.
.TP
\f[V]PRINTING\f[R]
Defines \f[V]print\f[R], \f[V]println\f[R], and \f[V]printf\f[R] as
\f[V]jshell\f[R] methods for use within the tool.
.TP
\f[V]TOOLING\f[R]
Defines \f[V]javac\f[R], \f[V]jar\f[R], and other methods for running
JDK tools via their command-line interface within the \f[V]jshell\f[R]
tool.
.PP
The following options are valid:
.TP
\f[V]-default\f[R]
Sets the startup settings to the default settings.
.TP
\f[V]-none\f[R]
Specifies that no startup settings are used.
.PP
Use the \f[V]-retain\f[R] option to save the start setting across
sessions.
.RE
.TP
\f[V]truncation\f[R] \f[I]mode\f[R] \f[I]length\f[R] \f[I]selector\f[R]
Sets the maximum length of a displayed value.
If no mode is entered, then the current truncation values for all
feedback modes are displayed.
If only a mode is entered, then the current truncation values for that
mode are displayed.
.RS
.PP
To define truncation values, the following arguments are required:
.TP
\f[I]mode\f[R]
Specifies the feedback mode to which the truncation value is applied.
Only custom modes created with the \f[V]/set mode\f[R] command can be
modified.
.TP
\f[I]length\f[R]
Specifies the unsigned integer to use as the maximum length for the
specified selector.
.TP
\f[I]selector\f[R]
Specifies the context in which the truncation value is applied.
The selectors are described in the online help, which is accessed from
JShell using the \f[V]/help /set truncation\f[R] command.
.RE
.RE
.TP
\f[V]/types\f[R] [\f[I]option\f[R]]
Displays classes, interfaces, and enums that were entered.
If no option is entered, then all current active classes, interfaces,
and enums are displayed.
.RS
.PP
The following options are valid:
.TP
{\f[I]name\f[R]|\f[I]id\f[R]|\f[I]startID\f[R]\f[V]-\f[R]\f[I]endID\f[R]} [{\f[I]name\f[R]|\f[I]id\f[R]|\f[I]startID\f[R]\f[V]-\f[R]\f[I]endID\f[R]}...]
Displays information for classes, interfaces, and enums identified by
name, ID, or ID range.
For a range of IDs, provide the starting ID and ending ID separated with
a hyphen.
To provide a list, separate the items in the list with a space.
Use the \f[V]/list\f[R] command to see the IDs of the code snippets.
.TP
\f[V]-all\f[R]
Displays information for all classes, interfaces, and enums, including
those added when JShell was started, and classes, interfaces, and enums
that failed, were overwritten, or were dropped.
.TP
\f[V]-start\f[R]
Displays information for startup classes, interfaces, and enums that
were added when JShell was started.
.RE
.TP
\f[V]/vars\f[R] [\f[I]option\f[R]]
Displays the name, type, and value of variables that were entered.
If no option is entered, then all current active variables are
displayed.
.RS
.PP
The following options are valid:
.TP
{\f[I]name\f[R]|\f[I]id\f[R]|\f[I]startID\f[R]\f[V]-\f[R]\f[I]endID\f[R]} [{\f[I]name\f[R]|\f[I]id\f[R]|\f[I]startID\f[R]\f[V]-\f[R]\f[I]endID\f[R]}...]
Displays information for variables identified by name, ID, or ID range.
For a range of IDs, provide the starting ID and ending ID separated with
a hyphen.
To provide a list, separate the items in the list with a space.
Use the \f[V]/list\f[R] command to see the IDs of the code snippets.
.TP
\f[V]-all\f[R]
Displays information for all variables, including those added when
JShell was started, and variables that failed, were overwritten, or were
dropped.
.TP
\f[V]-start\f[R]
Displays information for startup variables that were added when JShell
was started.
.RE
.TP
\f[V]/?\f[R]
Same as the \f[V]/help\f[R] command.
.TP
\f[V]/!\f[R]
Reruns the last snippet.
.TP
\f[V]/\f[R]{\f[I]name\f[R]|\f[I]id\f[R]|\f[I]startID\f[R]\f[V]-\f[R]\f[I]endID\f[R]} [{\f[I]name\f[R]|\f[I]id\f[R]|\f[I]startID\f[R]\f[V]-\f[R]\f[I]endID\f[R]}...]
Reruns the snippets identified by ID, range of IDs, or name.
For a range of IDs, provide the starting ID and ending ID separated with
a hyphen.
To provide a list, separate the items in the list with a space.
The first item in the list must be an ID or ID range.
Use the \f[V]/list\f[R] command to see the IDs of the code snippets.
.TP
\f[V]/-\f[R]\f[I]n\f[R]
Reruns the -\f[I]n\f[R]th previous snippet.
For example, if 15 code snippets were entered, then \f[V]/-4\f[R] runs
the 11th snippet.
Commands aren\[aq]t included in the count.
.SH INPUT SHORTCUTS
.PP
The following shortcuts are available for entering commands and snippets
in JShell.
.SS Tab completion
.TP
\f[B]<tab>\f[R]
When entering snippets, commands, subcommands, command arguments, or
command options, use the Tab key to automatically complete the item.
If the item can\[aq]t be determined from what was entered, then possible
options are provided.
.RS
.PP
When entering a method call, use the Tab key after the method call\[aq]s
opening parenthesis to see the parameters for the method.
If the method has more than one signature, then all signatures are
displayed.
Pressing the Tab key a second time displays the description of the
method and the parameters for the first signature.
Continue pressing the Tab key for a description of any additional
signatures.
.RE
.TP
\f[B]Shift+<Tab> V\f[R]
After entering a complete expression, use this key sequence to convert
the expression to a variable declaration of a type determined by the
type of the expression.
.TP
\f[B]Shift+<Tab> M\f[R]
After entering a complete expression or statement, use this key sequence
to convert the expression or statement to a method declaration.
If an expression is entered, the return type is based on the type of the
expression.
.TP
\f[B]Shift+<Tab> I\f[R]
When an identifier is entered that can\[aq]t be resolved, use this key
sequence to show possible imports that resolve the identifier based on
the content of the specified class path.
.SS Command abbreviations
.PP
An abbreviation of a command is accepted if the abbreviation uniquely
identifies a command.
For example, \f[V]/l\f[R] is recognized as the \f[V]/list\f[R] command.
However, \f[V]/s\f[R] isn\[aq]t a valid abbreviation because it
can\[aq]t be determined if the \f[V]/set\f[R] or \f[V]/save\f[R] command
is meant.
Use \f[V]/se\f[R] for the \f[V]/set\f[R] command or \f[V]/sa\f[R] for
the \f[V]/save\f[R] command.
.PP
Abbreviations are also accepted for subcommands, command arguments, and
command options.
For example, use \f[V]/m -a\f[R] to display all methods.
.SS History navigation
.PP
A history of what was entered is maintained across sessions.
Use the up and down arrows to scroll through commands and snippets from
the current and past sessions.
Use the Ctrl key with the up and down arrows to skip all but the first
line of multiline snippets.
.SS History search
.PP
Use the Ctrl+R key combination to search the history for the string
entered.
The prompt changes to show the string and the match.
Ctrl+R searches backwards from the current location in the history
through earlier entries.
Ctrl+S searches forward from the current location in the history though
later entries.
.SH INPUT EDITING
.PP
The editing capabilities of JShell are similar to that of other common
shells.
Keyboard keys and key combinations provide line editing shortcuts.
The Ctrl key and Meta key are used in key combinations.
If your keyboard doesn\[aq]t have a Meta key, then the Alt key is often
mapped to provide Meta key functionality.
.PP
Line Editing Shortcuts
.TS
tab(@);
l l.
T{
Key or Key Combination
T}@T{
Action
T}
_
T{
Return
T}@T{
Enter the current line.
T}
T{
Left arrow
T}@T{
Move the cursor to the left one character.
T}
T{
Right arrow
T}@T{
Move the cursor to the right one character.
T}
T{
Ctrl+A
T}@T{
Move the cursor to the beginning of the line.
T}
T{
Ctrl+E
T}@T{
Move the cursor to the end of the line.
T}
T{
Meta+B
T}@T{
Move the cursor to the left one word.
T}
T{
Meta+F
T}@T{
Move the cursor to the right one word.
T}
T{
Delete
T}@T{
Delete the character under the cursor.
T}
T{
Backspace
T}@T{
Delete the character before the cursor.
T}
T{
Ctrl+K
T}@T{
Delete the text from the cursor to the end of the line.
T}
T{
Meta+D
T}@T{
Delete the text from the cursor to the end of the word.
T}
T{
Ctrl+W
T}@T{
Delete the text from the cursor to the previous white space.
T}
T{
Ctrl+Y
T}@T{
Paste the most recently deleted text into the line.
T}
T{
Meta+Y
T}@T{
After Ctrl+Y, press to cycle through the previously deleted text.
T}
.TE
.SH EXAMPLE OF STARTING AND STOPPING A JSHELL SESSION
.PP
JShell is provided with the JDK.
To start a session, enter \f[V]jshell\f[R] on the command line.
A welcome message is printed, and a prompt for entering commands and
snippets is provided.
.IP
.nf
\f[CB]
% jshell
|  Welcome to JShell -- Version 9
|  For an introduction type: /help intro

jshell>
\f[R]
.fi
.PP
To see which snippets were automatically loaded when JShell started, use
the \f[V]/list -start\f[R] command.
The default startup snippets are import statements for common packages.
The ID for each snippet begins with the letter \f[I]s\f[R], which
indicates it\[aq]s a startup snippet.
.IP
.nf
\f[CB]
jshell> /list -start

  s1 : import java.io.*;
  s2 : import java.math.*;
  s3 : import java.net.*;
  s4 : import java.nio.file.*;
  s5 : import java.util.*;
  s6 : import java.util.concurrent.*;
  s7 : import java.util.function.*;
  s8 : import java.util.prefs.*;
  s9 : import java.util.regex.*;
 s10 : import java.util.stream.*;

jshell>
\f[R]
.fi
.PP
To end the session, use the \f[V]/exit\f[R] command.
.IP
.nf
\f[CB]
jshell> /exit
|  Goodbye

%
\f[R]
.fi
.SH EXAMPLE OF ENTERING SNIPPETS
.PP
Snippets are Java statements, variable definitions, method definitions,
class definitions, import statements, and expressions.
Terminating semicolons are automatically added to the end of a completed
snippet if they\[aq]re missing.
.PP
The following example shows two variables and a method being defined,
and the method being run.
Note that a scratch variable is automatically created to hold the result
because no variable was provided.
.IP
.nf
\f[CB]
jshell> int a=4
a ==> 4

jshell> int b=8
b ==> 8

jshell> int square(int i1) {
   ...> return i1 * i1;
   ...> }
|  created method square(int)

jshell> square(b)
$5 ==> 64
\f[R]
.fi
.SH EXAMPLE OF CHANGING SNIPPETS
.PP
Change the definition of a variable, method, or class by entering it
again.
.PP
The following examples shows a method being defined and the method run:
.IP
.nf
\f[CB]
jshell> String grade(int testScore) {
   ...>     if (testScore >= 90) {
   ...>         return \[dq]Pass\[dq];
   ...>     }
   ...>     return \[dq]Fail\[dq];
   ...> }
|  created method grade(int)

jshell> grade(88)
$3 ==> \[dq]Fail\[dq]
\f[R]
.fi
.PP
To change the method \f[V]grade\f[R] to allow more students to pass,
enter the method definition again and change the pass score to
\f[V]80\f[R].
Use the up arrow key to retrieve the previous entries to avoid having to
reenter them and make the change in the \f[V]if\f[R] statement.
The following example shows the new definition and reruns the method to
show the new result:
.IP
.nf
\f[CB]
jshell> String grade(int testScore) {
   ...>     if (testScore >= 80) {
   ...>         return \[dq]Pass\[dq];
   ...>     }
   ...>     return \[dq]Fail\[dq];
   ...> }
|  modified method grade(int)

jshell> grade(88)
$5 ==> \[dq]Pass\[dq]
\f[R]
.fi
.PP
For snippets that are more than a few lines long, or to make more than a
few changes, use the \f[V]/edit\f[R] command to open the snippet in an
editor.
After the changes are complete, close the edit window to return control
to the JShell session.
The following example shows the command and the feedback provided when
the edit window is closed.
The \f[V]/list\f[R] command is used to show that the pass score was
changed to \f[V]85\f[R].
.IP
.nf
\f[CB]
jshell> /edit grade
|  modified method grade(int)
jshell> /list grade

   6 : String grade(int testScore) {
           if (testScore >= 85) {
               return \[dq]Pass\[dq];
           }
           return \[dq]Fail\[dq];
       }
\f[R]
.fi
.SH EXAMPLE OF CREATING A CUSTOM FEEDBACK MODE
.PP
The feedback mode determines the prompt that\[aq]s displayed, the
feedback messages that are provided as snippets are entered, and the
maximum length of a displayed value.
Predefined feedback modes are provided.
Commands for creating custom feedback modes are also provided.
.PP
Use the \f[V]/set mode\f[R] command to create a new feedback mode.
In the following example, the new mode \f[V]mymode\f[R], is based on the
predefined feedback mode, \f[V]normal\f[R], and verifying command
feedback is displayed:
.IP
.nf
\f[CB]
jshell> /set mode mymode normal -command
|  Created new feedback mode: mymode
\f[R]
.fi
.PP
Because the new mode is based on the \f[V]normal\f[R] mode, the prompts
are the same.
The following example shows how to see what prompts are used and then
changes the prompts to custom strings.
The first string represents the standard JShell prompt.
The second string represents the prompt for additional lines in
multiline snippets.
.IP
.nf
\f[CB]
jshell> /set prompt mymode
|  /set prompt mymode \[dq]\[rs]njshell> \[dq] \[dq]   ...> \[dq]

jshell> /set prompt mymode \[dq]\[rs]nprompt$ \[dq] \[dq]   continue$ \[dq]
\f[R]
.fi
.PP
The maximum length of a displayed value is controlled by the truncation
setting.
Different types of values can have different lengths.
The following example sets an overall truncation value of 72, and a
truncation value of 500 for variable value expressions:
.IP
.nf
\f[CB]
jshell> /set truncation mymode 72

jshell> /set truncation mymode 500 varvalue
\f[R]
.fi
.PP
The feedback displayed after snippets are entered is controlled by the
format setting and is based on the type of snippet entered and the
action taken for that snippet.
In the predefined mode \f[V]normal\f[R], the string \f[V]created\f[R] is
displayed when a method is created.
The following example shows how to change that string to
\f[V]defined\f[R]:
.IP
.nf
\f[CB]
jshell> /set format mymode action \[dq]defined\[dq] added-primary
\f[R]
.fi
.PP
Use the \f[V]/set feedback\f[R] command to start using the feedback mode
that was just created.
The following example shows the custom mode in use:
.IP
.nf
\f[CB]
jshell> /set feedback mymode
|  Feedback mode: mymode

prompt$ int square (int num1){
   continue$ return num1*num1;
   continue$ }
|  defined method square(int)

prompt$
\f[R]
.fi
