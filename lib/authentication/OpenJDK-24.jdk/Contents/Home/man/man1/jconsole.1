.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JCONSOLE" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jconsole - start a graphical console to monitor and manage Java
applications
.SH SYNOPSIS
.PP
\f[V]jconsole\f[R] [\f[V]-interval=\f[R]\f[I]n\f[R]] [\f[V]-notile\f[R]]
[\f[V]-plugin\f[R] \f[I]path\f[R]] [\f[V]-version\f[R]]
[\f[I]connection\f[R] ...
] [\f[V]-J\f[R]\f[I]input_arguments\f[R]]
.PP
\f[V]jconsole\f[R] \f[V]-help\f[R]
.SH OPTIONS
.TP
\f[V]-interval\f[R]
Sets the update interval to \f[V]n\f[R] seconds (default is 4 seconds).
.TP
\f[V]-notile\f[R]
Doesn\[aq]t tile the windows for two or more connections.
.TP
\f[V]-pluginpath\f[R] \f[I]path\f[R]
Specifies the path that \f[V]jconsole\f[R] uses to look up plug-ins.
The plug-in \f[I]path\f[R] should contain a provider-configuration file
named \f[V]META-INF/services/com.sun.tools.jconsole.JConsolePlugin\f[R]
that contains one line for each plug-in.
The line specifies the fully qualified class name of the class
implementing the \f[V]com.sun.tools.jconsole.JConsolePlugin\f[R] class.
.TP
\f[V]-version\f[R]
Prints the program version.
.TP
\f[I]connection\f[R] = \f[I]pid\f[R] | \f[I]host\f[R]\f[V]:\f[R]\f[I]port\f[R] | \f[I]jmxURL\f[R]
A connection is described by either \f[I]pid\f[R],
\f[I]host\f[R]\f[V]:\f[R]\f[I]port\f[R] or \f[I]jmxURL\f[R].
.RS
.IP \[bu] 2
The \f[I]pid\f[R] value is the process ID of a target process.
The JVM must be running with the same user ID as the user ID running the
\f[V]jconsole\f[R] command.
.IP \[bu] 2
The \f[I]host\f[R]\f[V]:\f[R]\f[I]port\f[R] values are the name of the
host system on which the JVM is running, and the port number specified
by the system property \f[V]com.sun.management.jmxremote.port\f[R] when
the JVM was started.
.IP \[bu] 2
The \f[I]jmxUrl\f[R] value is the address of the JMX agent to be
connected to as described in JMXServiceURL.
.RE
.TP
\f[V]-J\f[R]\f[I]input_arguments\f[R]
Passes \f[I]input_arguments\f[R] to the JVM on which the
\f[V]jconsole\f[R] command is run.
.TP
\f[V]-help\f[R] or \f[V]--help\f[R]
Displays the help message for the command.
.SH DESCRIPTION
.PP
The \f[V]jconsole\f[R] command starts a graphical console tool that lets
you monitor and manage Java applications and virtual machines on a local
or remote machine.
.PP
On Windows, the \f[V]jconsole\f[R] command doesn\[aq]t associate with a
console window.
It does, however, display a dialog box with error information when the
\f[V]jconsole\f[R] command fails.
