.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JSTACK" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jstack - print Java stack traces of Java threads for a specified Java
process
.SH SYNOPSIS
.PP
\f[B]Note:\f[R] This command is experimental and unsupported.
.PP
\f[V]jstack\f[R] [\f[I]options\f[R]] \f[I]pid\f[R]
.TP
\f[I]options\f[R]
This represents the \f[V]jstack\f[R] command-line options.
See \f[B]Options for the jstack Command\f[R].
.TP
\f[I]pid\f[R]
The process ID for which the stack trace is printed.
The process must be a Java process.
To get a list of Java processes running on a machine, use either the
\f[V]ps\f[R] command or, if the JVM processes are not running in a
separate docker instance, the \f[B]jps\f[R] command.
.SH DESCRIPTION
.PP
The \f[V]jstack\f[R] command prints Java stack traces of Java threads
for a specified Java process.
For each Java frame, the full class name, method name, byte code index
(BCI), and line number, when available, are printed.
C++ mangled names aren\[aq]t demangled.
To demangle C++ names, the output of this command can be piped to
\f[V]c++filt\f[R].
When the specified process is running on a 64-bit JVM, you might need to
specify the \f[V]-J-d64\f[R] option, for example:
\f[V]jstack -J-d64\f[R] \f[I]pid\f[R].
.PP
\f[B]Note:\f[R]
.PP
This command is unsupported and might not be available in future
releases of the JDK.
In Windows Systems where the \f[V]dbgeng.dll\f[R] file isn\[aq]t
present, the Debugging Tools for Windows must be installed so that these
tools work.
The \f[V]PATH\f[R] environment variable needs to contain the location of
the \f[V]jvm.dll\f[R] that is used by the target process, or the
location from which the core dump file was produced.
.SH OPTIONS FOR THE JSTACK COMMAND
.TP
\f[V]-l\f[R]
The long listing option prints additional information about locks.
.TP
\f[V]-h\f[R] or \f[V]-help\f[R]
Prints a help message.
