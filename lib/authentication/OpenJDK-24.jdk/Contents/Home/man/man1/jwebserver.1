.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JWEBSERVER" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jwebserver - launch the Java Simple Web Server
.SH SYNOPSIS
.PP
\f[V]jwebserver\f[R] [\f[I]options\f[R]]
.TP
\f[I]options\f[R]
Command-line options.
For a detailed description of the options, see \f[B]Options\f[R].
.SH DESCRIPTION
.PP
The \f[V]jwebserver\f[R] tool provides a minimal HTTP server, designed
to be used for prototyping, testing, and debugging.
It serves a single directory hierarchy, and only serves static files.
Only HTTP/1.1 is supported; HTTP/2 and HTTPS are not supported.
.PP
Only idempotent HEAD and GET requests are served.
Any other requests receive a \f[V]501 - Not Implemented\f[R] or a
\f[V]405 - Not Allowed\f[R] response.
GET requests are mapped to the directory being served, as follows:
.IP \[bu] 2
If the requested resource is a file, its content is served.
.IP \[bu] 2
If the requested resource is a directory that contains an index file,
the content of the index file is served.
.IP \[bu] 2
Otherwise, the names of all files and subdirectories of the directory
are listed.
Symbolic links and hidden files are not listed or served.
.PP
MIME types are configured automatically, using the built-in table.
For example, \f[V].html\f[R] files are served as \f[V]text/html\f[R] and
\f[V].java\f[R] files are served as \f[V]text/plain\f[R].
.PP
\f[V]jwebserver\f[R] is located in the jdk.httpserver module, and can
alternatively be started with \f[V]java -m jdk.httpserver\f[R].
It is based on the web server implementation in the
\f[V]com.sun.net.httpserver\f[R] package.
The \f[V]com.sun.net.httpserver.SimpleFileServer\f[R] class provides a
programmatic way to retrieve the server and its components for reuse and
extension.
.SH USAGE
.IP
.nf
\f[CB]
jwebserver [-b bind address] [-p port] [-d directory]
           [-o none|info|verbose] [-h to show options]
           [-version to show version information]
\f[R]
.fi
.SH OPTIONS
.TP
\f[V]-h\f[R] or \f[V]-?\f[R] or \f[V]--help\f[R]
Prints the help message and exits.
.TP
\f[V]-b\f[R] \f[I]addr\f[R] or \f[V]--bind-address\f[R] \f[I]addr\f[R]
Specifies the address to bind to.
Default: 127.0.0.1 or ::1 (loopback).
For all interfaces use \f[V]-b 0.0.0.0\f[R] or \f[V]-b ::\f[R].
.TP
\f[V]-d\f[R] \f[I]dir\f[R] or \f[V]--directory\f[R] \f[I]dir\f[R]
Specifies the directory to serve.
Default: current directory.
.TP
\f[V]-o\f[R] \f[I]level\f[R] or \f[V]--output\f[R] \f[I]level\f[R]
Specifies the output format.
\f[V]none\f[R] | \f[V]info\f[R] | \f[V]verbose\f[R].
Default: \f[V]info\f[R].
.TP
\f[V]-p\f[R] \f[I]port\f[R] or \f[V]--port\f[R] \f[I]port\f[R]
Specifies the port to listen on.
Default: 8000.
.TP
\f[V]-version\f[R] or \f[V]--version\f[R]
Prints the version information and exits.
.PP
To stop the server, press \f[V]Ctrl + C\f[R].
.SH STARTING THE SERVER
.PP
The following command starts the Simple Web Server:
.IP
.nf
\f[CB]
$ jwebserver
\f[R]
.fi
.PP
If startup is successful, the server prints a message to
\f[V]System.out\f[R] listing the local address and the absolute path of
the directory being served.
For example:
.IP
.nf
\f[CB]
$ jwebserver
Binding to loopback by default. For all interfaces use \[dq]-b 0.0.0.0\[dq] or \[dq]-b ::\[dq].
Serving /cwd and subdirectories on 127.0.0.1 port 8000
URL http://127.0.0.1:8000/
\f[R]
.fi
.SH CONFIGURATION
.PP
By default, the server runs in the foreground and binds to the loopback
address and port 8000.
This can be changed with the \f[V]-b\f[R] and \f[V]-p\f[R] options.
For example, to bind the Simple Web Server to all interfaces, use:
.IP
.nf
\f[CB]
$ jwebserver -b 0.0.0.0
Serving /cwd and subdirectories on 0.0.0.0 (all interfaces) port 8000
URL http://123.456.7.891:8000/
\f[R]
.fi
.PP
Note that this makes the web server accessible to all hosts on the
network.
\f[I]Do not do this unless you are sure the server cannot leak any
sensitive information.\f[R]
.PP
As another example, use the following command to run on port 9000:
.IP
.nf
\f[CB]
$ jwebserver -p 9000
\f[R]
.fi
.PP
By default, the files of the current directory are served.
A different directory can be specified with the \f[V]-d\f[R] option.
.PP
By default, every request is logged on the console.
The output looks like this:
.IP
.nf
\f[CB]
127.0.0.1 - - [10/Feb/2021:14:34:11 +0000] \[dq]GET /some/subdirectory/ HTTP/1.1\[dq] 200 -
\f[R]
.fi
.PP
Logging output can be changed with the \f[V]-o\f[R] option.
The default setting is \f[V]info\f[R].
The \f[V]verbose\f[R] setting additionally includes the request and
response headers as well as the absolute path of the requested resource.
.SH STOPPING THE SERVER
.PP
Once started successfully, the server runs until it is stopped.
On Unix platforms, the server can be stopped by sending it a
\f[V]SIGINT\f[R] signal (\f[V]Ctrl+C\f[R] in a terminal window).
.SH HELP OPTION
.PP
The \f[V]-h\f[R] option displays a help message describing the usage and
the options of the \f[V]jwebserver\f[R].
