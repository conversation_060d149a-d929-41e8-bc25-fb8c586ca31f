.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JSTATD" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jstatd - monitor the creation and termination of instrumented Java
HotSpot VMs
.SH SYNOPSIS
.PP
\f[B]WARNING:\f[R] This command is experimental, unsupported, and
deprecated.
It will be removed in a future release.
.PP
\f[V]jstatd\f[R] [\f[I]options\f[R]]
.TP
\f[I]options\f[R]
This represents the \f[V]jstatd\f[R] command-line options.
See \f[B]Options for the jstatd Command\f[R].
.SH DESCRIPTION
.PP
The \f[V]jstatd\f[R] command is an RMI server application that monitors
for the creation and termination of instrumented Java HotSpot VMs and
provides an interface to enable remote monitoring tools, \f[V]jstat\f[R]
and \f[V]jps\f[R], to attach to JVMs that are running on the local host
and collect information about the JVM process.
.PP
The \f[V]jstatd\f[R] server requires an RMI registry on the local host.
The \f[V]jstatd\f[R] server attempts to attach to the RMI registry on
the default port, or on the port you specify with the \f[V]-p\f[R]
\f[V]port\f[R] option.
If an RMI registry is not found, then one is created within the
\f[V]jstatd\f[R] application that\[aq]s bound to the port that\[aq]s
indicated by the \f[V]-p\f[R] \f[V]port\f[R] option or to the default
RMI registry port when the \f[V]-p\f[R] \f[V]port\f[R] option is
omitted.
You can stop the creation of an internal RMI registry by specifying the
\f[V]-nr\f[R] option.
.SH OPTIONS FOR THE JSTATD COMMAND
.TP
\f[V]-nr\f[R]
This option does not attempt to create an internal RMI registry within
the \f[V]jstatd\f[R] process when an existing RMI registry isn\[aq]t
found.
.TP
\f[V]-p\f[R] \f[I]port\f[R]
This option sets the port number where the RMI registry is expected to
be found, or when not found, created if the \f[V]-nr\f[R] option
isn\[aq]t specified.
.TP
\f[V]-r\f[R] \f[I]rmiport\f[R]
This option sets the port number to which the RMI connector is bound.
If not specified a random available port is used.
.TP
\f[V]-n\f[R] \f[I]rminame\f[R]
This option sets the name to which the remote RMI object is bound in the
RMI registry.
The default name is \f[V]JStatRemoteHost\f[R].
If multiple \f[V]jstatd\f[R] servers are started on the same host, then
the name of the exported RMI object for each server can be made unique
by specifying this option.
However, doing so requires that the unique server name be included in
the monitoring client\[aq]s \f[V]hostid\f[R] and \f[V]vmid\f[R] strings.
.TP
\f[V]-J\f[R]\f[I]option\f[R]
This option passes a Java \f[V]option\f[R] to the JVM, where the option
is one of those described on the reference page for the Java application
launcher.
For example, \f[V]-J-Xms48m\f[R] sets the startup memory to 48 MB.
See \f[B]java\f[R].
.SH SECURITY
.PP
The \f[V]jstatd\f[R] server can monitor only JVMs for which it has the
appropriate native access permissions.
Therefore, the \f[V]jstatd\f[R] process must be running with the same
user credentials as the target JVMs.
Some user credentials, such as the root user in Linux and macOS
operating systems, have permission to access the instrumentation
exported by any JVM on the system.
A \f[V]jstatd\f[R] process running with such credentials can monitor any
JVM on the system, but introduces additional security concerns.
.PP
The \f[V]jstatd\f[R] server doesn\[aq]t provide any authentication of
remote clients.
Therefore, running a \f[V]jstatd\f[R] server process exposes the
instrumentation export by all JVMs for which the \f[V]jstatd\f[R]
process has access permissions to any user on the network.
This exposure might be undesirable in your environment, and therefore,
local security policies should be considered before you start the
\f[V]jstatd\f[R] process, particularly in production environments or on
networks that aren\[aq]t secure.
.PP
For security purposes, the \f[V]jstatd\f[R] server uses an RMI
ObjectInputFilter to allow only essential classes to be deserialized.
.PP
If your security concerns can\[aq]t be addressed, then the safest action
is to not run the \f[V]jstatd\f[R] server and use the \f[V]jstat\f[R]
and \f[V]jps\f[R] tools locally.
However, when using \f[V]jps\f[R] to get a list of instrumented JVMs,
the list will not include any JVMs running in docker containers.
.SH REMOTE INTERFACE
.PP
The interface exported by the \f[V]jstatd\f[R] process is proprietary
and guaranteed to change.
Users and developers are discouraged from writing to this interface.
.SH EXAMPLES
.PP
The following are examples of the \f[V]jstatd\f[R] command.
The \f[V]jstatd\f[R] scripts automatically start the server in the
background.
.SH INTERNAL RMI REGISTRY
.PP
This example shows how to start a \f[V]jstatd\f[R] session with an
internal RMI registry.
This example assumes that no other server is bound to the default RMI
registry port (port \f[V]1099\f[R]).
.RS
.PP
\f[V]jstatd\f[R]
.RE
.SH EXTERNAL RMI REGISTRY
.PP
This example starts a \f[V]jstatd\f[R] session with an external RMI
registry.
.IP
.nf
\f[CB]
rmiregistry&
jstatd
\f[R]
.fi
.PP
This example starts a \f[V]jstatd\f[R] session with an external RMI
registry server on port \f[V]2020\f[R].
.IP
.nf
\f[CB]
jrmiregistry 2020&
jstatd -p 2020
\f[R]
.fi
.PP
This example starts a \f[V]jstatd\f[R] session with an external RMI
registry server on port \f[V]2020\f[R] and JMX connector bound to port
\f[V]2021\f[R].
.IP
.nf
\f[CB]
jrmiregistry 2020&
jstatd -p 2020 -r 2021
\f[R]
.fi
.PP
This example starts a \f[V]jstatd\f[R] session with an external RMI
registry on port 2020 that\[aq]s bound to
\f[V]AlternateJstatdServerName\f[R].
.IP
.nf
\f[CB]
rmiregistry 2020&
jstatd -p 2020 -n AlternateJstatdServerName
\f[R]
.fi
.SH STOP THE CREATION OF AN IN-PROCESS RMI REGISTRY
.PP
This example starts a \f[V]jstatd\f[R] session that doesn\[aq]t create
an RMI registry when one isn\[aq]t found.
This example assumes an RMI registry is already running.
If an RMI registry isn\[aq]t running, then an error message is
displayed.
.RS
.PP
\f[V]jstatd -nr\f[R]
.RE
.SH ENABLE RMI LOGGING
.PP
This example starts a \f[V]jstatd\f[R] session with RMI logging
capabilities enabled.
This technique is useful as a troubleshooting aid or for monitoring
server activities.
.RS
.PP
\f[V]jstatd -J-Djava.rmi.server.logCalls=true\f[R]
.RE
