.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JPACKAGE" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jpackage - tool for packaging self-contained Java applications.
.SH SYNOPSIS
.PP
\f[V]jpackage\f[R] [\f[I]options\f[R]]
.TP
\f[I]options\f[R]
Command-line options separated by spaces.
See \f[B]jpackage Options\f[R].
.SH DESCRIPTION
.PP
The \f[V]jpackage\f[R] tool will take as input a Java application and a
Java run-time image, and produce a Java application image that includes
all the necessary dependencies.
It will be able to produce a native package in a platform-specific
format, such as an exe on Windows or a dmg on macOS.
Each format must be built on the platform it runs on, there is no
cross-platform support.
The tool will have options that allow packaged applications to be
customized in various ways.
.SH JPACKAGE OPTIONS
.SS Generic Options:
.TP
\f[V]\[at]\f[R]\f[I]filename\f[R]
Read options from a file.
.RS
.PP
This option can be used multiple times.
.RE
.TP
\f[V]--type\f[R] or \f[V]-t\f[R] \f[I]type\f[R]
The type of package to create
.RS
.PP
Valid values are: {\[dq]app-image\[dq], \[dq]exe\[dq], \[dq]msi\[dq],
\[dq]rpm\[dq], \[dq]deb\[dq], \[dq]pkg\[dq], \[dq]dmg\[dq]}
.PP
If this option is not specified a platform dependent default type will
be created.
.RE
.TP
\f[V]--app-version\f[R] \f[I]version\f[R]
Version of the application and/or package
.TP
\f[V]--copyright\f[R] \f[I]copyright\f[R]
Copyright for the application
.TP
\f[V]--description\f[R] \f[I]description\f[R]
Description of the application
.TP
\f[V]--help\f[R] or \f[V]-h\f[R]
Print the usage text with a list and description of each valid option
for the current platform to the output stream, and exit.
.TP
\f[V]--icon\f[R] \f[I]path\f[R]
Path of the icon of the application package
.RS
.PP
(absolute path or relative to the current directory)
.RE
.TP
\f[V]--name\f[R] or \f[V]-n\f[R] \f[I]name\f[R]
Name of the application and/or package
.TP
\f[V]--dest\f[R] or \f[V]-d\f[R] \f[I]destination\f[R]
Path where generated output file is placed
.RS
.PP
(absolute path or relative to the current directory).
.PP
Defaults to the current working directory.
.RE
.TP
\f[V]--resource-dir\f[R] \f[I]path\f[R]
Path to override jpackage resources
.RS
.PP
(absolute path or relative to the current directory)
.PP
Icons, template files, and other resources of jpackage can be
over-ridden by adding replacement resources to this directory.
.RE
.TP
\f[V]--temp\f[R] \f[I]directory\f[R]
Path of a new or empty directory used to create temporary files
.RS
.PP
(absolute path or relative to the current directory)
.PP
If specified, the temp dir will not be removed upon the task completion
and must be removed manually.
.PP
If not specified, a temporary directory will be created and removed upon
the task completion.
.RE
.TP
\f[V]--vendor\f[R] \f[I]vendor\f[R]
Vendor of the application
.TP
\f[V]--verbose\f[R]
Enables verbose output.
.TP
\f[V]--version\f[R]
Print the product version to the output stream and exit.
.SS Options for creating the runtime image:
.TP
\f[V]--add-modules\f[R] \f[I]module-name\f[R] [\f[V],\f[R]\f[I]module-name\f[R]...]
A comma (\[dq],\[dq]) separated list of modules to add
.RS
.PP
This module list, along with the main module (if specified) will be
passed to jlink as the --add-module argument.
If not specified, either just the main module (if --module is
specified), or the default set of modules (if --main-jar is specified)
are used.
.PP
This option can be used multiple times.
.RE
.TP
\f[V]--module-path\f[R] or \f[V]-p\f[R] \f[I]module-path\f[R] [\f[V],\f[R]\f[I]module-path\f[R]...]
A File.pathSeparator separated list of paths
.RS
.PP
Each path is either a directory of modules or the path to a modular jar,
and is absolute or relative to the current directory.
.PP
This option can be used multiple times.
.RE
.TP
\f[V]--jlink-options\f[R] \f[I]options\f[R]
A space separated list of options to pass to jlink
.RS
.PP
If not specified, defaults to \[dq]--strip-native-commands --strip-debug
--no-man-pages --no-header-files\[dq]
.PP
This option can be used multiple times.
.RE
.TP
\f[V]--runtime-image\f[R] \f[I]directory\f[R]
Path of the predefined runtime image that will be copied into the
application image
.RS
.PP
(absolute path or relative to the current directory)
.PP
If --runtime-image is not specified, jpackage will run jlink to create
the runtime image using options specified by --jlink-options.
.RE
.SS Options for creating the application image:
.TP
\f[V]--input\f[R] or \f[V]-i\f[R] \f[I]directory\f[R]
Path of the input directory that contains the files to be packaged
.RS
.PP
(absolute path or relative to the current directory)
.PP
All files in the input directory will be packaged into the application
image.
.RE
.TP
\f[V]--app-content\f[R] \f[I]additional-content\f[R] [\f[V],\f[R]\f[I]additional-content\f[R]...]
A comma separated list of paths to files and/or directories to add to
the application payload.
.RS
.PP
This option can be used more than once.
.RE
.SS Options for creating the application launcher(s):
.TP
\f[V]--add-launcher\f[R] \f[I]name\f[R]=\f[I]path\f[R]
Name of launcher, and a path to a Properties file that contains a list
of key, value pairs
.RS
.PP
(absolute path or relative to the current directory)
.PP
The keys \[dq]module\[dq], \[dq]main-jar\[dq], \[dq]main-class\[dq],
\[dq]description\[dq], \[dq]arguments\[dq], \[dq]java-options\[dq],
\[dq]icon\[dq], \[dq]launcher-as-service\[dq], \[dq]win-console\[dq],
\[dq]win-shortcut\[dq], \[dq]win-menu\[dq], and \[dq]linux-shortcut\[dq]
can be used.
.PP
These options are added to, or used to overwrite, the original command
line options to build an additional alternative launcher.
The main application launcher will be built from the command line
options.
Additional alternative launchers can be built using this option, and
this option can be used multiple times to build multiple additional
launchers.
.RE
.TP
\f[V]--arguments\f[R] \f[I]arguments\f[R]
Command line arguments to pass to the main class if no command line
arguments are given to the launcher
.RS
.PP
This option can be used multiple times.
.RE
.TP
\f[V]--java-options\f[R] \f[I]options\f[R]
Options to pass to the Java runtime
.RS
.PP
This option can be used multiple times.
.RE
.TP
\f[V]--main-class\f[R] \f[I]class-name\f[R]
Qualified name of the application main class to execute
.RS
.PP
This option can only be used if --main-jar is specified.
.RE
.TP
\f[V]--main-jar\f[R] \f[I]main-jar\f[R]
The main JAR of the application; containing the main class (specified as
a path relative to the input path)
.RS
.PP
Either --module or --main-jar option can be specified but not both.
.RE
.TP
\f[V]--module\f[R] or \f[V]-m\f[R] \f[I]module-name\f[R][/\f[I]main-class\f[R]]
The main module (and optionally main class) of the application
.RS
.PP
This module must be located on the module path.
.PP
When this option is specified, the main module will be linked in the
Java runtime image.
Either --module or --main-jar option can be specified but not both.
.RE
.SS Platform dependent options for creating the application launcher:
.SS Windows platform options (available only when running on Windows):
.TP
\f[V]--win-console\f[R]
Creates a console launcher for the application, should be specified for
application which requires console interactions
.SS macOS platform options (available only when running on macOS):
.TP
\f[V]--mac-package-identifier\f[R] \f[I]identifier\f[R]
An identifier that uniquely identifies the application for macOS
.RS
.PP
Defaults to the main class name.
.PP
May only use alphanumeric (A-Z,a-z,0-9), hyphen (-), and period (.)
characters.
.RE
.TP
\f[V]--mac-package-name\f[R] \f[I]name\f[R]
Name of the application as it appears in the Menu Bar
.RS
.PP
This can be different from the application name.
.PP
This name must be less than 16 characters long and be suitable for
displaying in the menu bar and the application Info window.
Defaults to the application name.
.RE
.TP
\f[V]--mac-package-signing-prefix\f[R] \f[I]prefix\f[R]
When signing the application package, this value is prefixed to all
components that need to be signed that don\[aq]t have an existing
package identifier.
.TP
\f[V]--mac-sign\f[R]
Request that the package or the predefined application image be signed.
.TP
\f[V]--mac-signing-keychain\f[R] \f[I]keychain-name\f[R]
Name of the keychain to search for the signing identity
.RS
.PP
If not specified, the standard keychains are used.
.RE
.TP
\f[V]--mac-signing-key-user-name\f[R] \f[I]name\f[R]
Team or user name portion in Apple signing identities
.TP
\f[V]--mac-app-store\f[R]
Indicates that the jpackage output is intended for the Mac App Store.
.TP
\f[V]--mac-entitlements\f[R] \f[I]path\f[R]
Path to file containing entitlements to use when signing executables and
libraries in the bundle
.TP
\f[V]--mac-app-category\f[R] \f[I]category\f[R]
String used to construct LSApplicationCategoryType in application plist
.RS
.PP
The default value is \[dq]utilities\[dq].
.RE
.SS Options for creating the application package:
.TP
\f[V]--about-url\f[R] \f[I]url\f[R]
URL of the application\[aq]s home page
.TP
\f[V]--app-image\f[R] \f[I]directory\f[R]
Location of the predefined application image that is used to build an
installable package (on all platforms) or to be signed (on macOS)
.RS
.PP
(absolute path or relative to the current directory)
.RE
.TP
\f[V]--file-associations\f[R] \f[I]path\f[R]
Path to a Properties file that contains list of key, value pairs
.RS
.PP
(absolute path or relative to the current directory)
.PP
The keys \[dq]extension\[dq], \[dq]mime-type\[dq], \[dq]icon\[dq], and
\[dq]description\[dq] can be used to describe the association.
.PP
This option can be used multiple times.
.RE
.TP
\f[V]--install-dir\f[R] \f[I]path\f[R]
Absolute path of the installation directory of the application (on macOS
or linux), or relative sub-path of the installation directory such as
\[dq]Program Files\[dq] or \[dq]AppData\[dq] (on Windows)
.TP
\f[V]--license-file\f[R] \f[I]path\f[R]
Path to the license file
.RS
.PP
(absolute path or relative to the current directory)
.RE
.TP
\f[V]--runtime-image\f[R] \f[I]path\f[R]
Path of the predefined runtime image to install
.RS
.PP
(absolute path or relative to the current directory)
.PP
Option is required when creating a runtime installer.
.RE
.TP
\f[V]--launcher-as-service\f[R]
Request to create an installer that will register the main application
launcher as a background service-type application.
.SS Platform dependent options for creating the application package:
.SS Windows platform options (available only when running on Windows):
.TP
\f[V]--win-dir-chooser\f[R]
Adds a dialog to enable the user to choose a directory in which the
product is installed.
.TP
\f[V]--win-help-url\f[R] \f[I]url\f[R]
URL where user can obtain further information or technical support
.TP
\f[V]--win-menu\f[R]
Request to add a Start Menu shortcut for this application
.TP
\f[V]--win-menu-group\f[R] \f[I]menu-group-name\f[R]
Start Menu group this application is placed in
.TP
\f[V]--win-per-user-install\f[R]
Request to perform an install on a per-user basis
.TP
\f[V]--win-shortcut\f[R]
Request to create a desktop shortcut for this application
.TP
\f[V]--win-shortcut-prompt\f[R]
Adds a dialog to enable the user to choose if shortcuts will be created
by installer
.TP
\f[V]--win-update-url\f[R] \f[I]url\f[R]
URL of available application update information
.TP
\f[V]--win-upgrade-uuid\f[R] \f[I]id\f[R]
UUID associated with upgrades for this package
.SS Linux platform options (available only when running on Linux):
.TP
\f[V]--linux-package-name\f[R] \f[I]name\f[R]
Name for Linux package
.RS
.PP
Defaults to the application name.
.RE
.TP
\f[V]--linux-deb-maintainer\f[R] \f[I]email-address\f[R]
Maintainer for .deb bundle
.TP
\f[V]--linux-menu-group\f[R] \f[I]menu-group-name\f[R]
Menu group this application is placed in
.TP
\f[V]--linux-package-deps\f[R]
Required packages or capabilities for the application
.TP
\f[V]--linux-rpm-license-type\f[R] \f[I]type\f[R]
Type of the license (\[dq]License: \f[I]value\f[R]\[dq] of the RPM
\&.spec)
.TP
\f[V]--linux-app-release\f[R] \f[I]release\f[R]
Release value of the RPM <name>.spec file or Debian revision value of
the DEB control file
.TP
\f[V]--linux-app-category\f[R] \f[I]category-value\f[R]
Group value of the RPM <name>.spec file or Section value of DEB control
file
.TP
\f[V]--linux-shortcut\f[R]
Creates a shortcut for the application.
.SS macOS platform options (available only when running on macOS):
.TP
\f[V]--mac-dmg-content\f[R] \f[I]additional-content\f[R] [\f[V],\f[R]\f[I]additional-content\f[R]...]
Include all the referenced content in the dmg.
.RS
.PP
This option can be used more than once.
.RE
.SH JPACKAGE EXAMPLES
.IP
.nf
\f[CB]
Generate an application package suitable for the host system:
\f[R]
.fi
.IP
.nf
\f[CB]
For a modular application:
    jpackage -n name -p modulePath -m moduleName/className
For a non-modular application:
    jpackage -i inputDir -n name \[rs]
        --main-class className --main-jar myJar.jar
From a pre-built application image:
    jpackage -n name --app-image appImageDir
\f[R]
.fi
.IP
.nf
\f[CB]
Generate an application image:
\f[R]
.fi
.IP
.nf
\f[CB]
For a modular application:
    jpackage --type app-image -n name -p modulePath \[rs]
        -m moduleName/className
For a non-modular application:
    jpackage --type app-image -i inputDir -n name \[rs]
        --main-class className --main-jar myJar.jar
To provide your own options to jlink, run jlink separately:
    jlink --output appRuntimeImage -p modulePath \[rs]
        --add-modules moduleName \[rs]
        --no-header-files [<additional jlink options>...]
    jpackage --type app-image -n name \[rs]
        -m moduleName/className --runtime-image appRuntimeImage
\f[R]
.fi
.IP
.nf
\f[CB]
Generate a Java runtime package:
\f[R]
.fi
.IP
.nf
\f[CB]
jpackage -n name --runtime-image <runtime-image>
\f[R]
.fi
.IP
.nf
\f[CB]
Sign the predefined application image (on macOS):
\f[R]
.fi
.IP
.nf
\f[CB]
jpackage --type app-image --app-image <app-image> \[rs]
    --mac-sign [<additional signing options>...]

Note: the only additional options that are permitted in this mode are:
      the set of additional mac signing options and --verbose
\f[R]
.fi
.SH JPACKAGE AND JLINK
.PP
jpackage will use jlink to create Java Runtime unless the
\f[V]--runtime-image\f[R] option is used.
The created Java Runtime image on Windows will include MS runtime
libraries bundled with the JDK.
If MS runtime libraries of a different version are needed for the
application, the user will need to add/replace those themselves.
.SH JPACKAGE RESOURCE DIRECTORY
.PP
Icons, template files, and other resources of jpackage can be
over-ridden by adding replacement resources to this directory.
jpackage will lookup files by specific names in the resource directory.
.SS Resource directory files considered only when running on Linux:
.TP
\f[V]<launcher-name>.png\f[R]
Application launcher icon
.RS
.PP
Default resource is \f[I]JavaApp.png\f[R]
.RE
.TP
\f[V]<launcher-name>.desktop\f[R]
A desktop file to be used with \f[V]xdg-desktop-menu\f[R] command
.RS
.PP
Considered with application launchers registered for file associations
and/or have an icon
.PP
Default resource is \f[I]template.desktop\f[R]
.RE
.SS Resource directory files considered only when building Linux DEB/RPM installer:
.TP
\f[V]<package-name>-<launcher-name>.service\f[R]
systemd unit file for application launcher registered as a background
service-type application
.RS
.PP
Default resource is \f[I]unit-template.service\f[R]
.RE
.SS Resource directory files considered only when building Linux RPM installer:
.TP
\f[V]<package-name>.spec\f[R]
RPM spec file
.RS
.PP
Default resource is \f[I]template.spec\f[R]
.RE
.SS Resource directory files considered only when building Linux DEB installer:
.TP
\f[V]control\f[R]
Control file
.RS
.PP
Default resource is \f[I]template.control\f[R]
.RE
.TP
\f[V]copyright\f[R]
Copyright file
.RS
.PP
Default resource is \f[I]template.copyright\f[R]
.RE
.TP
\f[V]preinstall\f[R]
Pre-install shell script
.RS
.PP
Default resource is \f[I]template.preinstall\f[R]
.RE
.TP
\f[V]prerm\f[R]
Pre-remove shell script
.RS
.PP
Default resource is \f[I]template.prerm\f[R]
.RE
.TP
\f[V]postinstall\f[R]
Post-install shell script
.RS
.PP
Default resource is \f[I]template.postinstall\f[R]
.RE
.TP
\f[V]postrm\f[R]
Post-remove shell script
.RS
.PP
Default resource is \f[I]template.postrm\f[R]
.RE
.SS Resource directory files considered only when running on Windows:
.TP
\f[V]<launcher-name>.ico\f[R]
Application launcher icon
.RS
.PP
Default resource is \f[I]JavaApp.ico\f[R]
.RE
.TP
\f[V]<launcher-name>.properties\f[R]
Properties file for application launcher executable
.RS
.PP
Default resource is \f[I]WinLauncher.template\f[R]
.RE
.SS Resource directory files considered only when building Windows MSI/EXE installer:
.TP
\f[V]<application-name>-post-image.wsf\f[R]
A Windows Script File (WSF) to run after building application image
.TP
\f[V]main.wxs\f[R]
Main WiX project file
.RS
.PP
Default resource is \f[I]main.wxs\f[R]
.RE
.TP
\f[V]overrides.wxi\f[R]
Overrides WiX project file
.RS
.PP
Default resource is \f[I]overrides.wxi\f[R]
.RE
.TP
\f[V]service-installer.exe\f[R]
Service installer executable
.RS
.PP
Considered if some application launchers are registered as background
service-type applications
.RE
.TP
\f[V]<launcher-name>-service-install.wxi\f[R]
Service installer WiX project file
.RS
.PP
Considered if some application launchers are registered as background
service-type applications
.PP
Default resource is \f[I]service-install.wxi\f[R]
.RE
.TP
\f[V]<launcher-name>-service-config.wxi\f[R]
Service installer WiX project file
.RS
.PP
Considered if some application launchers are registered as background
service-type applications
.PP
Default resource is \f[I]service-config.wxi\f[R]
.RE
.TP
\f[V]InstallDirNotEmptyDlg.wxs\f[R]
WiX project file for installer UI dialog checking installation directory
doesn\[aq]t exist or is empty
.RS
.PP
Default resource is \f[I]InstallDirNotEmptyDlg.wxs\f[R]
.RE
.TP
\f[V]ShortcutPromptDlg.wxs\f[R]
WiX project file for installer UI dialog configuring shortcuts
.RS
.PP
Default resource is \f[I]ShortcutPromptDlg.wxs\f[R]
.RE
.TP
\f[V]bundle.wxf\f[R]
WiX project file with the hierarchy of components of application image
.TP
\f[V]ui.wxf\f[R]
WiX project file for installer UI
.TP
\f[V]wix-conv.xsl\f[R]
WiX source code converter.
Used for converting WiX sources from WiX v3 to v4 schema when WiX v4 or
newer is used
.RS
.PP
Default resource is \f[I]wix3-to-wix4-conv.xsl\f[R]
.RE
.SS Resource directory files considered only when building Windows EXE installer:
.TP
\f[V]WinInstaller.properties\f[R]
Properties file for the installer executable
.RS
.PP
Default resource is \f[I]WinInstaller.template\f[R]
.RE
.TP
\f[V]<package-name>-post-msi.wsf\f[R]
A Windows Script File (WSF) to run after building embedded MSI installer
for EXE installer
.SS Resource directory files considered only when running on macOS:
.TP
\f[V]<launcher-name>.icns\f[R]
Application launcher icon
.RS
.PP
Default resource is \f[I]JavaApp.icns\f[R]
.RE
.TP
\f[V]Info.plist\f[R]
Application property list file
.RS
.PP
Default resource is \f[I]Info-lite.plist.template\f[R]
.RE
.TP
\f[V]Runtime-Info.plist\f[R]
Java Runtime property list file
.RS
.PP
Default resource is \f[I]Runtime-Info.plist.template\f[R]
.RE
.TP
\f[V]<application-name>.entitlements\f[R]
Signing entitlements property list file
.RS
.PP
Default resource is \f[I]sandbox.plist\f[R]
.RE
.SS Resource directory files considered only when building macOS PKG/DMG installer:
.TP
\f[V]<package-name>-post-image.sh\f[R]
Shell script to run after building application image
.SS Resource directory files considered only when building macOS PKG installer:
.TP
\f[V]uninstaller\f[R]
Uninstaller shell script
.RS
.PP
Considered if some application launchers are registered as background
service-type applications
.PP
Default resource is \f[I]uninstall.command.template\f[R]
.RE
.TP
\f[V]preinstall\f[R]
Pre-install shell script
.RS
.PP
Default resource is \f[I]preinstall.template\f[R]
.RE
.TP
\f[V]postinstall\f[R]
Post-install shell script
.RS
.PP
Default resource is \f[I]postinstall.template\f[R]
.RE
.TP
\f[V]services-preinstall\f[R]
Pre-install shell script for services package
.RS
.PP
Considered if some application launchers are registered as background
service-type applications
.PP
Default resource is \f[I]services-preinstall.template\f[R]
.RE
.TP
\f[V]services-postinstall\f[R]
Post-install shell script for services package
.RS
.PP
Considered if some application launchers are registered as background
service-type applications
.PP
Default resource is \f[I]services-postinstall.template\f[R]
.RE
.TP
\f[V]<package-name>-background.png\f[R]
Background image
.RS
.PP
Default resource is \f[I]background_pkg.png\f[R]
.RE
.TP
\f[V]<package-name>-background-darkAqua.png\f[R]
Dark background image
.RS
.PP
Default resource is \f[I]background_pkg.png\f[R]
.RE
.TP
\f[V]product-def.plist\f[R]
Package property list file
.RS
.PP
Default resource is \f[I]product-def.plist\f[R]
.RE
.TP
\f[V]<package-name>-<launcher-name>.plist\f[R]
launchd property list file for application launcher registered as a
background service-type application
.RS
.PP
Default resource is \f[I]launchd.plist.template\f[R]
.RE
.SS Resource directory files considered only when building macOS DMG installer:
.TP
\f[V]<package-name>-dmg-setup.scpt\f[R]
Setup AppleScript script
.RS
.PP
Default resource is \f[I]DMGsetup.scpt\f[R]
.RE
.TP
\f[V]<package-name>-license.plist\f[R]
License property list file
.RS
.PP
Default resource is \f[I]lic_template.plist\f[R]
.RE
.TP
\f[V]<package-name>-background.tiff\f[R]
Background image
.RS
.PP
Default resource is \f[I]background_dmg.tiff\f[R]
.RE
.TP
\f[V]<package-name>-volume.icns\f[R]
Volume icon
.RS
.PP
Default resource is \f[I]JavaApp.icns\f[R]
.RE
