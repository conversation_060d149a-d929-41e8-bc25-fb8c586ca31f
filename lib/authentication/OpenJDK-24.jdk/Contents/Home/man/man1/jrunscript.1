.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JRUNSCRIPT" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jrunscript - run a command-line script shell that supports interactive
and batch modes
.SH SYNOPSIS
.PP
\f[B]Note:\f[R]
.PP
This tool is \f[B]experimental\f[R] and unsupported.
It is deprecated and will be removed in a future release.
.PP
\f[V]jrunscript\f[R] [\f[I]options\f[R]] [\f[I]arguments\f[R]]
.TP
\f[I]options\f[R]
This represents the \f[V]jrunscript\f[R] command-line options that can
be used.
See \f[B]Options for the jrunscript Command\f[R].
.TP
\f[I]arguments\f[R]
Arguments, when used, follow immediately after options or the command
name.
See \f[B]Arguments\f[R].
.SH DESCRIPTION
.PP
The \f[V]jrunscript\f[R] command is a language-independent command-line
script shell.
The \f[V]jrunscript\f[R] command supports both an interactive
(read-eval-print) mode and a batch (\f[V]-f\f[R] option) mode of script
execution.
By default, JavaScript is the language used, but the \f[V]-l\f[R] option
can be used to specify a different language.
By using Java to scripting language communication, the
\f[V]jrunscript\f[R] command supports an exploratory programming style.
.PP
If JavaScript is used, then before it evaluates a user defined script,
the \f[V]jrunscript\f[R] command initializes certain built-in functions
and objects, which are documented in the API Specification for
\f[V]jrunscript\f[R] JavaScript built-in functions.
.SH OPTIONS FOR THE JRUNSCRIPT COMMAND
.TP
\f[V]-cp\f[R] \f[I]path\f[R] or \f[V]-classpath\f[R] \f[I]path\f[R]
Indicates where any class files are that the script needs to access.
.TP
\f[V]-D\f[R]\f[I]name\f[R]\f[V]=\f[R]\f[I]value\f[R]
Sets a Java system property.
.TP
\f[V]-J\f[R]\f[I]flag\f[R]
Passes \f[I]flag\f[R] directly to the Java Virtual Machine where the
\f[V]jrunscript\f[R] command is running.
.TP
\f[V]-l\f[R] \f[I]language\f[R]
Uses the specified scripting language.
By default, JavaScript is used.
To use other scripting languages, you must specify the corresponding
script engine\[aq]s JAR file with the \f[V]-cp\f[R] or
\f[V]-classpath\f[R] option.
.TP
\f[V]-e\f[R] \f[I]script\f[R]
Evaluates the specified script.
This option can be used to run one-line scripts that are specified
completely on the command line.
.TP
\f[V]-encoding\f[R] \f[I]encoding\f[R]
Specifies the character encoding used to read script files.
.TP
\f[V]-f\f[R] \f[I]script-file\f[R]
Evaluates the specified script file (batch mode).
.TP
\f[V]-f -\f[R]
Enters interactive mode to read and evaluate a script from standard
input.
.TP
\f[V]-help\f[R] or \f[V]-?\f[R]
Displays a help message and exits.
.TP
\f[V]-q\f[R]
Lists all script engines available and exits.
.SH ARGUMENTS
.PP
If arguments are present and if no \f[V]-e\f[R] or \f[V]-f\f[R] option
is used, then the first argument is the script file and the rest of the
arguments, if any, are passed as script arguments.
If arguments and the \f[V]-e\f[R] or the \f[V]-f\f[R] option are used,
then all arguments are passed as script arguments.
If arguments \f[V]-e\f[R] and \f[V]-f\f[R] are missing, then the
interactive mode is used.
.SH EXAMPLE OF EXECUTING INLINE SCRIPTS
.RS
.PP
\f[V]jrunscript -e \[dq]print(\[aq]hello world\[aq])\[dq]\f[R]
.RE
.RS
.PP
\f[V]jrunscript -e \[dq]cat(\[aq]http://www.example.com\[aq])\[dq]\f[R]
.RE
.SH EXAMPLE OF USING SPECIFIED LANGUAGE AND EVALUATE THE SCRIPT FILE
.RS
.PP
\f[V]jrunscript -l js -f test.js\f[R]
.RE
.SH EXAMPLE OF INTERACTIVE MODE
.IP
.nf
\f[CB]
jrunscript
js> print(\[aq]Hello World\[rs]n\[aq]);
Hello World
js> 34 + 55
89.0
js> t = new java.lang.Thread(function() { print(\[aq]Hello World\[rs]n\[aq]); })
Thread[Thread-0,5,main]
js> t.start()
js> Hello World

js>
\f[R]
.fi
.SH RUN SCRIPT FILE WITH SCRIPT ARGUMENTS
.PP
In this example, the \f[V]test.js\f[R] file is the script file.
The \f[V]arg1\f[R], \f[V]arg2\f[R], and \f[V]arg3\f[R] arguments are
passed to the script.
The script can access these arguments with an arguments array.
.RS
.PP
\f[V]jrunscript test.js arg1 arg2 arg3\f[R]
.RE
