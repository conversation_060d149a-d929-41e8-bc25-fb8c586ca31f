<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict/>
	<key>files2</key>
	<dict>
		<key>Home/bin/jar</key>
		<dict>
			<key>hash2</key>
			<data>
			z8wd1Q5ctmG0JDKxUL+4n6kr4Ic9IqiMMuDWz8d6NOI=
			</data>
		</dict>
		<key>Home/bin/jarsigner</key>
		<dict>
			<key>hash2</key>
			<data>
			dy7WJdIuUB45ESCeSDhmXeUocChodTx6fdWtTP1wcrA=
			</data>
		</dict>
		<key>Home/bin/java</key>
		<dict>
			<key>hash2</key>
			<data>
			jyDmzqPfN4BFAFVOdDrJoBthao2WO09q4Ohd/R5J6/I=
			</data>
		</dict>
		<key>Home/bin/javac</key>
		<dict>
			<key>hash2</key>
			<data>
			NKEHPXJEuW3XyHcEfSfw718yeVyTMKpGjvGH5x0RQaI=
			</data>
		</dict>
		<key>Home/bin/javadoc</key>
		<dict>
			<key>hash2</key>
			<data>
			SLNedPWyrkGV0kn7wVc5PBBCRDoiZiwRCp5EMElGpiY=
			</data>
		</dict>
		<key>Home/bin/javap</key>
		<dict>
			<key>hash2</key>
			<data>
			vjnSHRfqgy310eMCitz5s+hOd8zSv8/v+J6fZE7T6Zs=
			</data>
		</dict>
		<key>Home/bin/jcmd</key>
		<dict>
			<key>hash2</key>
			<data>
			1vuyo0AL5i6lIcLzG3Bqsm4jjqfcBBjgdHntqmCI110=
			</data>
		</dict>
		<key>Home/bin/jconsole</key>
		<dict>
			<key>hash2</key>
			<data>
			HSmMzbu4Xmj7mVcEgTc3Qxyt4CgbCJnDfilA4RtCUaw=
			</data>
		</dict>
		<key>Home/bin/jdb</key>
		<dict>
			<key>hash2</key>
			<data>
			d4ZQjSIrcdmcW6r0JtkI8nWUTMsxKal7kn3Ev5sqRak=
			</data>
		</dict>
		<key>Home/bin/jdeprscan</key>
		<dict>
			<key>hash2</key>
			<data>
			13d1Ph50qWKAdun3904BlhsEN3LuV0Hx515bj8KeCDw=
			</data>
		</dict>
		<key>Home/bin/jdeps</key>
		<dict>
			<key>hash2</key>
			<data>
			PMIfHpOb1ehIS1K0Cnju+Gc1Dv6USVoNbqds9kCcjLU=
			</data>
		</dict>
		<key>Home/bin/jfr</key>
		<dict>
			<key>hash2</key>
			<data>
			I1oFNsDsPlTxjxDBn16Djw1gnZn/89JBKAsm76J+y4c=
			</data>
		</dict>
		<key>Home/bin/jhsdb</key>
		<dict>
			<key>hash2</key>
			<data>
			EeiqN7htbLTRfo+am/nJSOxYkvF7m3NrZnYrQHXi5gA=
			</data>
		</dict>
		<key>Home/bin/jimage</key>
		<dict>
			<key>hash2</key>
			<data>
			ttR84uOP5gJNMOnrU2LZpqMY1DYWC5YPryomXw+14c8=
			</data>
		</dict>
		<key>Home/bin/jinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			HmxHdha1dMqe6TYJfJnX1bRIcAbWutef3kKHlTsrrco=
			</data>
		</dict>
		<key>Home/bin/jlink</key>
		<dict>
			<key>hash2</key>
			<data>
			p9SwbVecxTvF3npqDAuC5T4ibz9kUvcbpe4f6m4DYN8=
			</data>
		</dict>
		<key>Home/bin/jmap</key>
		<dict>
			<key>hash2</key>
			<data>
			p4eL9sPVpCa1K0djZAC82H3kxycjZ1Jgv1ni7wCaq/I=
			</data>
		</dict>
		<key>Home/bin/jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			4LKLpD25JkD3bNG1VeyPi+HHzBV9fR2m144s1DYL4E4=
			</data>
		</dict>
		<key>Home/bin/jnativescan</key>
		<dict>
			<key>hash2</key>
			<data>
			BRPcGnI4I8x9w5pb4td9FVqj4EV+J2JSLsus8Q7O5JQ=
			</data>
		</dict>
		<key>Home/bin/jpackage</key>
		<dict>
			<key>hash2</key>
			<data>
			AYUHfUMInttrt9xyzP4YoAS84QdWsWHE/JtLaXtOF2w=
			</data>
		</dict>
		<key>Home/bin/jps</key>
		<dict>
			<key>hash2</key>
			<data>
			VPk/y/DgI71tVRwe2mhTsevW9dc6wkuXjFvb3uRVZD0=
			</data>
		</dict>
		<key>Home/bin/jrunscript</key>
		<dict>
			<key>hash2</key>
			<data>
			hbDACuLgT9yBHZu7jh7y7Bwb9S6BZltcJ+OOksnmpSk=
			</data>
		</dict>
		<key>Home/bin/jshell</key>
		<dict>
			<key>hash2</key>
			<data>
			/sWcQKTJ3CC8VCkRDdN+RlD5qmZb8CiXsK2nZNeHDR8=
			</data>
		</dict>
		<key>Home/bin/jstack</key>
		<dict>
			<key>hash2</key>
			<data>
			UHtCCCRlTY1JXVuBbkME44Z/rWZ+97QhrEawb14Svws=
			</data>
		</dict>
		<key>Home/bin/jstat</key>
		<dict>
			<key>hash2</key>
			<data>
			MFwHQhMe8y8maTzlksAijCNfidHHdJqu/J10HZqzNWo=
			</data>
		</dict>
		<key>Home/bin/jstatd</key>
		<dict>
			<key>hash2</key>
			<data>
			Ppd2MKAGj707czIl8bkyvKI7NTfTyTe6ii7uxooEYWo=
			</data>
		</dict>
		<key>Home/bin/jwebserver</key>
		<dict>
			<key>hash2</key>
			<data>
			CLzOWsmdEDY6RQOvfcoIgJvRkkfOiKFR9vV8VPRo1j4=
			</data>
		</dict>
		<key>Home/bin/keytool</key>
		<dict>
			<key>hash2</key>
			<data>
			G2gDrDeA1d+GigRTaOh2McCLUqcTlf7imeKmHjHlToU=
			</data>
		</dict>
		<key>Home/bin/rmiregistry</key>
		<dict>
			<key>hash2</key>
			<data>
			QAwOGvNBf3Oxr3oPCp/RYf3UTnsuotgPLdUcn9ZoZ4g=
			</data>
		</dict>
		<key>Home/bin/serialver</key>
		<dict>
			<key>hash2</key>
			<data>
			eC7mmi45cYvAJ3094ncNKRhA46HjSOw+1cUkkRP+4ng=
			</data>
		</dict>
		<key>Home/conf/jaxp-strict.properties.template</key>
		<dict>
			<key>hash2</key>
			<data>
			PMm4djN2lBSyLCTJfs3tihXfYw/lqxKryiOY2I4xsys=
			</data>
		</dict>
		<key>Home/conf/jaxp.properties</key>
		<dict>
			<key>hash2</key>
			<data>
			1PRQUqEA5a7wK2Y4o7nGmyvdRhVc61ilJnHl1ZTDAQA=
			</data>
		</dict>
		<key>Home/conf/logging.properties</key>
		<dict>
			<key>hash2</key>
			<data>
			hZBjPh6KfYj/50zAQYnFxhse1HAxgMvIUdzVV178znQ=
			</data>
		</dict>
		<key>Home/conf/management/jmxremote.access</key>
		<dict>
			<key>hash2</key>
			<data>
			DCXSbuISyh6MM/Z8PEYNQ/6EnDodI9vjQRSFF2ArKAw=
			</data>
		</dict>
		<key>Home/conf/management/jmxremote.password.template</key>
		<dict>
			<key>hash2</key>
			<data>
			AnO2prniDmzlTFrucBZAKOA5UGOyt9OQYKQLZJVUPb8=
			</data>
		</dict>
		<key>Home/conf/management/management.properties</key>
		<dict>
			<key>hash2</key>
			<data>
			deYDJ1MdNNHOXrUH21JxGh2Sa5x30U1rOjktdHEFSI0=
			</data>
		</dict>
		<key>Home/conf/net.properties</key>
		<dict>
			<key>hash2</key>
			<data>
			8Ak5BoIMMOB5y/eb10TXYarzKukXWYx85x+sfq4W+G0=
			</data>
		</dict>
		<key>Home/conf/security/java.security</key>
		<dict>
			<key>hash2</key>
			<data>
			Evv8JXeT86ofuxJnHFhm+fWnGQQGf0ob3VflTtBnqG4=
			</data>
		</dict>
		<key>Home/conf/security/policy/README.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			baB0czSw/qdZL9kmFLK7yLEmU14Smx/uSDd02RTpjrU=
			</data>
		</dict>
		<key>Home/conf/security/policy/limited/default_US_export.policy</key>
		<dict>
			<key>hash2</key>
			<data>
			dYuTClJvxnCrdTf4wmMhUnBQox9fQhSaLdpiPFagoak=
			</data>
		</dict>
		<key>Home/conf/security/policy/limited/default_local.policy</key>
		<dict>
			<key>hash2</key>
			<data>
			KyYnVI5hMWFQ1H/8PmytRlygWzzM1Hhet9Iap7qg9EE=
			</data>
		</dict>
		<key>Home/conf/security/policy/limited/exempt_local.policy</key>
		<dict>
			<key>hash2</key>
			<data>
			jD12SKvNlaJyzhLbhwCCk39Nf2h41zDYPLf7sx64ssk=
			</data>
		</dict>
		<key>Home/conf/security/policy/unlimited/default_US_export.policy</key>
		<dict>
			<key>hash2</key>
			<data>
			dYuTClJvxnCrdTf4wmMhUnBQox9fQhSaLdpiPFagoak=
			</data>
		</dict>
		<key>Home/conf/security/policy/unlimited/default_local.policy</key>
		<dict>
			<key>hash2</key>
			<data>
			jYoxjm2Q39fiZhLStjhapwT2hsphNMVR+JKEGNkrhRo=
			</data>
		</dict>
		<key>Home/conf/sound.properties</key>
		<dict>
			<key>hash2</key>
			<data>
			KZwjYLYVXrKJkOxJzSF1P5fkNEL+j6sD4E8+IT30OmY=
			</data>
		</dict>
		<key>Home/include/classfile_constants.h</key>
		<dict>
			<key>hash2</key>
			<data>
			hql/cowEcCqKgqAdxuMasYkeq0cJkJPBek6qGLqGhvs=
			</data>
		</dict>
		<key>Home/include/darwin/jawt_md.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SvEJJ+vP39+/IYu/kMOXhW0lGjEsH+j0RpsFYjXaTfs=
			</data>
		</dict>
		<key>Home/include/darwin/jni_md.h</key>
		<dict>
			<key>hash2</key>
			<data>
			gjKlpo02BgqqQm+/20U3mD7P38X+B6QyMABd3RpLmGo=
			</data>
		</dict>
		<key>Home/include/jawt.h</key>
		<dict>
			<key>hash2</key>
			<data>
			hRAdB5KKWJrM/vnXwmGFD6qNWvyO8mKvfslzQAj28vU=
			</data>
		</dict>
		<key>Home/include/jdwpTransport.h</key>
		<dict>
			<key>hash2</key>
			<data>
			xCJW+FlsvztZqiW+VLvy7OR6Xe511OYVN5HcViDP8sg=
			</data>
		</dict>
		<key>Home/include/jni.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HgO9g9FmKsAGR/egbBHTIzN2fuU3gD7B0VPomIhBRSA=
			</data>
		</dict>
		<key>Home/include/jvmti.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ep7vkxqk/ErNKkOo/JEFSe3bj88j3JIwQqyq4HGBzNs=
			</data>
		</dict>
		<key>Home/include/jvmticmlr.h</key>
		<dict>
			<key>hash2</key>
			<data>
			buPlLSS9tPTQMS37qz1HvVJMvaxVQKTXkMrAYgxZs8g=
			</data>
		</dict>
		<key>Home/jmods/java.base.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			YnZzIYjNrfTq0iFZOaDSkXC8PwlZWiD1+DW8DnRbJ/Y=
			</data>
		</dict>
		<key>Home/jmods/java.compiler.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			aCRaAAom6kaqWFisFauZlM+ERIK2dMUN1tYQy6HQXXY=
			</data>
		</dict>
		<key>Home/jmods/java.datatransfer.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			RHzkzbday57444Citb0Mjo7yh3Hhs8fm4oNq4ALzg6g=
			</data>
		</dict>
		<key>Home/jmods/java.desktop.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			mtwE1sxc70+EQ/JtjPcANUiAazucpo/p5roCVPvcFZU=
			</data>
		</dict>
		<key>Home/jmods/java.instrument.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			01q4le7CxB/Q1rBjNXC/97DzGNexLt1SUxLeiH81Eis=
			</data>
		</dict>
		<key>Home/jmods/java.logging.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			IYyyciiG1s8DlB8KhQL3UbB7Y2g6CUVDai51hAis1qg=
			</data>
		</dict>
		<key>Home/jmods/java.management.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			Hut2P3eyazM0V1UEV2TK1IF6HhGk2+7oQz+nzoxK4cc=
			</data>
		</dict>
		<key>Home/jmods/java.management.rmi.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			t3UVdPJ2BoHKxz2myVFqZu481qyNUn/HBMs/Qzi2SQg=
			</data>
		</dict>
		<key>Home/jmods/java.naming.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			FuD8WztWGb/OyzRSppzFZCQgsoENzY0Gd4nPxiFyYlY=
			</data>
		</dict>
		<key>Home/jmods/java.net.http.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			UXVu8Dk20B5m5tkDZX0JwEBdnqSuDIn/v6fbO0FduLQ=
			</data>
		</dict>
		<key>Home/jmods/java.prefs.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			jsG+pv18D87kcDzdbvsHBizw8JdjecwZNvW/ekD/Erc=
			</data>
		</dict>
		<key>Home/jmods/java.rmi.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			VB2S8cZMCM4qc/94upal+INvLasDO/LMfiVznLEfO64=
			</data>
		</dict>
		<key>Home/jmods/java.scripting.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			q7T8kLBtRmeja99+8FQcLhhaIDY6uK4Ih+t1FpBs9xg=
			</data>
		</dict>
		<key>Home/jmods/java.se.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			GdEe4CoUX/2VOxJeojRk53LTJ5zexvRqHjA8gtJazvU=
			</data>
		</dict>
		<key>Home/jmods/java.security.jgss.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			o9zmcbux/uS4xdeh4rZ1NUJYpErnMjw57WFYAuFoljs=
			</data>
		</dict>
		<key>Home/jmods/java.security.sasl.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			HS4ZeS5ge/1Q4JFSuBC95CeGbJZ96WnNqKOGMNPmU7U=
			</data>
		</dict>
		<key>Home/jmods/java.smartcardio.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			4sWMgRU6+Rp5ZPrJLwByhfn/bo+9o3nG7FQnw+cN4pM=
			</data>
		</dict>
		<key>Home/jmods/java.sql.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			BZ79QbrA5R2QL1IfxeB947JV80/+JSS2FV5qh3/JtgU=
			</data>
		</dict>
		<key>Home/jmods/java.sql.rowset.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			W67BQJvvO8Qq6punnckw/ENdoXM7xFFgrZSVvkt9wSA=
			</data>
		</dict>
		<key>Home/jmods/java.transaction.xa.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			fAS6F3UMcFekoJ0RB9wPnpVLUWfuNjiynWAYAw7GhFg=
			</data>
		</dict>
		<key>Home/jmods/java.xml.crypto.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			ilmpZAQ4GAz9FMv7LIJ2ikIsl10cMY+9VndO3Zu4jGo=
			</data>
		</dict>
		<key>Home/jmods/java.xml.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			e+R0vSjNatiZpTiwFzb+SWxEKJSGoZ+iRzrBpzhXaao=
			</data>
		</dict>
		<key>Home/jmods/jdk.accessibility.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			fRNJZVa8PpeAny4AKd9ic7ILLCS0N7cuRLTghSDzUDU=
			</data>
		</dict>
		<key>Home/jmods/jdk.attach.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			jKjT2YQVbHww4Lwqs3HY9r+/fPymK2U2cKUuVF49IPs=
			</data>
		</dict>
		<key>Home/jmods/jdk.charsets.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			lZXxuBl+pwe/s+MInpCJmFDovg6mfA1+lKxTDrVl0Cg=
			</data>
		</dict>
		<key>Home/jmods/jdk.compiler.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			Qb4tNMeLievz/ueWKjbxp0s6rXR5RoBSefydmeLXRgw=
			</data>
		</dict>
		<key>Home/jmods/jdk.crypto.cryptoki.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			exgPcVnVNDwP1A6+b2mjsyhz1FVJ9ubfOUY593wZ5Ao=
			</data>
		</dict>
		<key>Home/jmods/jdk.crypto.ec.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			E/4GL+e8+PAVVGNwtRqHOq/3C5NnC+fmQtE63whKkrg=
			</data>
		</dict>
		<key>Home/jmods/jdk.dynalink.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			ywRtlBCdTM7iiVhOidMMpg5TiKZ6taX7pQQHbpls1Lc=
			</data>
		</dict>
		<key>Home/jmods/jdk.editpad.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			Ldsh1e6ko8KClir3fwE18fgCvHpJmeGgXFCaGog0vis=
			</data>
		</dict>
		<key>Home/jmods/jdk.graal.compiler.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			Ae9WNif1MBUcERvDgYA1W1as0lPoheDLg9eTFXZg5sI=
			</data>
		</dict>
		<key>Home/jmods/jdk.graal.compiler.management.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			GzHOr7ieMvMZsKOKHZP3uU0rAIq/FspbRQPsRn2so7c=
			</data>
		</dict>
		<key>Home/jmods/jdk.hotspot.agent.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			LvUOnWSn4GSuJ2lcGY5ZPNP0/+2AUWcFmsSFpvhAbWw=
			</data>
		</dict>
		<key>Home/jmods/jdk.httpserver.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			DwBi7W6dgx8X13AuljFignCxKBiMsjbEDMppow0t7NE=
			</data>
		</dict>
		<key>Home/jmods/jdk.incubator.vector.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			hrYVEavmVlB7cxrpXTG5oRDITKFyHOON37WO5xuOrtY=
			</data>
		</dict>
		<key>Home/jmods/jdk.internal.ed.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			DtmDOVyEmNtIifstqic9CKpt4zEHcDHz99hm2/a3v+s=
			</data>
		</dict>
		<key>Home/jmods/jdk.internal.jvmstat.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			uPwFBruH+VlsVxYXxP+osI5kSUOlRT2d1Z79zKNDyvI=
			</data>
		</dict>
		<key>Home/jmods/jdk.internal.le.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			eL2pu+vT7BAMqWTlGAMxYiu4czGaPjJO9Qlx9QxVxjA=
			</data>
		</dict>
		<key>Home/jmods/jdk.internal.md.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			UfhWyWcZQcAd1tWFSWjxIsxw4AXj9VJ5vojRxuJW2fU=
			</data>
		</dict>
		<key>Home/jmods/jdk.internal.opt.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			VpV76vgGd87j7DWsvT5MU42RcaN3zYdjl6SXqLrCmfk=
			</data>
		</dict>
		<key>Home/jmods/jdk.internal.vm.ci.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			9gafZpgje09k8QQbbDuVelKHvmzz/AR0WTT/GmFT+K4=
			</data>
		</dict>
		<key>Home/jmods/jdk.jartool.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			lZD8mWiVJzlGbkgLOZSZzWvG46BAAPQ+PIuXiD4yu9g=
			</data>
		</dict>
		<key>Home/jmods/jdk.javadoc.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			4UURHSQsuGvy1RhzKJzVAHLzQ5Gljcs2n9VhTcvG/Rc=
			</data>
		</dict>
		<key>Home/jmods/jdk.jcmd.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			BNbkMcGgyKeTX/wOys5AYpJbDNVEavwAsJwYfHsayUg=
			</data>
		</dict>
		<key>Home/jmods/jdk.jconsole.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			aNkjo8X1h4n2Gp/eqIjXA44TzetuPogydvs23kBOBns=
			</data>
		</dict>
		<key>Home/jmods/jdk.jdeps.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			1Nb34Uu7ACMo/qESGcl1PeVMzpIB8NrgLbpO2JEaqrE=
			</data>
		</dict>
		<key>Home/jmods/jdk.jdi.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			3tLb799YQXdOOnjyNk56EqI7WWoiKbyx4TnXUcIvAYc=
			</data>
		</dict>
		<key>Home/jmods/jdk.jdwp.agent.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			FB5pXGJdrThcmdRJtmedKoW7vOyT644Yf7FrvGTw7yQ=
			</data>
		</dict>
		<key>Home/jmods/jdk.jfr.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			lo0KKOHSvusZaNC/lSlNawqDYzFA2vXOlZaEmKovbuE=
			</data>
		</dict>
		<key>Home/jmods/jdk.jlink.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			R/iVai7oA05NIMXIRZ9ijC8afpPOhtu9+E+Us+Ak+tU=
			</data>
		</dict>
		<key>Home/jmods/jdk.jpackage.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			OL/8H0SVFRe30oqBWgnBCKRuzkuk7Qi5VqQ4D2ejKAk=
			</data>
		</dict>
		<key>Home/jmods/jdk.jshell.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			HciuLAW8x5gXZRjdEbtFUqP6zPqjyuBvIO3TEvaM/Aw=
			</data>
		</dict>
		<key>Home/jmods/jdk.jsobject.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			YyBGV7nARmG9HY+etmGcsz3NdAvXJAQRh9dJQqMEpSk=
			</data>
		</dict>
		<key>Home/jmods/jdk.jstatd.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			xWOAP4aGu/kmJFllWcdFx4z6FOKit1vdFtG4aK+VKpk=
			</data>
		</dict>
		<key>Home/jmods/jdk.localedata.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			14oDe3DT7Rz6hPYHVHryN9BE/WfBKuBRs9giEEQ2nXk=
			</data>
		</dict>
		<key>Home/jmods/jdk.management.agent.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			rHl4q42TN518F/t4GMH4lQCEZWiR93IPoDN8sjXWp2E=
			</data>
		</dict>
		<key>Home/jmods/jdk.management.jfr.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			0G66NnkAlij3NddkIBKYRp76DBKB6zMCPEaQYO58iH8=
			</data>
		</dict>
		<key>Home/jmods/jdk.management.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			K4TiZqCKl546GFaJ7CpWc6V3OozAnwB3+LlKvb1qm/8=
			</data>
		</dict>
		<key>Home/jmods/jdk.naming.dns.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			u86n6UGJWfuZjZiLpgMF2K8iWcSej5twuHaja9PFa/8=
			</data>
		</dict>
		<key>Home/jmods/jdk.naming.rmi.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			niCJ4XZWH24RqU52MRKDpPQOe5PJHvpvFp7vVB7RRxE=
			</data>
		</dict>
		<key>Home/jmods/jdk.net.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			oOSR6QB7zUgzb2blhUJh8pM0VVSna03FjduZ3KOBanY=
			</data>
		</dict>
		<key>Home/jmods/jdk.nio.mapmode.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			4NxbXxdqrCd4bYokrr2k039JYWh8Jj2Ane+IEEer5ik=
			</data>
		</dict>
		<key>Home/jmods/jdk.sctp.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			PPQuQtgnqNMJRw3JWgCFsNv72MbhMtY5u9Q4c0xfcZk=
			</data>
		</dict>
		<key>Home/jmods/jdk.security.auth.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			CGJbdDVseYqeLTRnK2sSNcI+1VwF/UsMQ4+vqR2xV2U=
			</data>
		</dict>
		<key>Home/jmods/jdk.security.jgss.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			Gi3mO1Cc8d0OSRbK00N0Gkb/nrPLY8iI73OPqJCuYWw=
			</data>
		</dict>
		<key>Home/jmods/jdk.unsupported.desktop.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			e4dHYh0v5J6EOdT+LsDYQ6T6YiDdoTWv6CIY3uAonj8=
			</data>
		</dict>
		<key>Home/jmods/jdk.unsupported.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			BP0aKrdy3Z/MKC7aXnnkwFY6BKQdLOIB8yyO5kM21dA=
			</data>
		</dict>
		<key>Home/jmods/jdk.xml.dom.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			E83PWXIxztzYsNZIMO/n8sC4yxXUexjzdmZ3LhgfKg0=
			</data>
		</dict>
		<key>Home/jmods/jdk.zipfs.jmod</key>
		<dict>
			<key>hash2</key>
			<data>
			9LFlOrGgqpCmqJpi99JHvaCl+CxR4rQrBQfyny3Kx8A=
			</data>
		</dict>
		<key>Home/legal/java.base/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>hash2</key>
			<data>
			ppvOJ1uno1cK9lecsPVWgs11/t/NSeDo6QIicMRHyRY=
			</data>
		</dict>
		<key>Home/legal/java.base/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>hash2</key>
			<data>
			dSkvA78j09t8mFrswZECm5OIMgByHtI+00ouYBRj3zM=
			</data>
		</dict>
		<key>Home/legal/java.base/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			S5q+vEM4BIp8LcGE6fgA3rNJNmvfKOsjwmd6d7TIdyY=
			</data>
		</dict>
		<key>Home/legal/java.base/aes.md</key>
		<dict>
			<key>hash2</key>
			<data>
			RcbU2kgyXt+/89z3HHBOUEwFeQRDXtI8bVcEbVUetp0=
			</data>
		</dict>
		<key>Home/legal/java.base/asm.md</key>
		<dict>
			<key>hash2</key>
			<data>
			hry7RIFeIEriECSatskVcZ/2zJKtqDzv7VvP5NBFUk0=
			</data>
		</dict>
		<key>Home/legal/java.base/c-libutl.md</key>
		<dict>
			<key>hash2</key>
			<data>
			vvQGeZItb9+35N2yI61nIjAPYFS6c3u/YYjWD87FF/k=
			</data>
		</dict>
		<key>Home/legal/java.base/cldr.md</key>
		<dict>
			<key>hash2</key>
			<data>
			j+NGEtPKbmNAWgGAIjQN8JUZrURSyyruxXc8cWXIH2A=
			</data>
		</dict>
		<key>Home/legal/java.base/icu.md</key>
		<dict>
			<key>hash2</key>
			<data>
			viOtIKgjHSJBs3/PQBVI/r2Fl96Dc/iSaIggIMt8iBg=
			</data>
		</dict>
		<key>Home/legal/java.base/public_suffix.md</key>
		<dict>
			<key>hash2</key>
			<data>
			14GOAuv8TlzYJhPgA+e6a+Lp1ZSepKoLqI1NL3ymmZk=
			</data>
		</dict>
		<key>Home/legal/java.base/siphash.md</key>
		<dict>
			<key>hash2</key>
			<data>
			WnkrWnStKl89anrYt6hBEW5Yp3LBi8bjkjIKNlsiLHY=
			</data>
		</dict>
		<key>Home/legal/java.base/unicode.md</key>
		<dict>
			<key>hash2</key>
			<data>
			UZTWdg8ufszAeAQZbMYZ+E5zD6nRDAUptxQWGG9NEtw=
			</data>
		</dict>
		<key>Home/legal/java.compiler/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.compiler/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.compiler/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.datatransfer/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.datatransfer/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.datatransfer/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.desktop/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.desktop/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.desktop/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.desktop/colorimaging.md</key>
		<dict>
			<key>hash2</key>
			<data>
			BNYePo5x3UUuvlIAivU3jZ9mQNFFeK61FdxTdZc7AYk=
			</data>
		</dict>
		<key>Home/legal/java.desktop/freetype.md</key>
		<dict>
			<key>hash2</key>
			<data>
			CTE5ZeH1IpLPM1tmlk40rWHEbkJ/+tS807/gLQkNxBs=
			</data>
		</dict>
		<key>Home/legal/java.desktop/giflib.md</key>
		<dict>
			<key>hash2</key>
			<data>
			KrtOqCH9cL/mImmRPUhydmdIUzvl3D3ojRXff/bPsL8=
			</data>
		</dict>
		<key>Home/legal/java.desktop/harfbuzz.md</key>
		<dict>
			<key>hash2</key>
			<data>
			ch18WiS9DApZ/l7h/eHBdUGnRcA444g9SxhKIXefTkg=
			</data>
		</dict>
		<key>Home/legal/java.desktop/jpeg.md</key>
		<dict>
			<key>hash2</key>
			<data>
			wd+5cZpxrZ+GH4coVQVC1oHiXI70DmOTYG5uKgwdZTo=
			</data>
		</dict>
		<key>Home/legal/java.desktop/lcms.md</key>
		<dict>
			<key>hash2</key>
			<data>
			6fXzdMtBFqy9guw5sKH5OrH1rf2MIISIuo+X3mXoZEY=
			</data>
		</dict>
		<key>Home/legal/java.desktop/libpng.md</key>
		<dict>
			<key>hash2</key>
			<data>
			JkKcHrZdtBysgZmb69cFpgpduh2DdmTkzJT1TVhn2Bg=
			</data>
		</dict>
		<key>Home/legal/java.desktop/mesa3d.md</key>
		<dict>
			<key>hash2</key>
			<data>
			Y/Tm91yuu8y5XZA/tD5GrHERs2JNCjTxRrJ219nnsVI=
			</data>
		</dict>
		<key>Home/legal/java.desktop/pipewire.md</key>
		<dict>
			<key>hash2</key>
			<data>
			fASU016F5fM7saawYahLe3RZBHZMO3usfX2LgXpsotY=
			</data>
		</dict>
		<key>Home/legal/java.desktop/xwd.md</key>
		<dict>
			<key>hash2</key>
			<data>
			HU/6k8h/NQhLAqeqkKIQhLQBnbT+EAPC5c53W0o4T1k=
			</data>
		</dict>
		<key>Home/legal/java.instrument/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.instrument/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.instrument/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.logging/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.logging/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.logging/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.management.rmi/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.management.rmi/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.management.rmi/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.management/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.management/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.management/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.naming/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.naming/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.naming/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.net.http/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.net.http/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.net.http/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.prefs/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.prefs/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.prefs/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.rmi/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.rmi/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.rmi/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.scripting/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.scripting/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.scripting/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.se/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.se/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.se/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.security.jgss/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.security.jgss/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.security.jgss/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.security.sasl/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.security.sasl/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.security.sasl/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.smartcardio/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.smartcardio/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.smartcardio/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.smartcardio/pcsclite.md</key>
		<dict>
			<key>hash2</key>
			<data>
			OtTIn1Hgx5FwpuXqquFCnbJXRdvlqE/GGATK7sL+xU8=
			</data>
		</dict>
		<key>Home/legal/java.sql.rowset/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.sql.rowset/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.sql.rowset/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.sql/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.sql/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.sql/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.transaction.xa/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.transaction.xa/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.transaction.xa/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.xml.crypto/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.xml.crypto/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.xml.crypto/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.xml.crypto/santuario.md</key>
		<dict>
			<key>hash2</key>
			<data>
			t3ZLYXMdTulWewkPNNAiN6/PsDd+XRE2x60+80XMSTc=
			</data>
		</dict>
		<key>Home/legal/java.xml/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/java.xml/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/java.xml/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/java.xml/bcel.md</key>
		<dict>
			<key>hash2</key>
			<data>
			hToefOOXuxDeDis73ghEvMZR8X2YPezQfS0APAMEwxE=
			</data>
		</dict>
		<key>Home/legal/java.xml/dom.md</key>
		<dict>
			<key>hash2</key>
			<data>
			Zoboh3ZnWEo6fAc0S6rcoaA+KfZ3Fi2Hw8CBHpkNEUg=
			</data>
		</dict>
		<key>Home/legal/java.xml/jcup.md</key>
		<dict>
			<key>hash2</key>
			<data>
			jV3P31BFWjw0x1OpjyHpUySK8gBBWpCE4/ECy2xDuL8=
			</data>
		</dict>
		<key>Home/legal/java.xml/schema10part1.md</key>
		<dict>
			<key>hash2</key>
			<data>
			siD4keW/o6pLKks036tTopFVPCrg/QsVpMsRFtTpd3A=
			</data>
		</dict>
		<key>Home/legal/java.xml/schema10part2.md</key>
		<dict>
			<key>hash2</key>
			<data>
			I1DjXpGGbV7lvX7nwwOz5CS7yjNdNscUUliKrElOqj4=
			</data>
		</dict>
		<key>Home/legal/java.xml/xalan.md</key>
		<dict>
			<key>hash2</key>
			<data>
			Ar0BP8P0ONu4+kH6V4iBd+AV76ZDHTf85ueUxvNTpqE=
			</data>
		</dict>
		<key>Home/legal/java.xml/xerces.md</key>
		<dict>
			<key>hash2</key>
			<data>
			Smv2s2cZPuaGgcstn+0w/8XWLdLUd71i4CcXB9cbMkQ=
			</data>
		</dict>
		<key>Home/legal/java.xml/xhtml10.md</key>
		<dict>
			<key>hash2</key>
			<data>
			mlzeRwc8qAz1kSeJFCD0rtYD62UHBzitAnuxnnb7kxk=
			</data>
		</dict>
		<key>Home/legal/java.xml/xhtml10schema.md</key>
		<dict>
			<key>hash2</key>
			<data>
			e8UdiEqq5u8nVR7njUBLoxFjVnkNo9BrZlZ56y4cwAI=
			</data>
		</dict>
		<key>Home/legal/java.xml/xhtml11.md</key>
		<dict>
			<key>hash2</key>
			<data>
			58fXDebfcn9ZrwYfJ7BFTGR+j86TPxE4YSlIywbsio8=
			</data>
		</dict>
		<key>Home/legal/java.xml/xhtml11schema.md</key>
		<dict>
			<key>hash2</key>
			<data>
			jNabE5LihlOACPTLAD+Fyuo6N1xhnml4E9ggtphXz3Q=
			</data>
		</dict>
		<key>Home/legal/java.xml/xmlspec.md</key>
		<dict>
			<key>hash2</key>
			<data>
			r+wmO/XcZ2l+4zc07iQ/hqh4FhM172KYJz5AQWc7BUQ=
			</data>
		</dict>
		<key>Home/legal/java.xml/xmlxsd.md</key>
		<dict>
			<key>hash2</key>
			<data>
			SgpYCTbHDuAGg3LEIeja2x7h/uxupkULQ5LJcg85GUs=
			</data>
		</dict>
		<key>Home/legal/jdk.accessibility/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.accessibility/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.accessibility/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.attach/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.attach/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.attach/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.charsets/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.charsets/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.charsets/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.compiler/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.compiler/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.compiler/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.crypto.cryptoki/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.crypto.cryptoki/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.crypto.cryptoki/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.crypto.cryptoki/pkcs11cryptotoken.md</key>
		<dict>
			<key>hash2</key>
			<data>
			Hzb/E0KlgRQshY+QBk4gYz1DUprIKtuFNFvZAqFOGLI=
			</data>
		</dict>
		<key>Home/legal/jdk.crypto.cryptoki/pkcs11wrapper.md</key>
		<dict>
			<key>hash2</key>
			<data>
			Nxl0sfyjdEo4ksfuH8xZO4tCgfwhj0yv0vcJ6d9f2B0=
			</data>
		</dict>
		<key>Home/legal/jdk.crypto.ec/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.crypto.ec/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.crypto.ec/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.dynalink/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.dynalink/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.dynalink/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.dynalink/dynalink.md</key>
		<dict>
			<key>hash2</key>
			<data>
			FzElkcq+4+9sNO2Il9kuTjYbqc6kHsANzWGjIqj8LNs=
			</data>
		</dict>
		<key>Home/legal/jdk.editpad/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.editpad/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.editpad/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.graal.compiler.management/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.graal.compiler.management/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.graal.compiler.management/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.graal.compiler/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.graal.compiler/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.graal.compiler/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.hotspot.agent/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.hotspot.agent/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.hotspot.agent/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.httpserver/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.httpserver/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.httpserver/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.incubator.vector/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.incubator.vector/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.incubator.vector/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.internal.ed/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.internal.ed/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.internal.ed/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.internal.jvmstat/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.internal.jvmstat/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.internal.jvmstat/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.internal.le/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.internal.le/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.internal.le/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.internal.le/jline.md</key>
		<dict>
			<key>hash2</key>
			<data>
			rrZNYKgFZOgRh5n7ALlNZobB/gTaL3OZ+XD5aNMrz4Q=
			</data>
		</dict>
		<key>Home/legal/jdk.internal.md/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.internal.md/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.internal.md/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.internal.md/commonmark.md</key>
		<dict>
			<key>hash2</key>
			<data>
			I6bwghixO22+FkENERBNVtFIYTFt06Bk4fvVBUtvRww=
			</data>
		</dict>
		<key>Home/legal/jdk.internal.opt/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.internal.opt/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.internal.opt/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.internal.opt/jopt-simple.md</key>
		<dict>
			<key>hash2</key>
			<data>
			mbxn+Tz1fW0g5gR3Mck/uyZ9cPvdQRXRGeD4XG7+XAU=
			</data>
		</dict>
		<key>Home/legal/jdk.internal.vm.ci/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.internal.vm.ci/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.internal.vm.ci/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.jartool/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.jartool/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.jartool/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.javadoc/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.javadoc/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.javadoc/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.javadoc/dejavufonts.md</key>
		<dict>
			<key>hash2</key>
			<data>
			vOxVkTyJgiqCkq9Ol6Wtegeqkg28SVJEOf98cGON0eg=
			</data>
		</dict>
		<key>Home/legal/jdk.javadoc/jquery.md</key>
		<dict>
			<key>hash2</key>
			<data>
			to9FTCvViVnIYtlL8PFsP3ii1Tc4jKBg01Q0TbgO5pU=
			</data>
		</dict>
		<key>Home/legal/jdk.javadoc/jqueryUI.md</key>
		<dict>
			<key>hash2</key>
			<data>
			uwoOievYJN9xRRa/ZLkQHGIIHks3bwD5KaWMCVVb8RE=
			</data>
		</dict>
		<key>Home/legal/jdk.jcmd/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.jcmd/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.jcmd/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.jconsole/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.jconsole/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.jconsole/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.jdeps/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.jdeps/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.jdeps/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.jdi/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.jdi/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.jdi/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.jdwp.agent/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.jdwp.agent/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.jdwp.agent/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.jfr/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.jfr/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.jfr/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.jlink/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.jlink/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.jlink/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.jpackage/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.jpackage/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.jpackage/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.jshell/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.jshell/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.jshell/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.jsobject/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.jsobject/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.jsobject/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.jstatd/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.jstatd/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.jstatd/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.localedata/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.localedata/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.localedata/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.localedata/cldr.md</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/cldr.md</string>
		</dict>
		<key>Home/legal/jdk.localedata/thaidict.md</key>
		<dict>
			<key>hash2</key>
			<data>
			wyYUSiNRyWCPpwi119PFo9oD6CtmR5sSjp20lpU5gko=
			</data>
		</dict>
		<key>Home/legal/jdk.management.agent/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.management.agent/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.management.agent/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.management.jfr/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.management.jfr/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.management.jfr/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.management/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.management/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.management/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.naming.dns/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.naming.dns/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.naming.dns/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.naming.rmi/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.naming.rmi/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.naming.rmi/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.net/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.net/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.net/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.nio.mapmode/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.nio.mapmode/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.nio.mapmode/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.sctp/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.sctp/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.sctp/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.security.auth/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.security.auth/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.security.auth/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.security.jgss/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.security.jgss/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.security.jgss/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.unsupported.desktop/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.unsupported.desktop/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.unsupported.desktop/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.unsupported/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.unsupported/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.unsupported/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.xml.dom/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.xml.dom/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.xml.dom/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/legal/jdk.zipfs/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>Home/legal/jdk.zipfs/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>Home/legal/jdk.zipfs/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>Home/lib/classlist</key>
		<dict>
			<key>hash2</key>
			<data>
			zsrDb5kKvk3t1d/jEcd2b4P3lm9dhk3oey5m2gcdub4=
			</data>
		</dict>
		<key>Home/lib/ct.sym</key>
		<dict>
			<key>hash2</key>
			<data>
			U4u9iG2pATfJPpFCl5OxxkoGh7wJoMbAbNAlavmVU44=
			</data>
		</dict>
		<key>Home/lib/fontconfig.bfc</key>
		<dict>
			<key>hash2</key>
			<data>
			VAmVKiGBJsXBHPDkljZlcRruq1D1ZIJk1whm/KcFqVA=
			</data>
		</dict>
		<key>Home/lib/fontconfig.properties.src</key>
		<dict>
			<key>hash2</key>
			<data>
			yNIKSjkkBmUlIwa909vI1x4eJGyM2MeYWzLkEBZYoUg=
			</data>
		</dict>
		<key>Home/lib/jfr/default.jfc</key>
		<dict>
			<key>hash2</key>
			<data>
			cL76oLa1kdYpw5NcoJRK8d8U/TiLNQNIeOaBMoVEe1Q=
			</data>
		</dict>
		<key>Home/lib/jfr/profile.jfc</key>
		<dict>
			<key>hash2</key>
			<data>
			ObLUGnHqyS/HhvWRbxBoX+yasc+f9QYwKUjXBjX1FY0=
			</data>
		</dict>
		<key>Home/lib/jrt-fs.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			2GBlfYLZtpowBOhqyYhbyQEQHoMaPA/gBPu2tmX0SqA=
			</data>
		</dict>
		<key>Home/lib/jspawnhelper</key>
		<dict>
			<key>hash2</key>
			<data>
			UR7WWwq8MAcHCPy9imkeBSfube7hHEaiQfsZTgfwGOc=
			</data>
		</dict>
		<key>Home/lib/jvm.cfg</key>
		<dict>
			<key>hash2</key>
			<data>
			qp77lpREwUhOKa3sq1WhIkWAkGFudmsvEjDvBbw4Z+A=
			</data>
		</dict>
		<key>Home/lib/libattach.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			+VzdCSSXyDapDpkQcv1Svzq9+gM5RPOzjSpWIq8V20o=
			</data>
		</dict>
		<key>Home/lib/libawt.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			Jg2lScieO9kYlVJ8ywOtdSUB3uLXwJn/mDRfBMt6IxM=
			</data>
		</dict>
		<key>Home/lib/libawt_lwawt.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			O7x9MNQV4PIBjHHpreY+nBhBItINhLuvevN0h5TunWg=
			</data>
		</dict>
		<key>Home/lib/libdt_socket.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			AUjWDkf0HZvBPrBIN4Ul8R9xbZ+smx78GVvFvEJiHG8=
			</data>
		</dict>
		<key>Home/lib/libextnet.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			Rov44JoxQmDOjTmudE7AXIr/wMQYtjkdrguYPDYoka4=
			</data>
		</dict>
		<key>Home/lib/libfontmanager.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			RtfZELxHmFX014WDMAyhGcxYTfW3g8mbejAb81fiIhs=
			</data>
		</dict>
		<key>Home/lib/libfreetype.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			1jHZKleWy1Tm7ejMmQ5GhN4GipzcRyyxjA/d6C6elXo=
			</data>
		</dict>
		<key>Home/lib/libinstrument.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			9v2MUisEEiDsiM2WYJpuBh3pWsqKFq0cJvevbTs1N0o=
			</data>
		</dict>
		<key>Home/lib/libj2gss.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			Zd9+KdUv1IL+j/QD/cX7BfETvSoWCHNlbcoDZ43d58I=
			</data>
		</dict>
		<key>Home/lib/libj2pcsc.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			ZSXV6iZwNU0lkrYtN3t6Y96OvAlfISGrP5lp1cdcCQc=
			</data>
		</dict>
		<key>Home/lib/libj2pkcs11.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			teKmJUpkuYMpzpP+Zn4Xl+ovnRdgl/voo5ERu9nXc70=
			</data>
		</dict>
		<key>Home/lib/libjaas.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			htJc5EnqWgekMe2eZ+RIybOaqHLBZSbrTc7qRPFK7ic=
			</data>
		</dict>
		<key>Home/lib/libjava.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			io24aLHMLrEyVTYVLC1hjSy2G5TWMmf2JkFhmwCYoTg=
			</data>
		</dict>
		<key>Home/lib/libjavajpeg.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			f2wSb5T6XVE5QczRkw8O46CpT0sBhUYGH0C1yreMY6I=
			</data>
		</dict>
		<key>Home/lib/libjawt.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			YWwGEhTFdxHdzeUNGNchf+BJKmXtRjr1fG6xqakJGKc=
			</data>
		</dict>
		<key>Home/lib/libjdwp.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			O6LM/T97W/b8Xmo6WfMX2+BgkztZuvrzpm4OJ56bepU=
			</data>
		</dict>
		<key>Home/lib/libjimage.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			LpgQrGbUDnkVxo0MA2eChq8YgrjZs9kZlBEm+yssSKA=
			</data>
		</dict>
		<key>Home/lib/libjli.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			jx+Vj7tqJdY0OxcRdJFQabENlyKbzgZDcaB7zIs3k8Q=
			</data>
		</dict>
		<key>Home/lib/libjsig.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			6Mf03l2lowFhL17oqSiW9SzQF939DK3jFhvC/hrs6AE=
			</data>
		</dict>
		<key>Home/lib/libjsound.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			szh1fxnD4Xu8BzRbcSAC4vSPZ/u/7ouGzb6vORfkufk=
			</data>
		</dict>
		<key>Home/lib/liblcms.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			xLvEo3GcDeBEyioXCBwMDHj/pkTgTMQ27qhSjmorIRE=
			</data>
		</dict>
		<key>Home/lib/libmanagement.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			Mc3eVbJQdiOL1e8H6Cyphh8+JpuKh84W3OL2ewg+8gU=
			</data>
		</dict>
		<key>Home/lib/libmanagement_agent.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			+Za6qJwb7li7+WVSLzplTHGD4pg+6hJGYHghqSkkUHw=
			</data>
		</dict>
		<key>Home/lib/libmanagement_ext.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			h8X6ZZJENtIdAv8tnjz+Ij5jFuqqpnDAYtxXLM+kG8M=
			</data>
		</dict>
		<key>Home/lib/libmlib_image.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			M/XDouVPpytgCZNujUlkedVla5c9MYdToTr1QQMM59M=
			</data>
		</dict>
		<key>Home/lib/libnet.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			nQ+tpL0zEkvqSkc0b+McujgN/nxYctsbE6OQtXT1oVg=
			</data>
		</dict>
		<key>Home/lib/libnio.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			4pjd3PW6BjZAJmAeqdLarB0XzRojJcAg52yyJZejOdk=
			</data>
		</dict>
		<key>Home/lib/libosx.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			+02mQDFyskgJx8TVT0dp3X3/RwjZg89KmAPTmbeLlME=
			</data>
		</dict>
		<key>Home/lib/libosxapp.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			pCS9VzJqu03ZHfXiZbKMdJn5AVnL6lGWeMkJL1w24pk=
			</data>
		</dict>
		<key>Home/lib/libosxkrb5.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			Q9QUhcA8QIFSvvwQUzdCuFif0sYhWuwzV9GFoPpiRuw=
			</data>
		</dict>
		<key>Home/lib/libosxsecurity.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			q6G7WVGbxqz1FO52avl3Oy5j1eRWYQKJICkjC9xudNo=
			</data>
		</dict>
		<key>Home/lib/libosxui.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			HB2kQgdv1UDvufkYZC4eGwxcW1zEo1GSaCwNljZRZrE=
			</data>
		</dict>
		<key>Home/lib/libprefs.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			ZPhiaK/2k/TcSQZ9wWunJhIAKPbpN/D5pIU27y84VdQ=
			</data>
		</dict>
		<key>Home/lib/librmi.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			w2/lwaBcJq8MHZrG2kjJStFpJg3YdQ6TfTlH6Nn6yZ4=
			</data>
		</dict>
		<key>Home/lib/libsaproc.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			qb7UOEaPilNkHyM9My5G1GxZk2uOTXPniYt7nlddTUo=
			</data>
		</dict>
		<key>Home/lib/libsplashscreen.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			Pk+faYpqmw0hDzQE7FrXN6F7xuiZJa8lOSvYl1hBf1w=
			</data>
		</dict>
		<key>Home/lib/libsyslookup.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			P66rhJgJ47n00HG6KJ8wZHM5icgnThUZH2yD1PB7ZlI=
			</data>
		</dict>
		<key>Home/lib/libverify.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			u+ISE2lMW1YpjGz86mI22NG81udcc713MS1da5geLtY=
			</data>
		</dict>
		<key>Home/lib/libzip.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			vjFxA98CzsWpy+Yzp6cgDNjaG66BrPYZD4H/nk27NEc=
			</data>
		</dict>
		<key>Home/lib/modules</key>
		<dict>
			<key>hash2</key>
			<data>
			QxBsSV5hz2EfZWbr6TdNLXboERab7OoIjaTzuoM3WUI=
			</data>
		</dict>
		<key>Home/lib/psfont.properties.ja</key>
		<dict>
			<key>hash2</key>
			<data>
			WkvVG5ab8Yf/htlPSnH9+/pgJ2KXX6PHPSZLRXX3x48=
			</data>
		</dict>
		<key>Home/lib/psfontj2d.properties</key>
		<dict>
			<key>hash2</key>
			<data>
			/dvy6EyrhCJ0XY3QdWrQjgCzPhd6VrqZObkH7HOFJ7A=
			</data>
		</dict>
		<key>Home/lib/security/blocked.certs</key>
		<dict>
			<key>hash2</key>
			<data>
			llcvJD8xwu+BpuYnVC5Zb2qSlc/zx64JXBtZXLFFfe0=
			</data>
		</dict>
		<key>Home/lib/security/cacerts</key>
		<dict>
			<key>hash2</key>
			<data>
			IWjnFluUI9JgXLvyr8FmXOw2vCD/XFSvkdEsOK5V0yc=
			</data>
		</dict>
		<key>Home/lib/security/public_suffix_list.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			WjpXHMLgFv7hDCIQNs8dK1Lui6OSiO8gq+JmeCjKMLQ=
			</data>
		</dict>
		<key>Home/lib/server/classes.jsa</key>
		<dict>
			<key>hash2</key>
			<data>
			wdXRi0315fCvlABvxrz6R4kntbEepX77LwtkgxoxkLM=
			</data>
		</dict>
		<key>Home/lib/server/classes_nocoops.jsa</key>
		<dict>
			<key>hash2</key>
			<data>
			L9j867cfqs4wdXdydCmbE2LCKnCljrF2Xs1u9xQjI0g=
			</data>
		</dict>
		<key>Home/lib/server/libjsig.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			6Mf03l2lowFhL17oqSiW9SzQF939DK3jFhvC/hrs6AE=
			</data>
		</dict>
		<key>Home/lib/server/libjvm.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			Um4fadt1sdxbsqO5srXj9tYQeuagZo6ATmW264VkSJ8=
			</data>
		</dict>
		<key>Home/lib/shaders.metallib</key>
		<dict>
			<key>hash2</key>
			<data>
			1ju6l+MoWRnjAeKiEzejyFF7THmYgzIScLTiM0jrYBs=
			</data>
		</dict>
		<key>Home/lib/src.zip</key>
		<dict>
			<key>hash2</key>
			<data>
			tZwnOIayw+x50nzPxhB1HVcgr8Bv2DCT2j39aI/vFRc=
			</data>
		</dict>
		<key>Home/lib/tzdb.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			hwIuta6UZddXYt5vsqZoxg1BHBOU5QDCRlGJVoEigUg=
			</data>
		</dict>
		<key>Home/man/man1/jabswitch.1</key>
		<dict>
			<key>hash2</key>
			<data>
			Q0RcBdsZynkMArtDZ4S/1AOQuSN4nWEyoiTz5wxGYR0=
			</data>
		</dict>
		<key>Home/man/man1/jaccessinspector.1</key>
		<dict>
			<key>hash2</key>
			<data>
			GTSZ+h+e6XIEkCBktuNTJyulmSSVPO2P2rOJ2WxUSFE=
			</data>
		</dict>
		<key>Home/man/man1/jaccesswalker.1</key>
		<dict>
			<key>hash2</key>
			<data>
			KEM8jSlkpIOHwh/c3ln6wIudWLapskudwL9FVxpnP3I=
			</data>
		</dict>
		<key>Home/man/man1/jar.1</key>
		<dict>
			<key>hash2</key>
			<data>
			4XzJcw/7RxNW+7x6BfggD767oh+RDseN39NH1zgcbwo=
			</data>
		</dict>
		<key>Home/man/man1/jarsigner.1</key>
		<dict>
			<key>hash2</key>
			<data>
			RsoBCKfYVg+MQkPzmcNrPMBMEfY8wnZOEMzknf+uz3Q=
			</data>
		</dict>
		<key>Home/man/man1/java.1</key>
		<dict>
			<key>hash2</key>
			<data>
			DChn1DOJj8DG1TpQl1p3XL4xj2bQNtZoR6YG5XyYRBU=
			</data>
		</dict>
		<key>Home/man/man1/javac.1</key>
		<dict>
			<key>hash2</key>
			<data>
			05aDex6Pg6Pb5Q5n3rXBZXPcSEWhzljZ5jkVMi/Wyl0=
			</data>
		</dict>
		<key>Home/man/man1/javadoc.1</key>
		<dict>
			<key>hash2</key>
			<data>
			Xy5yjwrZ/DQb/p71x8ciVxmCJ/JrAZTo+fZkCOUDz4k=
			</data>
		</dict>
		<key>Home/man/man1/javap.1</key>
		<dict>
			<key>hash2</key>
			<data>
			IkTXVgWOVy16LaD11f1Q06Ps58skNS7ea69Q490LCvs=
			</data>
		</dict>
		<key>Home/man/man1/jcmd.1</key>
		<dict>
			<key>hash2</key>
			<data>
			6vmMq3sBeU0Py/JT4IV7DBaU/OWCRmpL6ezRCZN4y9o=
			</data>
		</dict>
		<key>Home/man/man1/jconsole.1</key>
		<dict>
			<key>hash2</key>
			<data>
			B5nnQIaQVU218Qsdz3lyMVSd1HgXmo45a2xpuX5mlb4=
			</data>
		</dict>
		<key>Home/man/man1/jdb.1</key>
		<dict>
			<key>hash2</key>
			<data>
			ck3A/wp31kaX9JKqp9TLVmK0fhdT5cKCINfqmgm4wCs=
			</data>
		</dict>
		<key>Home/man/man1/jdeprscan.1</key>
		<dict>
			<key>hash2</key>
			<data>
			iuFxYOFOpk2TJk9go9dlFpuKLTnRM7OmHGWULgRoec8=
			</data>
		</dict>
		<key>Home/man/man1/jdeps.1</key>
		<dict>
			<key>hash2</key>
			<data>
			PEXxFs79N5kq4f5s2dhEO6t3FdxQ44NvLolj2VMdXys=
			</data>
		</dict>
		<key>Home/man/man1/jfr.1</key>
		<dict>
			<key>hash2</key>
			<data>
			lh8mL4VujdVlmKWvD/qGN/Iqe/4ofNKmm3MTC7Uwg6A=
			</data>
		</dict>
		<key>Home/man/man1/jhsdb.1</key>
		<dict>
			<key>hash2</key>
			<data>
			W5OYQil5yVtkPgEvByg47dVZFKI8glykOXc4Xh5dMBc=
			</data>
		</dict>
		<key>Home/man/man1/jinfo.1</key>
		<dict>
			<key>hash2</key>
			<data>
			oFq4J+ve6AgXBGxckYU7msS/7JJMZCdbxii1iavut68=
			</data>
		</dict>
		<key>Home/man/man1/jlink.1</key>
		<dict>
			<key>hash2</key>
			<data>
			1LfWnUa217iagQsyyxB7SBsNI8LKFMMHlBYsxv7vulA=
			</data>
		</dict>
		<key>Home/man/man1/jmap.1</key>
		<dict>
			<key>hash2</key>
			<data>
			8hZPTyoht0wE7luC7WYQ0xjXjZCICgbD5+qSscwCpsQ=
			</data>
		</dict>
		<key>Home/man/man1/jmod.1</key>
		<dict>
			<key>hash2</key>
			<data>
			LLXgaiAuAHGLHwIlqX+TzMJQ1heS+R5gtzMtpEGHBtk=
			</data>
		</dict>
		<key>Home/man/man1/jnativescan.1</key>
		<dict>
			<key>hash2</key>
			<data>
			GLpBBNOdk/WVDW85oxf3iSkxSlCmNXAnQUDXgAcy94U=
			</data>
		</dict>
		<key>Home/man/man1/jpackage.1</key>
		<dict>
			<key>hash2</key>
			<data>
			yJq1tcQHj94+RpOS+8I1YXwiovyVCQg019O9296+Kyc=
			</data>
		</dict>
		<key>Home/man/man1/jps.1</key>
		<dict>
			<key>hash2</key>
			<data>
			tlkT9yxkBNYSEz+LZsjiBfqykmw89pDwwGMx9fbBaZ0=
			</data>
		</dict>
		<key>Home/man/man1/jrunscript.1</key>
		<dict>
			<key>hash2</key>
			<data>
			Qoma/F5HE0erepCgx0qv+hjhQC/w3wmmdqVvYhXeeS0=
			</data>
		</dict>
		<key>Home/man/man1/jshell.1</key>
		<dict>
			<key>hash2</key>
			<data>
			OG8vhSq0/KEA4L+eQ4IooO0jJfrbDG0StRtPxmqY+bw=
			</data>
		</dict>
		<key>Home/man/man1/jstack.1</key>
		<dict>
			<key>hash2</key>
			<data>
			rPSviV0FBc5cPOa0SuuWc4iAqvrYWC7S9iM02haEBSM=
			</data>
		</dict>
		<key>Home/man/man1/jstat.1</key>
		<dict>
			<key>hash2</key>
			<data>
			hR+WYFWpkmc2CuKCUXyJz0yU05RAAB+i7Q7g/3wy7YM=
			</data>
		</dict>
		<key>Home/man/man1/jstatd.1</key>
		<dict>
			<key>hash2</key>
			<data>
			C5KrF8J1WKE7xt6/OwiqgKK1ARQl/8KysF7OXKALF2c=
			</data>
		</dict>
		<key>Home/man/man1/jwebserver.1</key>
		<dict>
			<key>hash2</key>
			<data>
			ikuqW3j24HGV7JsA4JHGuEq+Ciek2KUUItlWugFdd+0=
			</data>
		</dict>
		<key>Home/man/man1/keytool.1</key>
		<dict>
			<key>hash2</key>
			<data>
			NiLpObphDlUmvw89zxXkhdVtxes2PVX/Q1FSOeNteU0=
			</data>
		</dict>
		<key>Home/man/man1/kinit.1</key>
		<dict>
			<key>hash2</key>
			<data>
			nQqWdcPZyZWDyWssnkntoACcEg3qQrLxPJpNRfze3HU=
			</data>
		</dict>
		<key>Home/man/man1/klist.1</key>
		<dict>
			<key>hash2</key>
			<data>
			QfUdTU2QIFUb7XgeZI1pwiNZ2Etv+/bFMGCYzW5nDW0=
			</data>
		</dict>
		<key>Home/man/man1/ktab.1</key>
		<dict>
			<key>hash2</key>
			<data>
			szaNi0lrKfhr2vh9ZO6sdOvkwJkdSix1Rafj9taF188=
			</data>
		</dict>
		<key>Home/man/man1/rmiregistry.1</key>
		<dict>
			<key>hash2</key>
			<data>
			9Nvf74MtaRMJrt3XsVhd19P3yzk4UC4yYJMz5iZfHOA=
			</data>
		</dict>
		<key>Home/man/man1/serialver.1</key>
		<dict>
			<key>hash2</key>
			<data>
			Nyo1cAKJU7XQ1oXfK4eiza5nXeoizufG8r8QYEsG2sc=
			</data>
		</dict>
		<key>Home/release</key>
		<dict>
			<key>hash2</key>
			<data>
			YAx6t3vZ4XIoHuTgsa+v0ScSP2OE12OpqKnTpD5ifzg=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
