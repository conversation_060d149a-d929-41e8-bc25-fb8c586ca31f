.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JCMD" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jcmd - send diagnostic command requests to a running Java Virtual
Machine (JVM)
.SH SYNOPSIS
.PP
\f[V]jcmd\f[R] [\f[I]pid\f[R] | \f[I]main-class\f[R]]
\f[I]command\f[R]...
| \f[V]PerfCounter.print\f[R] | \f[V]-f\f[R] \f[I]filename\f[R]
.PP
\f[V]jcmd\f[R] [\f[V]-l\f[R]]
.PP
\f[V]jcmd\f[R] \f[V]-h\f[R]
.TP
\f[I]pid\f[R]
When used, the \f[V]jcmd\f[R] utility sends the diagnostic command
request to the process ID for the Java process.
.TP
\f[I]main-class\f[R]
When used, the \f[V]jcmd\f[R] utility sends the diagnostic command
request to all Java processes with the specified name of the main class.
.TP
\f[I]command\f[R]
The \f[V]command\f[R] must be a valid \f[V]jcmd\f[R] command for the
selected JVM.
The list of available commands for \f[V]jcmd\f[R] is obtained by running
the \f[V]help\f[R] command (\f[V]jcmd\f[R] \f[I]pid\f[R] \f[V]help\f[R])
where \f[I]pid\f[R] is the process ID for the running Java process.
.TP
\f[V]Perfcounter.print\f[R]
Prints the performance counters exposed by the specified Java process.
.TP
\f[V]-f\f[R] \f[I]filename\f[R]
Reads and executes commands from a specified file, \f[I]filename\f[R].
.TP
\f[V]-l\f[R]
Displays the list of Java Virtual Machine process identifiers that are
not running in a separate docker process along with the main class and
command-line arguments that were used to launch the process.
If the JVM is in a docker process, you must use tools such as
\f[V]ps\f[R] to look up the PID.
.RS
.PP
\f[B]Note:\f[R]
.PP
Using \f[V]jcmd\f[R] without arguments is the same as using
\f[V]jcmd -l\f[R].
.RE
.TP
\f[V]-h\f[R]
Displays the \f[V]jcmd\f[R] utility\[aq]s command-line help.
.SH DESCRIPTION
.PP
The \f[V]jcmd\f[R] utility is used to send diagnostic command requests
to the JVM.
It must be used on the same machine on which the JVM is running, and
have the same effective user and group identifiers that were used to
launch the JVM.
Each diagnostic command has its own set of options and arguments.
To display the description, syntax, and a list of available options and
arguments for a diagnostic command, use the name of the command as the
argument.
For example:
.RS
.PP
\f[V]jcmd\f[R] \f[I]pid\f[R] \f[V]help\f[R] \f[I]command\f[R]
.RE
.PP
If arguments contain spaces, then you must surround them with single or
double quotation marks (\f[V]\[aq]\f[R] or \f[V]\[dq]\f[R]).
In addition, you must escape single or double quotation marks with a
backslash (\f[V]\[rs]\f[R]) to prevent the operating system shell from
processing quotation marks.
Alternatively, you can surround these arguments with single quotation
marks and then with double quotation marks (or with double quotation
marks and then with single quotation marks).
.PP
If you specify the process identifier (\f[I]pid\f[R]) or the main class
(\f[I]main-class\f[R]) as the first argument, then the \f[V]jcmd\f[R]
utility sends the diagnostic command request to the Java process with
the specified identifier or to all Java processes with the specified
name of the main class.
You can also send the diagnostic command request to all available Java
processes by specifying \f[V]0\f[R] as the process identifier.
.SH COMMANDS FOR JCMD
.PP
The \f[I]command\f[R] must be a valid \f[V]jcmd\f[R] diagnostic command
for the selected JVM.
The list of available commands for \f[V]jcmd\f[R] is obtained by running
the \f[V]help\f[R] command (\f[V]jcmd\f[R] \f[I]pid\f[R] \f[V]help\f[R])
where \f[I]pid\f[R] is the process ID for a running Java process.
If the \f[I]pid\f[R] is \f[V]0\f[R], commands will be sent to all Java
processes.
The main class argument will be used to match, either partially or
fully, the class used to start Java.
If no options are given, it lists the running Java process identifiers
that are not in separate docker processes along with the main class and
command-line arguments that were used to launch the process (the same as
using \f[V]-l\f[R]).
.PP
\f[V]jcmd\f[R] \f[I]commands\f[R] may take options and arguments.
\f[I]Options\f[R] are specified using either \f[I]key\f[R] or
\f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
\f[I]Arguments\f[R] are given as just a value, never name=value.
.PP
The following commands are available:
.TP
\f[V]help\f[R] [\f[I]options\f[R]] [\f[I]arguments\f[R]]
For more information about a specific command.
.RS
.PP
\f[I]arguments\f[R]:
.IP \[bu] 2
\f[I]command name\f[R]: The name of the command for which we want help
(STRING, no default value)
.PP
\f[B]Note:\f[R]
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]-all\f[R]: (Optional) Show help for all commands (BOOLEAN, false) .
.RE
.TP
\f[V]Compiler.CodeHeap_Analytics\f[R] [\f[I]function\f[R]] [\f[I]granularity\f[R]]
Print CodeHeap analytics
.RS
.PP
Impact: Low: Depends on code heap size and content.
Holds CodeCache_lock during analysis step, usually sub-second duration.
.PP
\f[I]arguments\f[R]:
.IP \[bu] 2
\f[I]function\f[R]: (Optional) Function to be performed (aggregate,
UsedSpace, FreeSpace, MethodCount, MethodSpace, MethodAge, MethodNames,
discard (STRING, all)
.IP \[bu] 2
\f[I]granularity\f[R]: (Optional) Detail level - smaller value -> more
detail (INT, 4096)
.RE
.TP
\f[V]Compiler.codecache\f[R]
Prints code cache layout and bounds.
.RS
.PP
Impact: Low
.RE
.TP
\f[V]Compiler.codelist\f[R]
Prints all compiled methods in code cache that are alive.
.RS
.PP
Impact: Medium
.RE
.TP
\f[V]Compiler.directives_add\f[R] \f[I]arguments\f[R]
Adds compiler directives from a file.
.RS
.PP
Impact: Low
.PP
\f[I]arguments\f[R]:
.IP \[bu] 2
\f[I]filename\f[R]: The name of the directives file (STRING, no default
value)
.RE
.TP
\f[V]Compiler.directives_clear\f[R]
Remove all compiler directives.
.RS
.PP
Impact: Low
.RE
.TP
\f[V]Compiler.directives_print\f[R]
Prints all active compiler directives.
.RS
.PP
Impact: Low
.RE
.TP
\f[V]Compiler.directives_remove\f[R]
Remove latest added compiler directive.
.RS
.PP
Impact: Low
.RE
.TP
\f[V]Compiler.memory\f[R] [\f[I]options\f[R]]
Print compilation footprint
.RS
.PP
Impact: Medium: Pause time depends on number of compiled methods
.PP
\f[B]Note:\f[R]
.PP
The \f[I]options\f[R] must be specified using either \f[I]key\f[R] or
\f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]-H\f[R]: (Optional) Human readable format (BOOLEAN, false)
.IP \[bu] 2
\f[V]-s\f[R]: (Optional) Minimum memory size (MEMORY SIZE, 0)
.RE
.TP
\f[V]Compiler.perfmap\f[R] [\f[I]arguments\f[R]] (Linux only)
Write map file for Linux perf tool.
.RS
.PP
Impact: Low
.PP
\f[I]arguments\f[R]:
.IP \[bu] 2
\f[I]filename\f[R]: (Optional) The name of the map file.
If %p is specified in the filename, it is expanded to the JVM\[aq]s PID.
(FILE, \[dq]/tmp/perf-%p.map\[dq])
.RE
.TP
\f[V]Compiler.queue\f[R]
Prints methods queued for compilation.
.RS
.PP
Impact: Low
.RE
.TP
\f[V]GC.class_histogram\f[R] [\f[I]options\f[R]]
Provides statistics about the Java heap usage.
.RS
.PP
Impact: High --- depends on Java heap size and content.
.PP
\f[B]Note:\f[R]
.PP
The \f[I]options\f[R] must be specified using either \f[I]key\f[R] or
\f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]-all\f[R]: (Optional) Inspects all objects, including unreachable
objects (BOOLEAN, false)
.IP \[bu] 2
\f[V]-parallel\f[R]: (Optional) Number of parallel threads to use for
heap inspection.
0 (the default) means let the VM determine the number of threads to use.
1 means use one thread (disable parallelism).
For any other value the VM will try to use the specified number of
threads, but might use fewer.
(INT, 0)
.RE
.TP
\f[V]GC.finalizer_info\f[R]
Provides information about the Java finalization queue.
.RS
.PP
Impact: Medium
.RE
.TP
\f[V]GC.heap_dump\f[R] [\f[I]options\f[R]] \f[I]filename\f[R]
Generates a HPROF format dump of the Java heap.
.RS
.PP
Impact: High --- depends on the Java heap size and content.
Request a full GC unless the \f[V]-all\f[R] option is specified.
.PP
\f[B]Note:\f[R]
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]-all\f[R]: (Optional) Dump all objects, including unreachable
objects (BOOLEAN, false)
.IP \[bu] 2
\f[V]-gz\f[R]: (Optional) If specified, the heap dump is written in
gzipped format using the given compression level.
1 (recommended) is the fastest, 9 the strongest compression.
(INT, 1)
.IP \[bu] 2
\f[V]-overwrite\f[R]: (Optional) If specified, the dump file will be
overwritten if it exists (BOOLEAN, false)
.IP \[bu] 2
\f[V]-parallel\f[R]: (Optional) Number of parallel threads to use for
heap dump.
The VM will try to use the specified number of threads, but might use
fewer.
(INT, 1)
.PP
\f[I]arguments\f[R]:
.IP \[bu] 2
\f[I]filename\f[R]: The name of the dump file.
If %p is specified in the filename, it is expanded to the JVM\[aq]s PID.
(FILE, no default value)
.RE
.TP
\f[V]GC.heap_info\f[R]
Provides generic Java heap information.
.RS
.PP
Impact: Medium
.RE
.TP
\f[V]GC.run\f[R]
Calls \f[V]java.lang.System.gc()\f[R].
.RS
.PP
Impact: Medium --- depends on the Java heap size and content.
.RE
.TP
\f[V]GC.run_finalization\f[R]
Calls \f[V]java.lang.System.runFinalization()\f[R].
.RS
.PP
Impact: Medium --- depends on the Java content.
.RE
.TP
\f[V]JFR.check\f[R] [\f[I]options\f[R]]
Show information about a running flight recording
.RS
.PP
Impact: Low
.PP
\f[B]Note:\f[R]
.PP
The \f[I]options\f[R] must be specified using either \f[I]key\f[R] or
\f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
If no parameters are entered, information for all active recordings is
shown.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]name\f[R]: (Optional) Name of the flight recording.
(STRING, no default value)
.IP \[bu] 2
\f[V]verbose\f[R]: (Optional) Flag for printing the event settings for
the recording (BOOLEAN, false)
.RE
.TP
\f[V]JFR.configure\f[R] [\f[I]options\f[R]]
Set the parameters for a flight recording
.RS
.PP
Impact: Low
.PP
\f[B]Note:\f[R]
.PP
The \f[I]options\f[R] must be specified using either \f[I]key\f[R] or
\f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
If no parameters are entered, the current settings are displayed.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]dumppath\f[R]: (Optional) Path to the location where a recording
file is written in case the VM runs into a critical error, such as a
system crash.
(STRING, The default location is the current directory)
.IP \[bu] 2
\f[V]globalbuffercount\f[R]: (Optional) Number of global buffers.
This option is a legacy option: change the \f[V]memorysize\f[R]
parameter to alter the number of global buffers.
This value cannot be changed once JFR has been initialized.
(STRING, default determined by the value for \f[V]memorysize\f[R])
.IP \[bu] 2
\f[V]globalbuffersize\f[R]: (Optional) Size of the global buffers, in
bytes.
This option is a legacy option: change the \f[V]memorysize\f[R]
parameter to alter the size of the global buffers.
This value cannot be changed once JFR has been initialized.
(STRING, default determined by the value for \f[V]memorysize\f[R])
.IP \[bu] 2
\f[V]maxchunksize\f[R]: (Optional) Maximum size of an individual data
chunk in bytes if one of the following suffixes is not used: \[aq]m\[aq]
or \[aq]M\[aq] for megabytes OR \[aq]g\[aq] or \[aq]G\[aq] for
gigabytes.
This value cannot be changed once JFR has been initialized.
(STRING, 12M)
.IP \[bu] 2
\f[V]memorysize\f[R]: (Optional) Overall memory size, in bytes if one of
the following suffixes is not used: \[aq]m\[aq] or \[aq]M\[aq] for
megabytes OR \[aq]g\[aq] or \[aq]G\[aq] for gigabytes.
This value cannot be changed once JFR has been initialized.
(STRING, 10M)
.IP \[bu] 2
\f[V]repositorypath\f[R]: (Optional) Path to the location where
recordings are stored until they are written to a permanent file.
(STRING, The default location is the temporary directory for the
operating system.
On Linux operating systems, the temporary directory is \f[V]/tmp\f[R].
On Windwows, the temporary directory is specified by the \f[V]TMP\f[R]
environment variable.)
.IP \[bu] 2
\f[V]preserve-repository=\f[R]{\f[V]true\f[R]|\f[V]false\f[R]} :
Specifies whether files stored in the disk repository should be kept
after the JVM has exited.
If false, files are deleted.
By default, this parameter is disabled.
.IP \[bu] 2
\f[V]stackdepth\f[R]: (Optional) Stack depth for stack traces.
Setting this value greater than the default of 64 may cause a
performance degradation.
This value cannot be changed once JFR has been initialized.
(LONG, 64)
.IP \[bu] 2
\f[V]thread_buffer_size\f[R]: (Optional) Local buffer size for each
thread in bytes if one of the following suffixes is not used:
\[aq]k\[aq] or \[aq]K\[aq] for kilobytes or \[aq]m\[aq] or \[aq]M\[aq]
for megabytes.
Overriding this parameter could reduce performance and is not
recommended.
This value cannot be changed once JFR has been initialized.
(STRING, 8k)
.IP \[bu] 2
\f[V]samplethreads\f[R]: (Optional) Flag for activating thread sampling.
(BOOLEAN, true)
.RE
.TP
\f[V]JFR.dump\f[R] [\f[I]options\f[R]]
Write data to a file while a flight recording is running
.RS
.PP
Impact: Low
.PP
\f[B]Note:\f[R]
.PP
The \f[I]options\f[R] must be specified using either \f[I]key\f[R] or
\f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
No options are required.
The recording continues to run after the data is written.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]begin\f[R]: (Optional) Specify the time from which recording data
will be included in the dump file.
The format is specified as local time.
(STRING, no default value)
.IP \[bu] 2
\f[V]end\f[R]: (Optional) Specify the time to which recording data will
be included in the dump file.
The format is specified as local time.
(STRING, no default value)
.RS 2
.PP
\f[B]Note:\f[R] For both \f[V]begin\f[R] and \f[V]end\f[R], the time
must be in a format that can be read by
java.time.LocalTime::parse(STRING),
java.time.LocalDateTime::parse(STRING) or
java.time.Instant::parse(STRING).
For example, \[dq]13:20:15\[dq], \[dq]2020-03-17T09:00:00\[dq] or
\[dq]2020-03-17T09:00:00Z\[dq].
.PP
\f[B]Note:\f[R] \f[V]begin\f[R] and \f[V]end\f[R] times correspond to
the timestamps found within the recorded information in the flight
recording data.
.PP
Another option is to use a time relative to the current time that is
specified by a negative integer followed by \[dq]s\[dq], \[dq]m\[dq] or
\[dq]h\[dq].
For example, \[dq]-12h\[dq], \[dq]-15m\[dq] or \[dq]-30s\[dq]
.RE
.IP \[bu] 2
\f[V]filename\f[R]: (Optional) Name of the file to which the flight
recording data is dumped.
If no filename is given, a filename is generated from the PID and the
current date.
The filename may also be a directory in which case, the filename is
generated from the PID and the current date in the specified directory.
If %p and/or %t is specified in the filename, it expands to the
JVM\[aq]s PID and the current timestamp, respectively.
(FILE, no default value)
.IP \[bu] 2
\f[V]maxage\f[R]: (Optional) Length of time for dumping the flight
recording data to a file.
(INTEGER followed by \[aq]s\[aq] for seconds \[aq]m\[aq] for minutes or
\[aq]h\[aq] for hours, no default value)
.IP \[bu] 2
\f[V]maxsize\f[R]: (Optional) Maximum size for the amount of data to
dump from a flight recording in bytes if one of the following suffixes
is not used: \[aq]m\[aq] or \[aq]M\[aq] for megabytes OR \[aq]g\[aq] or
\[aq]G\[aq] for gigabytes.
(STRING, no default value)
.IP \[bu] 2
\f[V]name\f[R]: (Optional) Name of the recording.
If no name is given, data from all recordings is dumped.
(STRING, no default value)
.IP \[bu] 2
\f[V]path-to-gc-roots\f[R]: (Optional) Flag for saving the path to
garbage collection (GC) roots at the time the recording data is dumped.
The path information is useful for finding memory leaks but collecting
it can cause the application to pause for a short period of time.
Turn on this flag only when you have an application that you suspect has
a memory leak.
(BOOLEAN, false)
.RE
.TP
\f[V]JFR.start\f[R] [\f[I]options\f[R]]
Start a flight recording
.RS
.PP
Impact: Low
.PP
\f[B]Note:\f[R]
.PP
The \f[I]options\f[R] must be specified using either \f[I]key\f[R] or
\f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
If no parameters are entered, then a recording is started with default
values.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]delay\f[R]: (Optional) Length of time to wait before starting to
record (INTEGER followed by \[aq]s\[aq] for seconds \[aq]m\[aq] for
minutes or \[aq]h\[aq] for hours, 0s)
.IP \[bu] 2
\f[V]disk\f[R]: (Optional) Flag for also writing the data to disk while
recording (BOOLEAN, true)
.IP \[bu] 2
\f[V]dumponexit\f[R]: (Optional) Flag for writing the recording to disk
when the Java Virtual Machine (JVM) shuts down.
If set to \[aq]true\[aq] and no value is given for \f[V]filename\f[R],
the recording is written to a file in the directory where the process
was started.
The file name is a system-generated name that contains the process ID,
the recording ID and the current time stamp.
(For example: \f[V]id-1-2019_12_12_10_41.jfr\f[R]) (BOOLEAN, false)
.IP \[bu] 2
\f[V]duration\f[R]: (Optional) Length of time to record.
Note that \f[V]0s\f[R] means forever (INTEGER followed by \[aq]s\[aq]
for seconds \[aq]m\[aq] for minutes or \[aq]h\[aq] for hours, 0s)
.IP \[bu] 2
\f[V]filename\f[R]: (Optional) Name of the file to which the flight
recording data is written when the recording is stopped.
If no filename is given, a filename is generated from the PID and the
current date and is placed in the directory where the process was
started.
The filename may also be a directory in which case, the filename is
generated from the PID and the current date in the specified directory.
If %p and/or %t is specified in the filename, it expands to the
JVM\[aq]s PID and the current timestamp, respectively.
(FILE, no default value)
.IP \[bu] 2
\f[V]maxage\f[R]: (Optional) Maximum time to keep the recorded data on
disk.
This parameter is valid only when the \f[V]disk\f[R] parameter is set to
\f[V]true\f[R].
Note \f[V]0s\f[R] means forever.
(INTEGER followed by \[aq]s\[aq] for seconds \[aq]m\[aq] for minutes or
\[aq]h\[aq] for hours, 0s)
.IP \[bu] 2
\f[V]maxsize\f[R]: (Optional) Maximum size of the data to keep on disk
in bytes if one of the following suffixes is not used: \[aq]m\[aq] or
\[aq]M\[aq] for megabytes OR \[aq]g\[aq] or \[aq]G\[aq] for gigabytes.
This parameter is valid only when the \f[V]disk\f[R] parameter is set to
\[aq]true\[aq].
The value must not be less than the value for the \f[V]maxchunksize\f[R]
parameter set with the \f[V]JFR.configure\f[R] command.
(STRING, 0 (no maximum size))
.IP \[bu] 2
\f[V]name\f[R]: (Optional) Name of the recording.
If no name is provided, a name is generated.
Make note of the generated name that is shown in the response to the
command so that you can use it with other commands.
(STRING, system-generated default name)
.IP \[bu] 2
\f[V]path-to-gc-roots\f[R]: (Optional) Flag for saving the path to
garbage collection (GC) roots at the end of a recording.
The path information is useful for finding memory leaks but collecting
it is time consuming.
Turn on this flag only when you have an application that you suspect has
a memory leak.
If the \f[V]settings\f[R] parameter is set to \[aq]profile\[aq], then
the information collected includes the stack trace from where the
potential leaking object was allocated.
(BOOLEAN, false)
.IP \[bu] 2
\f[V]settings\f[R]: (Optional) Name of the settings file that identifies
which events to record.
To specify more than one file, separate the names with a comma
(\[aq],\[aq]).
Include the path if the file is not in \f[V]JAVA-HOME\f[R]/lib/jfr.
The following profiles are included with the JDK in the
\f[V]JAVA-HOME\f[R]/lib/jfr directory: \[aq]default.jfc\[aq]: collects a
predefined set of information with low overhead, so it has minimal
impact on performance and can be used with recordings that run
continuously; \[aq]profile.jfc\[aq]: Provides more data than the
\[aq]default.jfc\[aq] profile, but with more overhead and impact on
performance.
Use this configuration for short periods of time when more information
is needed.
Use \f[V]none\f[R] to start a recording without a predefined
configuration file.
(STRING, \f[V]JAVA-HOME\f[R]/lib/jfr/default.jfc)
.PP
Event settings and .jfc options can be specified using the following
syntax:
.IP \[bu] 2
\f[V]option\f[R]: (Optional) Specifies the option value to modify.
To list available options, use the \f[V]JAVA_HOME\f[R]/bin/jfr tool.
.IP \[bu] 2
\f[V]event-setting\f[R]: (Optional) Specifies the event setting value to
modify.
Use the form: \f[V]<event-name>#<setting-name>=<value>\f[R] To add a new
event setting, prefix the event name with \[aq]+\[aq].
.PP
You can specify values for multiple event settings and .jfc options by
separating them with a whitespace.
In case of a conflict between a parameter and a .jfc option, the
parameter will take precedence.
The whitespace delimiter can be omitted for timespan values, i.e.
20ms.
For more information about the settings syntax, see Javadoc of the
jdk.jfr package.
.RE
.TP
\f[V]JFR.stop\f[R] [\f[I]options\f[R]]
Stop a flight recording
.RS
.PP
Impact: Low
.PP
\f[B]Note:\f[R]
.PP
The \f[I]options\f[R] must be specified using either \f[I]key\f[R] or
\f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
If no parameters are entered, then no recording is stopped.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]filename\f[R]: (Optional) Name of the file to which the recording
is written when the recording is stopped.
If %p and/or %t is specified in the filename, it expands to the
JVM\[aq]s PID and the current timestamp, respectively.
If no path is provided, the data from the recording is discarded.
(FILE, no default value)
.IP \[bu] 2
\f[V]name\f[R]: (Optional) Name of the recording (STRING, no default
value)
.RE
.TP
\f[V]JFR.view\f[R] [\f[I]options\f[R]]
Display event data in predefined views.
.RS
.PP
Impact: Medium
.PP
\f[B]Note:\f[R]
.PP
The \f[I]options\f[R] must be specified using either \f[I]key\f[R] or
\f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
If no parameters are entered, then a list of available views are
displayed.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]cell-height\f[R]: (Optional) Maximum number of rows in a table
cell.
(INT, default value depends on the view)
.IP \[bu] 2
\f[V]maxage\f[R]: (Optional) Length of time for the view to span.
(INT followed by \[aq]s\[aq] for seconds \[aq]m\[aq] for minutes or
\[aq]h\[aq] for hours, default value is 10m)
.IP \[bu] 2
\f[V]maxsize\f[R]: (Optional) Maximum size for the view to span in bytes
if one of the following suffixes is not used: \[aq]m\[aq] or \[aq]M\[aq]
for megabytes OR \[aq]g\[aq] or \[aq]G\[aq] for gigabytes.
(STRING, default value is 32MB)
.IP \[bu] 2
\f[V]truncate\f[R]: (Optional) Maximum number of rows in a table cell.
(INT, default value depends on the view)
.IP \[bu] 2
\f[V]verbose\f[R]: (Optional) Displays the query that makes up the view.
(BOOLEAN, default value is false)
.IP \[bu] 2
\f[V]width\f[R]: (Optional) The width of the view in characters.
(INT, default value depends on the view)
.PP
\f[I]arguments\f[R]:
.IP \[bu] 2
\f[V]view\f[R]: Name of the view or event type to display.
Use \f[V]help JFR.view\f[R] to see a list of available views.
(STRING, no default value)
.PP
The view parameter can be an event type name.
Use \f[V]JFR.view types\f[R] to see a list.
To display all views, use \f[V]JFR.view all-views\f[R].
To display all events, use \f[V]JFR.view all-events\f[R].
.RE
.TP
\f[V]JVMTI.agent_load\f[R] [\f[I]arguments\f[R]]
Loads JVMTI native agent.
.RS
.PP
Impact: Low
.PP
\f[I]arguments\f[R]:
.IP \[bu] 2
\f[I]library path\f[R]: Absolute path of the JVMTI agent to load.
(STRING, no default value)
.IP \[bu] 2
\f[I]agent option\f[R]: (Optional) Option string to pass the agent.
(STRING, no default value)
.RE
.TP
\f[V]JVMTI.data_dump\f[R]
Signal the JVM to do a data-dump request for JVMTI.
.RS
.PP
Impact: High
.RE
.TP
\f[V]ManagementAgent.start\f[R] [\f[I]options\f[R]]
Starts remote management agent.
.RS
.PP
Impact: Low --- no impact
.PP
\f[B]Note:\f[R]
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]config.file\f[R]: (Optional) Sets
\f[V]com.sun.management.config.file\f[R] (STRING, no default value)
.IP \[bu] 2
\f[V]jmxremote.host\f[R]: (Optional) Sets
\f[V]com.sun.management.jmxremote.host\f[R] (STRING, no default value)
.IP \[bu] 2
\f[V]jmxremote.port\f[R]: (Optional) Sets
\f[V]com.sun.management.jmxremote.port\f[R] (STRING, no default value)
.IP \[bu] 2
\f[V]jmxremote.rmi.port\f[R]: (Optional) Sets
\f[V]com.sun.management.jmxremote.rmi.port\f[R] (STRING, no default
value)
.IP \[bu] 2
\f[V]jmxremote.ssl\f[R]: (Optional) Sets
\f[V]com.sun.management.jmxremote.ssl\f[R] (STRING, no default value)
.IP \[bu] 2
\f[V]jmxremote.registry.ssl\f[R]: (Optional) Sets
\f[V]com.sun.management.jmxremote.registry.ssl\f[R] (STRING, no default
value)
.IP \[bu] 2
\f[V]jmxremote.authenticate\f[R]: (Optional) Sets
\f[V]com.sun.management.jmxremote.authenticate\f[R] (STRING, no default
value)
.IP \[bu] 2
jmxremote.password.file: (Optional) Sets
\f[V]com.sun.management.jmxremote.password.file\f[R] (STRING, no default
value)
.IP \[bu] 2
\f[V]jmxremote.access.file\f[R]: (Optional) Sets
\f[V]com.sun.management.jmxremote.acce ss.file\f[R] (STRING, no default
value)
.IP \[bu] 2
\f[V]jmxremote.login.config\f[R]: (Optional) Sets
\f[V]com.sun.management.jmxremote.log in.config\f[R] (STRING, no default
value)
.IP \[bu] 2
\f[V]jmxremote.ssl.enabled.cipher.suites\f[R]: (Optional) Sets
\f[V]com.sun.management\f[R].
.IP \[bu] 2
\f[V]jmxremote.ssl.enabled.cipher.suite\f[R]: (STRING, no default value)
.IP \[bu] 2
\f[V]jmxremote.ssl.enabled.protocols\f[R]: (Optional) Sets
\f[V]com.sun.management.jmxr emote.ssl.enabled.protocols\f[R] (STRING,
no default value)
.IP \[bu] 2
\f[V]jmxremote.ssl.need.client.auth\f[R]: (Optional) Sets
\f[V]com.sun.management.jmxre mote.need.client.auth\f[R] (STRING, no
default value)
.IP \[bu] 2
\f[V]jmxremote.ssl.config.file\f[R]: (Optional) Sets
\f[V]com.sun.management.jmxremote. ssl_config_file\f[R] (STRING, no
default value)
.IP \[bu] 2
\f[V]jmxremote.autodiscovery\f[R]: (Optional) Sets
\f[V]com.sun.management.jmxremote.au todiscovery\f[R] (STRING, no
default value)
.IP \[bu] 2
\f[V]jdp.port\f[R]: (Optional) Sets
\f[V]com.sun.management.jdp.port\f[R] (INT, no default value)
.IP \[bu] 2
\f[V]jdp.address\f[R]: (Optional) Sets
\f[V]com.sun.management.jdp.address\f[R] (STRING, no default value)
.IP \[bu] 2
\f[V]jdp.source_addr\f[R]: (Optional) Sets
\f[V]com.sun.management.jdp.source_addr\f[R] (STRING, no default value)
.IP \[bu] 2
\f[V]jdp.ttl\f[R]: (Optional) Sets \f[V]com.sun.management.jdp.ttl\f[R]
(INT, no default value)
.IP \[bu] 2
\f[V]jdp.pause\f[R]: (Optional) Sets
\f[V]com.sun.management.jdp.pause\f[R] (INT, no default value)
.IP \[bu] 2
\f[V]jdp.name\f[R]: (Optional) Sets
\f[V]com.sun.management.jdp.name\f[R] (STRING, no default value)
.RE
.TP
\f[V]ManagementAgent.start_local\f[R]
Starts the local management agent.
.RS
.PP
Impact: Low --- no impact
.RE
.TP
\f[V]ManagementAgent.status\f[R]
Print the management agent status.
.RS
.PP
Impact: Low --- no impact
.RE
.TP
\f[V]ManagementAgent.stop\f[R]
Stops the remote management agent.
.RS
.PP
Impact: Low --- no impact
.RE
.TP
\f[V]System.dump_map\f[R] [\f[I]options\f[R]] (Linux only)
Dumps an annotated process memory map to an output file.
.RS
.PP
Impact: Low
.PP
\f[B]Note:\f[R]
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]-H\f[R]: (Optional) Human readable format (BOOLEAN, false)
.IP \[bu] 2
\f[V]-F\f[R]: (Optional) File path.
If %p is specified in the filename, it is expanded to the JVM\[aq]s PID.
(FILE, \[dq]vm_memory_map_%p.txt\[dq])
.RE
.TP
\f[V]System.map\f[R] [\f[I]options\f[R]] (Linux only)
Prints an annotated process memory map of the VM process.
.RS
.PP
Impact: Low
.PP
\f[B]Note:\f[R]
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]-H\f[R]: (Optional) Human readable format (BOOLEAN, false)
.RE
.TP
\f[V]System.native_heap_info\f[R] (Linux only)
Attempts to output information regarding native heap usage through
malloc_info(3).
If unsuccessful outputs \[dq]Error: \[dq] and a reason.
.RS
.PP
Impact: Low
.RE
.TP
\f[V]System.trim_native_heap\f[R] (Linux only)
Attempts to free up memory by trimming the C-heap.
.RS
.PP
Impact: Low
.RE
.TP
\f[V]Thread.dump_to_file\f[R] [\f[I]options\f[R]] \f[I]filepath\f[R]
Dump threads, with stack traces, to a file in plain text or JSON format.
.RS
.PP
Impact: Medium: Depends on the number of threads.
.PP
\f[B]Note:\f[R]
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]-overwrite\f[R]: (Optional) May overwrite existing file (BOOLEAN,
false)
.IP \[bu] 2
\f[V]-format\f[R]: (Optional) Output format (\[dq]plain\[dq] or
\[dq]json\[dq]) (STRING, plain)
.PP
\f[I]arguments\f[R]:
.IP \[bu] 2
\f[I]filepath\f[R]: The file path to the output file.
If %p is specified in the filename, it is expanded to the JVM\[aq]s PID.
(FILE, no default value)
.RE
.TP
\f[V]Thread.print\f[R] [\f[I]options\f[R]]
Prints all threads with stacktraces.
.RS
.PP
Impact: Medium --- depends on the number of threads.
.PP
\f[B]Note:\f[R]
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]-e\f[R]: (Optional) Print extended thread information (BOOLEAN,
false)
.IP \[bu] 2
\f[V]-l\f[R]: (Optional) Prints \f[V]java.util.concurrent\f[R] locks
(BOOLEAN, false)
.RE
.TP
\f[V]Thread.vthread_scheduler\f[R]
Print the virtual thread scheduler, and the delayed task schedulers that
support virtual threads doing timed operations.
.RS
.PP
Impact: Low
.RE
.TP
\f[V]Thread.vthread_pollers\f[R]
Print the I/O pollers that support virtual threads doing blocking
network I/O operations.
.RS
.PP
Impact: Low
.RE
.TP
\f[V]VM.cds\f[R] [\f[I]arguments\f[R]]
Dump a static or dynamic shared archive that includes all currently
loaded classes.
.RS
.PP
Impact: Medium --- pause time depends on number of loaded classes
.PP
\f[I]arguments\f[R]:
.IP \[bu] 2
\f[I]subcmd\f[R]: must be either \f[V]static_dump\f[R] or
\f[V]dynamic_dump\f[R] (STRING, no default value)
.IP \[bu] 2
\f[I]filename\f[R]: (Optional) Name of the shared archive to be dumped.
If %p is specified in the filename, it is expanded to the JVM\[aq]s PID.
(FILE, \[dq]java_pid%p_<subcmd>.jsa\[dq])
.PP
If \f[V]dynamic_dump\f[R] is specified, the target JVM must be started
with the JVM option \f[V]-XX:+RecordDynamicDumpInfo\f[R].
.RE
.TP
\f[V]VM.class_hierarchy\f[R] [\f[I]options\f[R]] [\f[I]arguments\f[R]]
Print a list of all loaded classes, indented to show the class
hierarchy.
The name of each class is followed by the ClassLoaderData* of its
ClassLoader, or \[dq]null\[dq] if it is loaded by the bootstrap class
loader.
.RS
.PP
Impact: Medium --- depends on the number of loaded classes.
.PP
\f[B]Note:\f[R]
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]-i\f[R]: (Optional) Inherited interfaces should be printed.
(BOOLEAN, false)
.IP \[bu] 2
\f[V]-s\f[R]: (Optional) If a classname is specified, print its
subclasses in addition to its superclasses.
Without this option only the superclasses will be printed.
(BOOLEAN, false)
.PP
\f[I]arguments\f[R]:
.IP \[bu] 2
\f[I]classname\f[R]: (Optional) The name of the class whose hierarchy
should be printed.
If not specified, all class hierarchies are printed.
(STRING, no default value)
.RE
.TP
\f[V]VM.classes\f[R] [\f[I]options\f[R]]
Print all loaded classes
.RS
.PP
Impact: Medium: Depends on number of loaded classes.
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]-verbose\f[R]: (Optional) Dump the detailed content of a Java
class.
Some classes are annotated with flags: \f[V]F\f[R] = has, or inherits, a
non-empty finalize method, \f[V]f\f[R] = has final method, \f[V]W\f[R] =
methods rewritten, \f[V]C\f[R] = marked with \f[V]\[at]Contended\f[R]
annotation, \f[V]R\f[R] = has been redefined, \f[V]S\f[R] = is shared
class (BOOLEAN, false)
.RE
.TP
\f[V]VM.classloader_stats\f[R]
Print statistics about all ClassLoaders.
.RS
.PP
Impact: Low
.RE
.TP
\f[V]VM.classloaders\f[R] [\f[I]options\f[R]]
Prints classloader hierarchy.
.RS
.PP
Impact: Medium --- Depends on number of class loaders and classes
loaded.
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]show-classes\f[R]: (Optional) Print loaded classes.
(BOOLEAN, false)
.IP \[bu] 2
\f[V]verbose\f[R]: (Optional) Print detailed information.
(BOOLEAN, false)
.IP \[bu] 2
\f[V]fold\f[R]: (Optional) Show loaders of the same name and class as
one.
(BOOLEAN, true)
.RE
.TP
\f[V]VM.command_line\f[R]
Print the command line used to start this VM instance.
.RS
.PP
Impact: Low
.RE
.TP
\f[V]VM.dynlibs\f[R]
Print loaded dynamic libraries.
.RS
.PP
Impact: Low
.RE
.TP
\f[V]VM.events\f[R] [\f[I]options\f[R]]
Print VM event logs
.RS
.PP
Impact: Low --- Depends on event log size.
.PP
\f[I]options\f[R]:
.PP
\f[B]Note:\f[R]
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.IP \[bu] 2
\f[V]log\f[R]: (Optional) Name of log to be printed.
If omitted, all logs are printed.
(STRING, no default value)
.IP \[bu] 2
\f[V]max\f[R]: (Optional) Maximum number of events to be printed (newest
first).
If omitted or zero, all events are printed.
(INT, 0)
.RE
.TP
\f[V]VM.flags\f[R] [\f[I]options\f[R]]
Print the VM flag options and their current values.
.RS
.PP
Impact: Low
.PP
\f[B]Note:\f[R]
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]-all\f[R]: (Optional) Prints all flags supported by the VM
(BOOLEAN, false).
.RE
.TP
\f[V]VM.info\f[R]
Print information about the JVM environment and status.
.RS
.PP
Impact: Low
.RE
.TP
\f[V]VM.log\f[R] [\f[I]options\f[R]]
Lists current log configuration, enables/disables/configures a log
output, or rotates all logs.
.RS
.PP
Impact: Low
.PP
\f[I]options\f[R]:
.PP
\f[B]Note:\f[R]
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.IP \[bu] 2
\f[V]output\f[R]: (Optional) The name or index (#) of output to
configure.
(STRING, no default value)
.IP \[bu] 2
\f[V]output_options\f[R]: (Optional) Options for the output.
(STRING, no default value)
.IP \[bu] 2
\f[V]what\f[R]: (Optional) Configures what tags to log.
(STRING, no default value )
.IP \[bu] 2
\f[V]decorators\f[R]: (Optional) Configures which decorators to use.
Use \[aq]none\[aq] or an empty value to remove all.
(STRING, no default value)
.IP \[bu] 2
\f[V]disable\f[R]: (Optional) Turns off all logging and clears the log
configuration.
(BOOLEAN, no default value)
.IP \[bu] 2
\f[V]list\f[R]: (Optional) Lists current log configuration.
(BOOLEAN, no default value)
.IP \[bu] 2
\f[V]rotate\f[R]: (Optional) Rotates all logs.
(BOOLEAN, no default value)
.RE
.TP
\f[V]VM.metaspace\f[R] [\f[I]options\f[R]]
Prints the statistics for the metaspace
.RS
.PP
Impact: Medium --- Depends on number of classes loaded.
.PP
\f[B]Note:\f[R]
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]basic\f[R]: (Optional) Prints a basic summary (does not need a
safepoint).
(BOOLEAN, false)
.IP \[bu] 2
\f[V]show-loaders\f[R]: (Optional) Shows usage by class loader.
(BOOLEAN, false)
.IP \[bu] 2
\f[V]show-classes\f[R]: (Optional) If show-loaders is set, shows loaded
classes for each loader.
(BOOLEAN, false)
.IP \[bu] 2
\f[V]by-chunktype\f[R]: (Optional) Break down numbers by chunk type.
(BOOLEAN, false)
.IP \[bu] 2
\f[V]by-spacetype\f[R]: (Optional) Break down numbers by loader type.
(BOOLEAN, false)
.IP \[bu] 2
\f[V]vslist\f[R]: (Optional) Shows details about the underlying virtual
space.
(BOOLEAN, false)
.IP \[bu] 2
\f[V]chunkfreelist\f[R]: (Optional) Shows details about global chunk
free lists (ChunkManager).
(BOOLEAN, false)
.IP \[bu] 2
\f[V]scale\f[R]: (Optional) Memory usage in which to scale.
Valid values are: 1, KB, MB or GB (fixed scale) or \[dq]dynamic\[dq] for
a dynamically chosen scale.
(STRING, dynamic)
.RE
.TP
\f[V]VM.native_memory\f[R] [\f[I]options\f[R]]
Print native memory usage
.RS
.PP
Impact: Medium
.PP
\f[B]Note:\f[R]
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]summary\f[R]: (Optional) Requests runtime to report current memory
summary, which includes total reserved and committed memory, along with
memory usage summary by each subsystem.
(BOOLEAN, false)
.IP \[bu] 2
\f[V]detail\f[R]: (Optional) Requests runtime to report memory
allocation >= 1K by each callsite.
(BOOLEAN, false)
.IP \[bu] 2
\f[V]baseline\f[R]: (Optional) Requests runtime to baseline current
memory usage, so it can be compared against in later time.
(BOOLEAN, false)
.IP \[bu] 2
\f[V]summary.diff\f[R]: (Optional) Requests runtime to report memory
summary comparison against previous baseline.
(BOOLEAN, false)
.IP \[bu] 2
\f[V]detail.diff\f[R]: (Optional) Requests runtime to report memory
detail comparison against previous baseline, which shows the memory
allocation activities at different callsites.
(BOOLEAN, false)
.IP \[bu] 2
\f[V]statistics\f[R]: (Optional) Prints tracker statistics for tuning
purpose.
(BOOLEAN, false)
.IP \[bu] 2
\f[V]scale\f[R]: (Optional) Memory usage in which scale, KB, MB or GB
(STRING, KB)
.RE
.TP
\f[V]VM.set_flag\f[R] [\f[I]arguments\f[R]]
Sets VM flag option using the provided value.
.RS
.PP
Impact: Low
.PP
\f[I]arguments\f[R]:
.IP \[bu] 2
\f[I]flag name\f[R]: The name of the flag that you want to set (STRING,
no default value)
.IP \[bu] 2
\f[I]string value\f[R]: (Optional) The value that you want to set
(STRING, no default value)
.RE
.TP
\f[V]VM.stringtable\f[R] [\f[I]options\f[R]]
Dump string table.
.RS
.PP
Impact: Medium --- depends on the Java content.
.PP
\f[B]Note:\f[R]
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]-verbose\f[R]: (Optional) Dumps the content of each string in the
table (BOOLEAN, false)
.RE
.TP
\f[V]VM.symboltable\f[R] [\f[I]options\f[R]]
Dump symbol table.
.RS
.PP
Impact: Medium --- depends on the Java content.
.PP
\f[B]Note:\f[R]
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax).
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]-verbose\f[R]: (Optional) Dumps the content of each symbol in the
table (BOOLEAN, false)
.RE
.TP
\f[V]VM.system_properties\f[R]
Print system properties.
.RS
.PP
Impact: Low
.RE
.TP
\f[V]VM.systemdictionary\f[R]
Prints the statistics for dictionary hashtable sizes and bucket length.
.RS
.PP
Impact: Medium
.PP
\f[B]Note:\f[R]
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]verbose\f[R]: (Optional) Dump the content of each dictionary entry
for all class loaders (BOOLEAN, false) .
.RE
.TP
\f[V]VM.uptime\f[R] [\f[I]options\f[R]]
Print VM uptime.
.RS
.PP
Impact: Low
.PP
\f[B]Note:\f[R]
.PP
The following \f[I]options\f[R] must be specified using either
\f[I]key\f[R] or \f[I]key\f[R]\f[V]=\f[R]\f[I]value\f[R] syntax.
.PP
\f[I]options\f[R]:
.IP \[bu] 2
\f[V]-date\f[R]: (Optional) Adds a prefix with the current date
(BOOLEAN, false)
.RE
.TP
\f[V]VM.version\f[R]
Print JVM version information.
.RS
.PP
Impact: Low
.RE
