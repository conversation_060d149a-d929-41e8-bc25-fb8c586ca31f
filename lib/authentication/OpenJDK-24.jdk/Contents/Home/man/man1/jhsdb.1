.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JHSDB" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jhsdb - attach to a Java process or launch a postmortem debugger to
analyze the content of a core dump from a crashed Java Virtual Machine
(JVM)
.SH SYNOPSIS
.PP
\f[B]WARNING:\f[R] The \f[V]debugd\f[R] subcommand and
\f[V]--connect\f[R] options are deprecated.
They will be removed in a future release.
.PP
\f[V]jhsdb\f[R] \f[V]clhsdb\f[R] [\f[V]--pid\f[R] \f[I]pid\f[R] |
\f[V]--exe\f[R] \f[I]executable\f[R] \f[V]--core\f[R]
\f[I]coredump\f[R]]
.PP
\f[V]jhsdb\f[R] \f[V]hsdb\f[R] [\f[V]--pid\f[R] \f[I]pid\f[R] |
\f[V]--exe\f[R] \f[I]executable\f[R] \f[V]--core\f[R]
\f[I]coredump\f[R]]
.PP
\f[V]jhsdb\f[R] \f[V]debugd\f[R] (\f[V]--pid\f[R] \f[I]pid\f[R] |
\f[V]--exe\f[R] \f[I]executable\f[R] \f[V]--core\f[R]
\f[I]coredump\f[R]) [\f[I]options\f[R]]
.PP
\f[V]jhsdb\f[R] \f[V]jstack\f[R] (\f[V]--pid\f[R] \f[I]pid\f[R] |
\f[V]--exe\f[R] \f[I]executable\f[R] \f[V]--core\f[R] \f[I]coredump\f[R]
| \f[V]--connect\f[R] \f[I][server-id\[at]]debugd-host\f[R])
[\f[I]options\f[R]]
.PP
\f[V]jhsdb\f[R] \f[V]jmap\f[R] (\f[V]--pid\f[R] \f[I]pid\f[R] |
\f[V]--exe\f[R] \f[I]executable\f[R] \f[V]--core\f[R] \f[I]coredump\f[R]
| \f[V]--connect\f[R] \f[I][server-id\[at]]debugd-host\f[R])
[\f[I]options\f[R]]
.PP
\f[V]jhsdb\f[R] \f[V]jinfo\f[R] (\f[V]--pid\f[R] \f[I]pid\f[R] |
\f[V]--exe\f[R] \f[I]executable\f[R] \f[V]--core\f[R] \f[I]coredump\f[R]
| \f[V]--connect\f[R] \f[I][server-id\[at]]debugd-host\f[R])
[\f[I]options\f[R]]
.PP
\f[V]jhsdb\f[R] \f[V]jsnap\f[R] (\f[V]--pid\f[R] \f[I]pid\f[R] |
\f[V]--exe\f[R] \f[I]executable\f[R] \f[V]--core\f[R] \f[I]coredump\f[R]
| \f[V]--connect\f[R] \f[I][server-id\[at]]debugd-host\f[R])
[\f[I]options\f[R]]
.TP
\f[I]pid\f[R]
The process ID to which the \f[V]jhsdb\f[R] tool should attach.
The process must be a Java process.
To get a list of Java processes running on a machine, use the
\f[V]ps\f[R] command or, if the JVM processes are not running in a
separate docker instance, the \f[B]jps\f[R] command.
.TP
\f[I]executable\f[R]
The Java executable file from which the core dump was produced.
.TP
\f[I]coredump\f[R]
The core file to which the \f[V]jhsdb\f[R] tool should attach.
.TP
\f[I][server-id\[at]]debugd-host\f[R]
An optional server ID and the address of the remote debug server
(debugd).
.TP
\f[I]options\f[R]
The command-line options for a \f[V]jhsdb\f[R] mode.
See \f[B]Options for the debugd Mode\f[R], \f[B]Options for the jstack
Mode\f[R], \f[B]Options for the jmap Mode\f[R], \f[B]Options for the
jinfo Mode\f[R], and \f[B]Options for the jsnap Mode\f[R].
.PP
\f[B]Note:\f[R]
.PP
Either the \f[I]pid\f[R] or the pair of \f[I]executable\f[R] and
\f[I]core\f[R] files or the \f[I][server-id\[at]]debugd-host\f[R] must
be provided for \f[V]debugd\f[R], \f[V]jstack\f[R], \f[V]jmap\f[R],
\f[V]jinfo\f[R] and \f[V]jsnap\f[R] modes.
.SH DESCRIPTION
.PP
You can use the \f[V]jhsdb\f[R] tool to attach to a Java process or to
launch a postmortem debugger to analyze the content of a core-dump from
a crashed Java Virtual Machine (JVM).
This command is experimental and unsupported.
.PP
\f[B]Note:\f[R]
.PP
Attaching the \f[V]jhsdb\f[R] tool to a live process will cause the
process to hang and the process will probably crash when the debugger
detaches.
.PP
The \f[V]jhsdb\f[R] tool can be launched in any one of the following
modes:
.TP
\f[V]jhsdb clhsdb\f[R]
Starts the interactive command-line debugger.
.TP
\f[V]jhsdb hsdb\f[R]
Starts the interactive GUI debugger.
.TP
\f[V]jhsdb debugd\f[R]
Starts the remote debug server.
.TP
\f[V]jhsdb jstack\f[R]
Prints stack and locks information.
.TP
\f[V]jhsdb jmap\f[R]
Prints heap information.
.TP
\f[V]jhsdb jinfo\f[R]
Prints basic JVM information.
.TP
\f[V]jhsdb jsnap\f[R]
Prints performance counter information.
.TP
\f[V]jhsdb\f[R] \f[I]command\f[R] \f[V]--help\f[R]
Displays the options available for the \f[I]command\f[R].
.SH OPTIONS FOR THE DEBUGD MODE
.TP
\f[V]--serverid\f[R] \f[I]server-id\f[R]
An optional unique ID for this debug server.
This is required if multiple debug servers are run on the same machine.
.TP
\f[V]--rmiport\f[R] \f[I]port\f[R]
Sets the port number to which the RMI connector is bound.
If not specified a random available port is used.
.TP
\f[V]--registryport\f[R] \f[I]port\f[R]
Sets the RMI registry port.
This option overrides the system property
\[aq]sun.jvm.hotspot.rmi.port\[aq].
If not specified, the system property is used.
If the system property is not set, the default port 1099 is used.
.TP
\f[V]--hostname\f[R] \f[I]hostname\f[R]
Sets the hostname the RMI connector is bound.
The value could be a hostname or an IPv4/IPv6 address.
This option overrides the system property
\[aq]java.rmi.server.hostname\[aq].
If not specified, the system property is used.
If the system property is not set, a system hostname is used.
.SH OPTIONS FOR THE JINFO MODE
.TP
\f[V]--flags\f[R]
Prints the VM flags.
.TP
\f[V]--sysprops\f[R]
Prints the Java system properties.
.TP
no option
Prints the VM flags and the Java system properties.
.SH OPTIONS FOR THE JMAP MODE
.TP
no option
Prints the same information as Solaris \f[V]pmap\f[R].
.TP
\f[V]--heap\f[R]
Prints the \f[V]java\f[R] heap summary.
.TP
\f[V]--binaryheap\f[R]
Dumps the \f[V]java\f[R] heap in \f[V]hprof\f[R] binary format.
.TP
\f[V]--dumpfile\f[R] \f[I]name\f[R]
The name of the dumpfile.
.TP
\f[V]--histo\f[R]
Prints the histogram of \f[V]java\f[R] object heap.
.TP
\f[V]--clstats\f[R]
Prints the class loader statistics.
.TP
\f[V]--finalizerinfo\f[R]
Prints the information on objects awaiting finalization.
.SH OPTIONS FOR THE JSTACK MODE
.TP
\f[V]--locks\f[R]
Prints the \f[V]java.util.concurrent\f[R] locks information.
.TP
\f[V]--mixed\f[R]
Attempts to print both \f[V]java\f[R] and native frames if the platform
allows it.
.SH OPTIONS FOR THE JSNAP MODE
.TP
\f[V]--all\f[R]
Prints all performance counters.
