.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JDEPRSCAN" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jdeprscan - static analysis tool that scans a jar file (or some other
aggregation of class files) for uses of deprecated API elements
.SH SYNOPSIS
.PP
\f[V]jdeprscan\f[R] [\f[I]options\f[R]]
{\f[I]dir\f[R]|\f[I]jar\f[R]|\f[I]class\f[R]}
.TP
\f[I]options\f[R]
See \f[B]Options for the jdeprscan Command\f[R]
.TP
\f[I]dir\f[R]|\f[I]jar\f[R]|\f[I]class\f[R]
\f[V]jdeprscan\f[R] command scans each argument for usages of deprecated
APIs.
The arguments can be a:
.RS
.IP \[bu] 2
\f[I]dir\f[R]: Directory
.IP \[bu] 2
\f[I]jar\f[R]: JAR file
.IP \[bu] 2
\f[I]class\f[R]: Class name or class file
.PP
The class name should use a dot (\f[V].\f[R]) as a separator.
For example:
.PP
\f[V]java.lang.Thread\f[R]
.PP
For nested classes, the dollar sign \f[V]$\f[R] separator character
should be used.
For example:
.PP
\f[V]java.lang.Thread$State\f[R]
.PP
A class file can also be named.
For example:
.PP
\f[V]build/classes/java/lang/Thread$State.class\f[R]
.RE
.SH DESCRIPTION
.PP
The \f[V]jdeprscan\f[R] tool is a static analysis tool provided by the
JDK that scans a JAR file or some other aggregation of class files for
uses of deprecated API elements.
The deprecated APIs identified by the \f[V]jdeprscan\f[R] tool are only
those that are defined by Java SE.
Deprecated APIs defined by third-party libraries aren\[aq]t reported.
.PP
To scan a JAR file or a set of class files, you must first ensure that
all of the classes that the scanned classes depend upon are present in
the class path.
Set the class path using the \f[V]--class-path\f[R] option described in
\f[B]Options for the jdeprscan Command\f[R].
Typically, you would use the same class path as the one that you use
when invoking your application.
.PP
If the \f[V]jdeprscan\f[R] can\[aq]t find all the dependent classes, it
will generate an error message for each class that\[aq]s missing.
These error messages are typically of the form:
.RS
.PP
\f[V]error: cannot find class ...\f[R]
.RE
.PP
If these errors occur, then you must adjust the class path so that it
includes all dependent classes.
.SH OPTIONS FOR THE JDEPRSCAN COMMAND
.PP
The following options are available:
.TP
\f[V]--class-path\f[R] \f[I]path\f[R]
Provides a search path for resolution of dependent classes.
.RS
.PP
\f[I]path\f[R] can be a search path that consists of one or more
directories separated by the system-specific path separator.
For example:
.IP \[bu] 2
\f[B]Linux and macOS:\f[R]
.RS 2
.RS
.PP
\f[V]--class-path /some/directory:/another/different/dir\f[R]
.RE
.RE
.PP
\f[B]Note:\f[R]
.PP
On Windows, use a semicolon (\f[V];\f[R]) as the separator instead of a
colon (\f[V]:\f[R]).
.IP \[bu] 2
\f[B]Windows:\f[R]
.RS 2
.RS
.PP
\f[V]--class-path \[rs]some\[rs]directory;\[rs]another\[rs]different\[rs]dir\f[R]
.RE
.RE
.RE
.TP
\f[V]--for-removal\f[R]
Limits scanning or listing to APIs that are deprecated for removal.
Can\[aq]t be used with a release value of 6, 7, or 8.
.TP
\f[V]--full-version\f[R]
Prints out the full version string of the tool.
.TP
\f[V]--help\f[R] or \f[V]-h\f[R]
Prints out a full help message.
.TP
\f[V]--list\f[R] or \f[V]-l\f[R]
Prints the set of deprecated APIs.
No scanning is done, so no directory, jar, or class arguments should be
provided.
.TP
\f[V]--release\f[R] \f[V]6\f[R]|\f[V]7\f[R]|\f[V]8\f[R]|\f[V]9\f[R]
Specifies the Java SE release that provides the set of deprecated APIs
for scanning.
.TP
\f[V]--verbose\f[R] or \f[V]-v\f[R]
Enables additional message output during processing.
.TP
\f[V]--version\f[R]
Prints out the abbreviated version string of the tool.
.SH EXAMPLE OF JDEPRSCAN OUTPUT
.PP
The JAR file for this library will be named something similar to
\f[V]commons-math3-3.6.1.jar\f[R].
To scan this JAR file for the use of deprecated APIs, run the following
command:
.RS
.PP
\f[V]jdeprscan commons-math3-3.6.1.jar\f[R]
.RE
.PP
This command produces several lines of output.
For example, one line of output might be:
.IP
.nf
\f[CB]
class org/apache/commons/math3/util/MathUtils uses deprecated method java/lang/Double::<init>(D)V
\f[R]
.fi
.PP
\f[B]Note:\f[R]
.PP
The class name is specified using the slash-separated binary name as
described in JVMS 4.2.1.
This is the form used internally in class files.
.PP
The deprecated API it uses is a method on the \f[V]java.lang.Double\f[R]
class.
.PP
The name of the deprecated method is \f[V]<init>\f[R], which is a
special name that means that the method is actually a constructor.
Another special name is \f[V]<clinit>\f[R], which indicates a class
static initializer.
.PP
Other methods are listed just by their method name.
Following the method name is the argument list and return type:
.RS
.PP
\f[V](D)V\f[R]
.RE
.PP
This indicates that it takes just one double value (a primitive) and
returns void.
The argument and return types can become cryptic.
For example, another line of output might be:
.IP
.nf
\f[CB]
class org/apache/commons/math3/util/Precision uses deprecated method java/math/BigDecimal::setScale(II)Ljava/math/BigDecimal;
\f[R]
.fi
.PP
In this line of output, the deprecated method is on class
\f[V]java.math.BigDecimal\f[R], and the method is \f[V]setScale()\f[R].
In this case, the \f[V](II)\f[R] means that it takes two \f[V]int\f[R]
arguments.
The \f[V]Ljava/math/BigDecimal;\f[R] after the parentheses means that it
returns a reference to \f[V]java.math.BigDecimal\f[R].
.SH JDEPRSCAN ANALYSIS CAN BE VERSION-SPECIFIC
.PP
You can use \f[V]jdeprscan\f[R] relative to the previous three JDK
releases.
For example, if you are running JDK 9, then you can check against JDK 8,
7, and 6.
.PP
As an example, look at this code snippet:
.IP
.nf
\f[CB]
public class Deprecations {
   SecurityManager sm = new RMISecurityManager();    // deprecated in 8
   Boolean b2 = new Boolean(true);          // deprecated in 9
}
\f[R]
.fi
.PP
The complete class compiles without warnings in JDK 7.
.PP
If you run \f[V]jdeprscan\f[R] on a system with JDK 9, then you see:
.IP
.nf
\f[CB]
$ jdeprscan --class-path classes --release 7 example.Deprecations
(no output)
\f[R]
.fi
.PP
Run \f[V]jdeprscan\f[R] with a release value of 8:
.IP
.nf
\f[CB]
$ jdeprscan --class-path classes --release 8 example.Deprecations
class example/Deprecations uses type java/rmi/RMISecurityManager deprecated
class example/Deprecations uses method in type java/rmi/RMISecurityManager deprecated
\f[R]
.fi
.PP
Run \f[V]jdeprscan\f[R] on JDK 9:
.IP
.nf
\f[CB]
$ jdeprscan --class-path classes example.Deprecations
class example/Deprecations uses type java/rmi/RMISecurityManager deprecated
class example/Deprecations uses method in type java/rmi/RMISecurityManager deprecated
class example/Deprecations uses method java/lang/Boolean <init> (Z)V deprecated
\f[R]
.fi
