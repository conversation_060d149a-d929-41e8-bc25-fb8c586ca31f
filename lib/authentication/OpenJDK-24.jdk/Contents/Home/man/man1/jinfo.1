.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JINFO" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jinfo - generate Java configuration information for a specified Java
process
.SH SYNOPSIS
.PP
\f[B]Note:\f[R] This command is experimental and unsupported.
.PP
\f[V]jinfo\f[R] [\f[I]option\f[R]] \f[I]pid\f[R]
.TP
\f[I]option\f[R]
This represents the \f[V]jinfo\f[R] command-line options.
See \f[B]Options for the jinfo Command\f[R].
.TP
\f[I]pid\f[R]
The process ID for which the configuration information is to be printed.
The process must be a Java process.
To get a list of Java processes running on a machine, use either the
\f[V]ps\f[R] command or, if the JVM processes are not running in a
separate docker instance, the \f[B]jps\f[R] command.
.SH DESCRIPTION
.PP
The \f[V]jinfo\f[R] command prints Java configuration information for a
specified Java process.
The configuration information includes Java system properties and JVM
command-line flags.
If the specified process is running on a 64-bit JVM, then you might need
to specify the \f[V]-J-d64\f[R] option, for example:
.RS
.PP
\f[V]jinfo -J-d64 -sysprops\f[R] \f[I]pid\f[R]
.RE
.PP
This command is unsupported and might not be available in future
releases of the JDK.
In Windows Systems where \f[V]dbgeng.dll\f[R] is not present, the
Debugging Tools for Windows must be installed to have these tools work.
The \f[V]PATH\f[R] environment variable should contain the location of
the \f[V]jvm.dll\f[R] that\[aq]s used by the target process or the
location from which the core dump file was produced.
.SH OPTIONS FOR THE JINFO COMMAND
.PP
\f[B]Note:\f[R]
.PP
If none of the following options are used, both the command-line flags
and the system property name-value pairs are printed.
.TP
\f[V]-flag\f[R] \f[I]name\f[R]
Prints the name and value of the specified command-line flag.
.TP
\f[V]-flag\f[R] [\f[V]+\f[R]|\f[V]-\f[R]]\f[I]name\f[R]
Enables or disables the specified Boolean command-line flag.
.TP
\f[V]-flag\f[R] \f[I]name\f[R]\f[V]=\f[R]\f[I]value\f[R]
Sets the specified command-line flag to the specified value.
.TP
\f[V]-flags\f[R]
Prints command-line flags passed to the JVM.
.TP
\f[V]-sysprops\f[R]
Prints Java system properties as name-value pairs.
.TP
\f[V]-h\f[R] or \f[V]-help\f[R]
Prints a help message.
