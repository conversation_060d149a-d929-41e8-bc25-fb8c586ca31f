.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JLINK" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jlink - assemble and optimize a set of modules and their dependencies
into a custom runtime image
.SH SYNOPSIS
.PP
\f[V]jlink\f[R] [\f[I]options\f[R]] \f[V]--module-path\f[R]
\f[I]modulepath\f[R] \f[V]--add-modules\f[R]
\f[I]module\f[R][,\f[I]module\f[R]...]
.TP
\f[I]options\f[R]
Command-line options separated by spaces.
See \f[B]jlink Options\f[R].
.TP
\f[I]modulepath\f[R]
The path where the \f[V]jlink\f[R] tool discovers observable modules.
These modules can be modular JAR files, JMOD files, or exploded modules.
.TP
\f[I]module\f[R]
The names of the modules to add to the runtime image.
The \f[V]jlink\f[R] tool adds these modules and their transitive
dependencies.
.SH DESCRIPTION
.PP
The \f[V]jlink\f[R] tool links a set of modules, along with their
transitive dependences, to create a custom runtime image.
.PP
\f[B]Note:\f[R]
.PP
Developers are responsible for updating their custom runtime images.
.SH JLINK OPTIONS
.TP
\f[V]--add-modules\f[R] \f[I]mod\f[R][\f[V],\f[R]\f[I]mod\f[R]...]
Adds the named modules, \f[I]mod\f[R], to the default set of root
modules.
The default set of root modules is empty.
.TP
\f[V]--bind-services\f[R]
Link service provider modules and their dependencies.
.TP
\f[V]-c ={0|1|2}\f[R] or \f[V]--compress={0|1|2}\f[R]
Enable compression of resources:
.RS
.IP \[bu] 2
\f[V]0\f[R]: No compression
.IP \[bu] 2
\f[V]1\f[R]: Constant string sharing
.IP \[bu] 2
\f[V]2\f[R]: ZIP
.RE
.TP
\f[V]--disable-plugin\f[R] \f[I]pluginname\f[R]
Disables the specified plug-in.
See \f[B]jlink Plug-ins\f[R] for the list of supported plug-ins.
.TP
\f[V]--endian\f[R] {\f[V]little\f[R]|\f[V]big\f[R]}
Specifies the byte order of the generated image.
The default value is the format of your system\[aq]s architecture.
.TP
\f[V]-h\f[R] or \f[V]--help\f[R]
Prints the help message.
.TP
\f[V]--ignore-signing-information\f[R]
Suppresses a fatal error when signed modular JARs are linked in the
runtime image.
The signature-related files of the signed modular JARs aren\[aq]t copied
to the runtime image.
.TP
\f[V]--launcher\f[R] \f[I]command\f[R]\f[V]=\f[R]\f[I]module\f[R] or \f[V]--launcher\f[R] \f[I]command\f[R]\f[V]=\f[R]\f[I]module\f[R]\f[V]/\f[R]\f[I]main\f[R]
Specifies the launcher command name for the module or the command name
for the module and main class (the module and the main class names are
separated by a slash (\f[V]/\f[R])).
.TP
\f[V]--limit-modules\f[R] \f[I]mod\f[R][\f[V],\f[R]\f[I]mod\f[R]...]
Limits the universe of observable modules to those in the transitive
closure of the named modules, \f[V]mod\f[R], plus the main module, if
any, plus any further modules specified in the \f[V]--add-modules\f[R]
option.
.TP
\f[V]--list-plugins\f[R]
Lists available plug-ins, which you can access through command-line
options; see \f[B]jlink Plug-ins\f[R].
.TP
\f[V]-p\f[R] or \f[V]--module-path\f[R] \f[I]modulepath\f[R]
Specifies the module path.
.RS
.PP
If this option is not specified, then the default module path is
\f[V]$JAVA_HOME/jmods\f[R].
This directory contains the \f[V]java.base\f[R] module and the other
standard and JDK modules.
If this option is specified but the \f[V]java.base\f[R] module cannot be
resolved from it, then the \f[V]jlink\f[R] command appends
\f[V]$JAVA_HOME/jmods\f[R] to the module path.
.RE
.TP
\f[V]--no-header-files\f[R]
Excludes header files.
.TP
\f[V]--no-man-pages\f[R]
Excludes man pages.
.TP
\f[V]--output\f[R] \f[I]path\f[R]
Specifies the location of the generated runtime image.
.TP
\f[V]--save-opts\f[R] \f[I]filename\f[R]
Saves \f[V]jlink\f[R] options in the specified file.
.TP
\f[V]--suggest-providers\f[R] [\f[I]name\f[R]\f[V],\f[R] ...]
Suggest providers that implement the given service types from the module
path.
.TP
\f[V]--version\f[R]
Prints version information.
.TP
\f[V]\[at]\f[R]\f[I]filename\f[R]
Reads options from the specified file.
.RS
.PP
An options file is a text file that contains the options and values that
you would typically enter in a command prompt.
Options may appear on one line or on several lines.
You may not specify environment variables for path names.
You may comment out lines by prefixing a hash symbol (\f[V]#\f[R]) to
the beginning of the line.
.PP
The following is an example of an options file for the \f[V]jlink\f[R]
command:
.IP
.nf
\f[CB]
#Wed Dec 07 00:40:19 EST 2016
--module-path mlib
--add-modules com.greetings
--output greetingsapp
\f[R]
.fi
.RE
.SH JLINK PLUG-INS
.PP
\f[B]Note:\f[R]
.PP
Plug-ins not listed in this section aren\[aq]t supported and are subject
to change.
.PP
For plug-in options that require a \f[I]pattern-list\f[R], the value is
a comma-separated list of elements, with each element using one the
following forms:
.IP \[bu] 2
\f[I]glob-pattern\f[R]
.IP \[bu] 2
\f[V]glob:\f[R]\f[I]glob-pattern\f[R]
.IP \[bu] 2
\f[V]regex:\f[R]\f[I]regex-pattern\f[R]
.IP \[bu] 2
\f[V]\[at]\f[R]\f[I]filename\f[R]
.RS 2
.IP \[bu] 2
\f[I]filename\f[R] is the name of a file that contains patterns to be
used, one pattern per line.
.RE
.PP
For a complete list of all available plug-ins, run the command
\f[V]jlink --list-plugins\f[R].
.SS Plugin \f[V]compress\f[R]
.TP
Options
\f[V]--compress=\f[R]{\f[V]0\f[R]|\f[V]1\f[R]|\f[V]2\f[R]}[\f[V]:filter=\f[R]\f[I]pattern-list\f[R]]
.TP
Description
Compresses all resources in the output image.
.RS
.IP \[bu] 2
Level 0: No compression
.IP \[bu] 2
Level 1: Constant string sharing
.IP \[bu] 2
Level 2: ZIP
.PP
An optional \f[I]pattern-list\f[R] filter can be specified to list the
pattern of files to include.
.RE
.SS Plugin \f[V]include-locales\f[R]
.TP
Options
\f[V]--include-locales=\f[R]\f[I]langtag\f[R][\f[V],\f[R]\f[I]langtag\f[R]]*
.TP
Description
Includes the list of locales where \f[I]langtag\f[R] is a BCP 47
language tag.
This option supports locale matching as defined in RFC 4647.
Ensure that you add the module jdk.localedata when using this option.
.RS
.PP
Example:
.RS
.PP
\f[V]--add-modules jdk.localedata --include-locales=en,ja,*-IN\f[R]
.RE
.RE
.SS Plugin \f[V]order-resources\f[R]
.TP
Options
\f[V]--order-resources=\f[R]\f[I]pattern-list\f[R]
.TP
Description
Orders the specified paths in priority order.
If \f[V]\[at]\f[R]\f[I]filename\f[R] is specified, then each line in
\f[I]pattern-list\f[R] must be an exact match for the paths to be
ordered.
.RS
.PP
Example:
.RS
.PP
\f[V]--order-resources=/module-info.class,\[at]classlist,/java.base/java/lang/\f[R]
.RE
.RE
.SS Plugin \f[V]strip-debug\f[R]
.TP
Options
\f[V]--strip-debug\f[R]
.TP
Description
Strips debug information from the output image.
.SS Plugin \f[V]generate-cds-archive\f[R]
.TP
Options
\f[V]--generate-cds-archive\f[R]
.TP
Description
Generate CDS archive if the runtime image supports the CDS feature.
.SH JLINK EXAMPLES
.PP
The following command creates a runtime image in the directory
\f[V]greetingsapp\f[R].
This command links the module \f[V]com.greetings\f[R], whose module
definition is contained in the directory \f[V]mlib\f[R].
.IP
.nf
\f[CB]
jlink --module-path mlib --add-modules com.greetings --output greetingsapp
\f[R]
.fi
.PP
The following command lists the modules in the runtime image
\f[V]greetingsapp\f[R]:
.IP
.nf
\f[CB]
greetingsapp/bin/java --list-modules
com.greetings
java.base\[at]11
java.logging\[at]11
org.astro\[at]1.0
\f[R]
.fi
.PP
The following command creates a runtime image in the directory
compressedrt that\[aq]s stripped of debug symbols, uses compression to
reduce space, and includes French language locale information:
.IP
.nf
\f[CB]
jlink --add-modules jdk.localedata --strip-debug --compress=2 --include-locales=fr --output compressedrt
\f[R]
.fi
.PP
The following example compares the size of the runtime image
\f[V]compressedrt\f[R] with \f[V]fr_rt\f[R], which isn\[aq]t stripped of
debug symbols and doesn\[aq]t use compression:
.IP
.nf
\f[CB]
jlink --add-modules jdk.localedata --include-locales=fr --output fr_rt

du -sh ./compressedrt ./fr_rt
23M     ./compressedrt
36M     ./fr_rt
\f[R]
.fi
.PP
The following example lists the providers that implement
\f[V]java.security.Provider\f[R]:
.IP
.nf
\f[CB]
jlink --suggest-providers java.security.Provider

Suggested providers:
  java.naming provides java.security.Provider used by java.base
  java.security.jgss provides java.security.Provider used by java.base
  java.security.sasl provides java.security.Provider used by java.base
  java.smartcardio provides java.security.Provider used by java.base
  java.xml.crypto provides java.security.Provider used by java.base
  jdk.crypto.cryptoki provides java.security.Provider used by java.base
  jdk.crypto.ec provides java.security.Provider used by java.base
  jdk.crypto.mscapi provides java.security.Provider used by java.base
  jdk.security.jgss provides java.security.Provider used by java.base
\f[R]
.fi
.PP
The following example creates a custom runtime image named
\f[V]mybuild\f[R] that includes only \f[V]java.naming\f[R] and
\f[V]jdk.crypto.cryptoki\f[R] and their dependencies but no other
providers.
Note that these dependencies must exist in the module path:
.IP
.nf
\f[CB]
jlink --add-modules java.naming,jdk.crypto.cryptoki --output mybuild
\f[R]
.fi
.PP
The following command is similar to the one that creates a runtime image
named \f[V]greetingsapp\f[R], except that it will link the modules
resolved from root modules with service binding; see the
\f[B]\f[VB]Configuration.resolveAndBind\f[B]\f[R] method.
.IP
.nf
\f[CB]
jlink --module-path mlib --add-modules com.greetings --output greetingsapp --bind-services
\f[R]
.fi
.PP
The following command lists the modules in the runtime image
greetingsapp created by this command:
.IP
.nf
\f[CB]
greetingsapp/bin/java --list-modules
com.greetings
java.base\[at]11
java.compiler\[at]11
java.datatransfer\[at]11
java.desktop\[at]11
java.logging\[at]11
java.management\[at]11
java.management.rmi\[at]11
java.naming\[at]11
java.prefs\[at]11
java.rmi\[at]11
java.security.jgss\[at]11
java.security.sasl\[at]11
java.smartcardio\[at]11
java.xml\[at]11
java.xml.crypto\[at]11
jdk.accessibility\[at]11
jdk.charsets\[at]11
jdk.compiler\[at]11
jdk.crypto.cryptoki\[at]11
jdk.crypto.ec\[at]11
jdk.crypto.mscapi\[at]11
jdk.internal.opt\[at]11
jdk.jartool\[at]11
jdk.javadoc\[at]11
jdk.jdeps\[at]11
jdk.jfr\[at]11
jdk.jlink\[at]11
jdk.localedata\[at]11
jdk.management\[at]11
jdk.management.jfr\[at]11
jdk.naming.dns\[at]11
jdk.naming.rmi\[at]11
jdk.security.auth\[at]11
jdk.security.jgss\[at]11
jdk.zipfs\[at]11
org.astro\[at]1.0
\f[R]
.fi
