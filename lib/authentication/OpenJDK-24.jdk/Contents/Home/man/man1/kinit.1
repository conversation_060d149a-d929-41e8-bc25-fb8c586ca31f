.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "KINIT" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
kinit - obtain and cache Kerberos ticket-granting tickets
.SH SYNOPSIS
.PP
Initial ticket request:
.PP
\f[V]kinit\f[R] [\f[V]-A\f[R]] [\f[V]-f\f[R]] [\f[V]-p\f[R]]
[\f[V]-c\f[R] \f[I]cache_name\f[R]] [\f[V]-l\f[R] \f[I]lifetime\f[R]]
[\f[V]-r\f[R] \f[I]renewable_time\f[R]] [[\f[V]-k\f[R] [\f[V]-t\f[R]
\f[I]keytab_file_name\f[R]]] [\f[I]principal\f[R]] [\f[I]password\f[R]]
.PP
Renew a ticket:
.PP
\f[V]kinit\f[R] \f[V]-R\f[R] [\f[V]-c\f[R] \f[I]cache_name\f[R]]
[\f[I]principal\f[R]]
.SH DESCRIPTION
.PP
This tool is similar in functionality to the \f[V]kinit\f[R] tool that
is commonly found in other Kerberos implementations, such as SEAM and
MIT Reference implementations.
The user must be registered as a principal with the Key Distribution
Center (KDC) prior to running \f[V]kinit\f[R].
.PP
By default, on Windows, a cache file named
\f[I]USER_HOME\f[R]\f[V]\[rs]krb5cc_\f[R]\f[I]USER_NAME\f[R] is
generated.
.PP
The identifier \f[I]USER_HOME\f[R] is obtained from the
\f[V]java.lang.System\f[R] property \f[V]user.home\f[R].
\f[I]USER_NAME\f[R] is obtained from the \f[V]java.lang.System\f[R]
property \f[V]user.name\f[R].
If \f[I]USER_HOME\f[R] is null, the cache file is stored in the current
directory from which the program is running.
\f[I]USER_NAME\f[R] is the operating system\[aq]s login user name.
This user name could be different than the user\[aq]s principal name.
For example, on Windows, the cache file could be
\f[V]C:\[rs]Windows\[rs]Users\[rs]duke\[rs]krb5cc_duke\f[R], in which
\f[V]duke\f[R] is the \f[I]USER_NAME\f[R] and
\f[V]C:\[rs]Windows\[rs]Users\[rs]duke\f[R] is the \f[I]USER_HOME\f[R].
.PP
By default, the keytab name is retrieved from the Kerberos configuration
file.
If the keytab name isn\[aq]t specified in the Kerberos configuration
file, the kinit tool assumes that the name is
\f[I]USER_HOME\f[R]\f[V]\[rs]krb5.keytab\f[R]
.PP
If you don\[aq]t specify the password using the \f[I]password\f[R]
option on the command line, the \f[V]kinit\f[R] tool prompts you for the
password.
.PP
\f[B]Note:\f[R]
.PP
The \f[V]password\f[R] option is provided only for testing purposes.
Don\[aq]t specify your password in a script or provide your password on
the command line.
Doing so will compromise your password.
.SH COMMANDS
.PP
You can specify one of the following commands.
After the command, specify the options for it.
.TP
\f[V]-A\f[R]
Doesn\[aq]t include addresses.
.TP
\f[V]-f\f[R]
Issues a forwardable ticket.
.TP
\f[V]-p\f[R]
Issues a proxiable ticket.
.TP
\f[V]-c\f[R] \f[I]cache_name\f[R]
The cache name (for example, \f[V]FILE:D:\[rs]temp\[rs]mykrb5cc\f[R]).
.TP
\f[V]-l\f[R] \f[I]lifetime\f[R]
Sets the lifetime of a ticket.
The value can be one of \[dq]h:m[:s]\[dq], \[dq]NdNhNmNs\[dq], and
\[dq]N\[dq].
See the \f[B]MIT krb5 Time Duration definition\f[R]
[http://web.mit.edu/kerberos/krb5-1.17/doc/basic/date_format.html#duration]
for more information.
.TP
\f[V]-r\f[R] \f[I]renewable_time\f[R]
Sets the total lifetime that a ticket can be renewed.
.TP
\f[V]-R\f[R]
Renews a ticket.
.TP
\f[V]-k\f[R]
Uses keytab
.TP
\f[V]-t\f[R] \f[I]keytab_filename\f[R]
The keytab name (for example,
\f[V]D:\[rs]winnt\[rs]profiles\[rs]duke\[rs]krb5.keytab\f[R]).
.TP
\f[I]principal\f[R]
The principal name (for example, \f[V]duke\[at]example.com\f[R]).
.TP
\f[I]password\f[R]
The \f[I]principal\f[R]\[aq]s Kerberos password.
\f[B]Don\[aq]t specify this on the command line or in a script.\f[R]
.PP
Run \f[V]kinit -help\f[R] to display the instructions above.
.SH EXAMPLES
.PP
Requests credentials valid for authentication from the current client
host, for the default services, storing the credentials cache in the
default location
(\f[V]C:\[rs]Windows\[rs]Users\[rs]duke\[rs]krb5cc_duke\f[R]):
.RS
.PP
\f[V]kinit duke\[at]example.com\f[R]
.RE
.PP
Requests proxiable credentials for a different principal and store these
credentials in a specified file cache:
.RS
.PP
\f[V]kinit -l 1h -r 10h duke\[at]example.com\f[R]
.RE
.PP
Requests a TGT for the specified principal that will expire in 1 hour
but is renewable for up to 10 hours.
Users must renew a ticket before it has expired.
The renewed ticket can be renewed repeatedly within 10 hours from its
initial request.
.RS
.PP
\f[V]kinit -R duke\[at]example.com\f[R]
.RE
.PP
Renews an existing renewable TGT for the specified principal.
.RS
.PP
\f[V]kinit -p -c FILE:C:\[rs]Windows\[rs]Users\[rs]duke\[rs]credentials\[rs]krb5cc_cafebeef cafebeef\[at]example.com\f[R]
.RE
.PP
Requests proxiable and forwardable credentials for a different principal
and stores these credentials in a specified file cache:
.RS
.PP
\f[V]kinit -f -p -c FILE:C:\[rs]Windows\[rs]Users\[rs]duke\[rs]credentials\[rs]krb5cc_cafebeef cafebeef\[at]example.com\f[R]
.RE
.PP
Displays the help menu for the \f[V]kinit\f[R] tool:
.RS
.PP
\f[V]kinit -help\f[R]
.RE
