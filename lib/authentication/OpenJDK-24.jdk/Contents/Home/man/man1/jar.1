.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JAR" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jar - create an archive for classes and resources, and manipulate or
restore individual classes or resources from an archive
.SH SYNOPSIS
.PP
\f[V]jar\f[R] [\f[I]OPTION\f[R] ...]
[ [\f[V]--release\f[R] \f[I]VERSION\f[R]] [\f[V]-C\f[R] \f[I]dir\f[R]]
\f[I]files\f[R]] ...
.SH DESCRIPTION
.PP
The \f[V]jar\f[R] command is a general-purpose archiving and compression
tool, based on the ZIP and ZLIB compression formats.
Initially, the \f[V]jar\f[R] command was designed to package Java
applets (not supported since JDK 11) or applications; however, beginning
with JDK 9, users can use the \f[V]jar\f[R] command to create modular
JARs.
For transportation and deployment, it\[aq]s usually more convenient to
package modules as modular JARs.
.PP
The syntax for the \f[V]jar\f[R] command resembles the syntax for the
\f[V]tar\f[R] command.
It has several main operation modes, defined by one of the mandatory
operation arguments.
Other arguments are either options that modify the behavior of the
operation or are required to perform the operation.
.PP
When modules or the components of an application (files, images and
sounds) are combined into a single archive, they can be downloaded by a
Java agent (such as a browser) in a single HTTP transaction, rather than
requiring a new connection for each piece.
This dramatically improves download times.
The \f[V]jar\f[R] command also compresses files, which further improves
download time.
The \f[V]jar\f[R] command also enables individual entries in a file to
be signed so that their origin can be authenticated.
A JAR file can be used as a class path entry, whether or not it\[aq]s
compressed.
.PP
An archive becomes a modular JAR when you include a module descriptor,
\f[V]module-info.class\f[R], in the root of the given directories or in
the root of the \f[V].jar\f[R] archive.
The following operations described in \f[B]Operation Modifiers Valid
Only in Create and Update Modes\f[R] are valid only when creating or
updating a modular jar or updating an existing non-modular jar:
.IP \[bu] 2
\f[V]--module-version\f[R]
.IP \[bu] 2
\f[V]--hash-modules\f[R]
.IP \[bu] 2
\f[V]--module-path\f[R]
.PP
\f[B]Note:\f[R]
.PP
All mandatory or optional arguments for long options are also mandatory
or optional for any corresponding short options.
.SH MAIN OPERATION MODES
.PP
When using the \f[V]jar\f[R] command, you must specify the operation for
it to perform.
You specify the operation mode for the \f[V]jar\f[R] command by
including the appropriate operation arguments described in this section.
You can mix an operation argument with other one-letter options.
Generally the operation argument is the first argument specified on the
command line.
.TP
\f[V]-c\f[R] or \f[V]--create\f[R]
Creates the archive.
.TP
\f[V]-i\f[R] \f[I]FILE\f[R] or \f[V]--generate-index=\f[R]\f[I]FILE\f[R]
Generates index information for the specified JAR file.
This option is deprecated and may be removed in a future release.
.TP
\f[V]-t\f[R] or \f[V]--list\f[R]
Lists the table of contents for the archive.
.TP
\f[V]-u\f[R] or \f[V]--update\f[R]
Updates an existing JAR file.
.TP
\f[V]-x\f[R] or \f[V]--extract\f[R]
Extracts the named (or all) files from the archive.
If a file with the same name appears more than once in the archive, each
copy will be extracted, with later copies overwriting (replacing)
earlier copies unless -k is specified.
.TP
\f[V]-d\f[R] or \f[V]--describe-module\f[R]
Prints the module descriptor or automatic module name.
.SH OPERATION MODIFIERS VALID IN ANY MODE
.PP
You can use the following options to customize the actions of any
operation mode included in the \f[V]jar\f[R] command.
.TP
\f[V]-C\f[R] \f[I]DIR\f[R]
When used with the create operation mode, changes the specified
directory and includes the \f[I]files\f[R] specified at the end of the
command line.
.RS
.PP
\f[V]jar\f[R] [\f[I]OPTION\f[R] ...]
[ [\f[V]--release\f[R] \f[I]VERSION\f[R]] [\f[V]-C\f[R] \f[I]dir\f[R]]
\f[I]files\f[R]]
.PP
When used with the extract operation mode, specifies the destination
directory where the JAR file will be extracted.
Unlike with the create operation mode, this option can be specified only
once with the extract operation mode.
.RE
.TP
\f[V]-f\f[R] \f[I]FILE\f[R] or \f[V]--file=\f[R]\f[I]FILE\f[R]
Specifies the archive file name.
.TP
\f[V]--release\f[R] \f[I]VERSION\f[R]
Creates a multirelease JAR file.
Places all files specified after the option into a versioned directory
of the JAR file named
\f[V]META-INF/versions/\f[R]\f[I]VERSION\f[R]\f[V]/\f[R], where
\f[I]VERSION\f[R] must be must be a positive integer whose value is 9 or
greater.
.RS
.PP
At run time, where more than one version of a class exists in the JAR,
the JDK will use the first one it finds, searching initially in the
directory tree whose \f[I]VERSION\f[R] number matches the JDK\[aq]s
major version number.
It will then look in directories with successively lower
\f[I]VERSION\f[R] numbers, and finally look in the root of the JAR.
.RE
.TP
\f[V]-v\f[R] or \f[V]--verbose\f[R]
Sends or prints verbose output to standard output.
.SH OPERATION MODIFIERS VALID ONLY IN CREATE AND UPDATE MODES
.PP
You can use the following options to customize the actions of the create
and the update main operation modes:
.TP
\f[V]-e\f[R] \f[I]CLASSNAME\f[R] or \f[V]--main-class=\f[R]\f[I]CLASSNAME\f[R]
Specifies the application entry point for standalone applications
bundled into a modular or executable modular JAR file.
.TP
\f[V]-m\f[R] \f[I]FILE\f[R] or \f[V]--manifest=\f[R]\f[I]FILE\f[R]
Includes the manifest information from the given manifest file.
.TP
\f[V]-M\f[R] or \f[V]--no-manifest\f[R]
Doesn\[aq]t create a manifest file for the entries.
.TP
\f[V]--module-version=\f[R]\f[I]VERSION\f[R]
Specifies the module version, when creating or updating a modular JAR
file, or updating a non-modular JAR file.
.TP
\f[V]--hash-modules=\f[R]\f[I]PATTERN\f[R]
Computes and records the hashes of modules matched by the given pattern
and that depend upon directly or indirectly on a modular JAR file being
created or a non-modular JAR file being updated.
.TP
\f[V]-p\f[R] or \f[V]--module-path\f[R]
Specifies the location of module dependence for generating the hash.
.TP
\f[V]\[at]\f[R]\f[I]file\f[R]
Reads \f[V]jar\f[R] options and file names from a text file as if they
were supplied on the command line
.SH OPERATION MODIFIERS VALID ONLY IN CREATE, UPDATE, AND GENERATE-INDEX MODES
.PP
You can use the following options to customize the actions of the create
(\f[V]-c\f[R] or \f[V]--create\f[R]) the update (\f[V]-u\f[R] or
\f[V]--update\f[R] ) and the generate-index (\f[V]-i\f[R] or
\f[V]--generate-index=\f[R]\f[I]FILE\f[R]) main operation modes:
.TP
\f[V]-0\f[R] or \f[V]--no-compress\f[R]
Stores without using ZIP compression.
.TP
\f[V]--date=\f[R]\f[I]TIMESTAMP\f[R]
The timestamp in ISO-8601 extended offset date-time with optional
time-zone format, to use for the timestamp of the entries, e.g.
\[dq]2022-02-12T12:30:00-05:00\[dq].
.SH OPERATION MODIFIERS VALID ONLY IN EXTRACT MODE
.TP
\f[V]--dir\f[R] \f[I]DIR\f[R]
Directory into which the JAR file will be extracted.
.TP
\f[V]-k\f[R] or \f[V]--keep-old-files\f[R]
Do not overwrite existing files.
If a Jar file entry with the same name exists in the target directory,
the existing file will not be overwritten.
As a result, if a file appears more than once in an archive, later
copies will not overwrite earlier copies.
Also note that some file system can be case insensitive.
.SH OTHER OPTIONS
.PP
The following options are recognized by the \f[V]jar\f[R] command and
not used with operation modes:
.TP
\f[V]-h\f[R] or \f[V]--help\f[R][\f[V]:compat\f[R]]
Displays the command-line help for the \f[V]jar\f[R] command or
optionally the compatibility help.
.TP
\f[V]--help-extra\f[R]
Displays help on extra options.
.TP
\f[V]--version\f[R]
Prints the program version.
.SH EXAMPLES OF JAR COMMAND SYNTAX
.IP \[bu] 2
Create an archive, \f[V]classes.jar\f[R], that contains two class files,
\f[V]Foo.class\f[R] and \f[V]Bar.class\f[R].
.RS 2
.RS
.PP
\f[V]jar --create --file classes.jar Foo.class Bar.class\f[R]
.RE
.RE
.IP \[bu] 2
Create an archive, \f[V]classes.jar\f[R], that contains two class files,
\f[V]Foo.class\f[R] and \f[V]Bar.class\f[R] setting the last modified
date and time to \f[V]2021 Jan 6 12:36:00\f[R].
.RS 2
.RS
.PP
\f[V]jar --create --date=\[dq]2021-01-06T14:36:00+02:00\[dq] --file=classes.jar Foo.class Bar.class\f[R]
.RE
.RE
.IP \[bu] 2
Create an archive, \f[V]classes.jar\f[R], by using an existing manifest,
\f[V]mymanifest\f[R], that contains all of the files in the directory
\f[V]foo/\f[R].
.RS 2
.RS
.PP
\f[V]jar --create --file classes.jar --manifest mymanifest -C foo/\f[R]
.RE
.RE
.IP \[bu] 2
Create a modular JAR archive,\f[V]foo.jar\f[R], where the module
descriptor is located in \f[V]classes/module-info.class\f[R].
.RS 2
.RS
.PP
\f[V]jar --create --file foo.jar --main-class com.foo.Main --module-version 1.0 -C foo/classes resources\f[R]
.RE
.RE
.IP \[bu] 2
Update an existing non-modular JAR, \f[V]foo.jar\f[R], to a modular JAR
file.
.RS 2
.RS
.PP
\f[V]jar --update --file foo.jar --main-class com.foo.Main --module-version 1.0 -C foo/module-info.class\f[R]
.RE
.RE
.IP \[bu] 2
Create a versioned or multi-release JAR, \f[V]foo.jar\f[R], that places
the files in the \f[V]classes\f[R] directory at the root of the JAR, and
the files in the \f[V]classes-10\f[R] directory in the
\f[V]META-INF/versions/10\f[R] directory of the JAR.
.RS 2
.PP
In this example, the \f[V]classes/com/foo\f[R] directory contains two
classes, \f[V]com.foo.Hello\f[R] (the entry point class) and
\f[V]com.foo.NameProvider\f[R], both compiled for JDK 8.
The \f[V]classes-10/com/foo\f[R] directory contains a different version
of the \f[V]com.foo.NameProvider\f[R] class, this one containing JDK 10
specific code and compiled for JDK 10.
.PP
Given this setup, create a multirelease JAR file \f[V]foo.jar\f[R] by
running the following command from the directory containing the
directories \f[V]classes\f[R] and \f[V]classes-10\f[R] .
.RS
.PP
\f[V]jar --create --file foo.jar --main-class com.foo.Hello -C classes . --release 10 -C classes-10 .\f[R]
.RE
.PP
The JAR file \f[V]foo.jar\f[R] now contains:
.IP
.nf
\f[CB]
% jar -tf foo.jar

META-INF/
META-INF/MANIFEST.MF
com/
com/foo/
com/foo/Hello.class
com/foo/NameProvider.class
META-INF/versions/10/com/
META-INF/versions/10/com/foo/
META-INF/versions/10/com/foo/NameProvider.class
\f[R]
.fi
.PP
As well as other information, the file \f[V]META-INF/MANIFEST.MF\f[R],
will contain the following lines to indicate that this is a multirelease
JAR file with an entry point of \f[V]com.foo.Hello\f[R].
.IP
.nf
\f[CB]
\&...
Main-Class: com.foo.Hello
Multi-Release: true
\f[R]
.fi
.PP
Assuming that the \f[V]com.foo.Hello\f[R] class calls a method on the
\f[V]com.foo.NameProvider\f[R] class, running the program using JDK 10
will ensure that the \f[V]com.foo.NameProvider\f[R] class is the one in
\f[V]META-INF/versions/10/com/foo/\f[R].
Running the program using JDK 8 will ensure that the
\f[V]com.foo.NameProvider\f[R] class is the one at the root of the JAR,
in \f[V]com/foo\f[R].
.RE
.IP \[bu] 2
Create an archive, \f[V]my.jar\f[R], by reading options and lists of
class files from the file \f[V]classes.list\f[R].
.RS 2
.PP
\f[B]Note:\f[R]
.PP
To shorten or simplify the \f[V]jar\f[R] command, you can provide an arg
file that lists the files to include in the JAR file and pass it to the
\f[V]jar\f[R] command with the at sign (\f[V]\[at]\f[R]) as a prefix.
.RS
.PP
\f[V]jar --create --file my.jar \[at]classes.list\f[R]
.RE
.PP
If one or more entries in the arg file cannot be found then the jar
command fails without creating the JAR file.
.RE
.IP \[bu] 2
Extract the JAR file \f[V]foo.jar\f[R] to \f[V]/tmp/bar/\f[R] directory:
.RS 2
.RS
.PP
\f[V]jar -xf foo.jar -C /tmp/bar/\f[R]
.RE
.PP
Alternatively, you can also do:
.RS
.PP
\f[V]jar --extract --file foo.jar --dir /tmp/bar/\f[R]
.RE
.RE
