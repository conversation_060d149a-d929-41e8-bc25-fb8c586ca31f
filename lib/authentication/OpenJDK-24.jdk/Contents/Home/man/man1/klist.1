.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "KLIST" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
klist - display the entries in the local credentials cache and key table
.SH SYNOPSIS
.PP
\f[V]klist\f[R] [\f[V]-c\f[R] [\f[V]-f\f[R]] [\f[V]-e\f[R]]
[\f[V]-a\f[R] [\f[V]-n\f[R]]]] [\f[V]-k\f[R] [\f[V]-t\f[R]]
[\f[V]-K\f[R]]] [\f[I]name\f[R]] [\f[V]-help\f[R]]
.SH DESCRIPTION
.PP
The \f[V]klist\f[R] tool displays the entries in the local credentials
cache and key table.
After you modify the credentials cache with the \f[V]kinit\f[R] tool or
modify the keytab with the \f[V]ktab\f[R] tool, the only way to verify
the changes is to view the contents of the credentials cache or keytab
using the \f[V]klist\f[R] tool.
The \f[V]klist\f[R] tool doesn\[aq]t change the Kerberos database.
.SH COMMANDS
.TP
\f[V]-c\f[R]
Specifies that the credential cache is to be listed.
.RS
.PP
The following are the options for credential cache entries:
.TP
\f[V]-f\f[R]
Show credential flags.
.TP
\f[V]-e\f[R]
Show the encryption type.
.TP
\f[V]-a\f[R]
Show addresses.
.TP
\f[V]-n\f[R]
If the \f[V]-a\f[R] option is specified, don\[aq]t reverse resolve
addresses.
.RE
.TP
\f[V]-k\f[R]
Specifies that key tab is to be listed.
.RS
.PP
List the keytab entries.
The following are the options for keytab entries:
.TP
\f[V]-t\f[R]
Show keytab entry timestamps.
.TP
\f[V]-K\f[R]
Show keytab entry DES keys.
.TP
\f[V]-e\f[R]
Shows keytab entry key type.
.RE
.TP
\f[I]name\f[R]
Specifies the credential cache name or the keytab name.
File-based cache or keytab\[aq]s prefix is \f[V]FILE:\f[R].
If the name isn\[aq]t specified, the \f[V]klist\f[R] tool uses default
values for the cache name and keytab.
The \f[V]kinit\f[R] documentation lists these default values.
.TP
\f[V]-help\f[R]
Displays instructions.
.SH EXAMPLES
.PP
List entries in the keytable specified including keytab entry timestamps
and DES keys:
.RS
.PP
\f[V]klist -k -t -K FILE:\[rs]temp\[rs]mykrb5cc\f[R]
.RE
.PP
List entries in the credentials cache specified including credentials
flag and address list:
.RS
.PP
\f[V]klist -c -f FILE:\[rs]temp\[rs]mykrb5cc\f[R]
.RE
