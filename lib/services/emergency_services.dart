class EmergencyService {
  final String name;
  final String number;
  final String description;
  final String type;
  final bool isAvailable24h;
  final String? website;
  final String? textNumber;

  EmergencyService({
    required this.name,
    required this.number,
    required this.description,
    required this.type,
    this.isAvailable24h = true,
    this.website,
    this.textNumber,
  });
}

class EmergencyServicesData {
  static const Map<String, List<EmergencyService>> _servicesByCountry = {
    'US': [
      // Emergency Services
      EmergencyService(
        name: 'Emergency Services',
        number: '911',
        description: 'Police, Fire, Medical Emergency',
        type: 'emergency',
      ),
      
      // Mental Health Crisis
      EmergencyService(
        name: '988 Suicide & Crisis Lifeline',
        number: '988',
        description: '24/7 crisis support and suicide prevention',
        type: 'crisis',
        website: 'https://988lifeline.org',
        textNumber: '988',
      ),
      EmergencyService(
        name: 'Crisis Text Line',
        number: '741741',
        description: 'Text HOME for 24/7 crisis support',
        type: 'crisis',
        textNumber: '741741',
      ),
      EmergencyService(
        name: 'SAMHSA National Helpline',
        number: '1-************',
        description: 'Substance abuse and mental health treatment referral',
        type: 'mental_health',
        website: 'https://www.samhsa.gov/find-help/national-helpline',
      ),
      
      // Domestic Violence & Abuse
      EmergencyService(
        name: 'National Domestic Violence Hotline',
        number: '1-************',
        description: '24/7 support for domestic violence survivors',
        type: 'abuse',
        website: 'https://www.thehotline.org',
        textNumber: 'START to 88788',
      ),
      EmergencyService(
        name: 'Childhelp National Child Abuse Hotline',
        number: '1-************',
        description: 'Professional crisis counselors for child abuse',
        type: 'abuse',
      ),
      EmergencyService(
        name: 'National Sexual Assault Hotline',
        number: '1-************',
        description: 'Support for sexual assault survivors',
        type: 'abuse',
        website: 'https://www.rainn.org',
      ),
      
      // Specialized Support
      EmergencyService(
        name: 'National Eating Disorders Association',
        number: '1-800-931-2237',
        description: 'Support for eating disorders',
        type: 'specialized',
        website: 'https://www.nationaleatingdisorders.org',
      ),
      EmergencyService(
        name: 'Trans Lifeline',
        number: '877-565-8860',
        description: 'Crisis support for transgender individuals',
        type: 'specialized',
        website: 'https://translifeline.org',
      ),
    ],
    
    'UK': [
      // Emergency Services
      EmergencyService(
        name: 'Emergency Services',
        number: '999',
        description: 'Police, Fire, Ambulance',
        type: 'emergency',
      ),
      EmergencyService(
        name: 'Non-Emergency Police',
        number: '101',
        description: 'Non-urgent police matters',
        type: 'emergency',
        isAvailable24h: false,
      ),
      
      // Mental Health Crisis
      EmergencyService(
        name: 'Samaritans',
        number: '116 123',
        description: '24/7 emotional support for anyone in distress',
        type: 'crisis',
        website: 'https://www.samaritans.org',
      ),
      EmergencyService(
        name: 'Crisis Text Line UK',
        number: '85258',
        description: 'Text SHOUT for 24/7 crisis support',
        type: 'crisis',
        textNumber: '85258',
      ),
      EmergencyService(
        name: 'Mind Infoline',
        number: '0300 123 3393',
        description: 'Mental health information and support',
        type: 'mental_health',
        website: 'https://www.mind.org.uk',
        isAvailable24h: false,
      ),
      
      // Domestic Violence & Abuse
      EmergencyService(
        name: 'National Domestic Abuse Helpline',
        number: '0808 2000 247',
        description: '24/7 support for domestic abuse survivors',
        type: 'abuse',
        website: 'https://www.nationaldahelpline.org.uk',
      ),
      EmergencyService(
        name: 'NSPCC Childline',
        number: '0800 1111',
        description: 'Free, confidential support for children',
        type: 'abuse',
        website: 'https://www.childline.org.uk',
      ),
      EmergencyService(
        name: 'Rape Crisis England & Wales',
        number: '0808 802 9999',
        description: 'Support for sexual violence survivors',
        type: 'abuse',
        website: 'https://rapecrisis.org.uk',
      ),
    ],
    
    'CA': [
      // Emergency Services
      EmergencyService(
        name: 'Emergency Services',
        number: '911',
        description: 'Police, Fire, Medical Emergency',
        type: 'emergency',
      ),
      
      // Mental Health Crisis
      EmergencyService(
        name: 'Talk Suicide Canada',
        number: '**************',
        description: '24/7 suicide prevention and crisis support',
        type: 'crisis',
        website: 'https://talksuicide.ca',
        textNumber: '45645',
      ),
      EmergencyService(
        name: 'Kids Help Phone',
        number: '**************',
        description: '24/7 support for young people',
        type: 'crisis',
        website: 'https://kidshelpphone.ca',
        textNumber: 'CONNECT to 686868',
      ),
      
      // Domestic Violence & Abuse
      EmergencyService(
        name: 'Assaulted Women\'s Helpline',
        number: '**************',
        description: '24/7 crisis support for women',
        type: 'abuse',
        website: 'https://www.awhl.org',
      ),
    ],
    
    'AU': [
      // Emergency Services
      EmergencyService(
        name: 'Emergency Services',
        number: '000',
        description: 'Police, Fire, Ambulance',
        type: 'emergency',
      ),
      
      // Mental Health Crisis
      EmergencyService(
        name: 'Lifeline Australia',
        number: '13 11 14',
        description: '24/7 crisis support and suicide prevention',
        type: 'crisis',
        website: 'https://www.lifeline.org.au',
        textNumber: '0477 13 11 14',
      ),
      EmergencyService(
        name: 'Beyond Blue',
        number: '1300 22 4636',
        description: 'Mental health support and information',
        type: 'mental_health',
        website: 'https://www.beyondblue.org.au',
      ),
      
      // Domestic Violence & Abuse
      EmergencyService(
        name: '1800RESPECT',
        number: '1800 737 732',
        description: '24/7 domestic and sexual violence support',
        type: 'abuse',
        website: 'https://www.1800respect.org.au',
      ),
    ],
  };

  /// Get emergency services for a specific country
  static List<EmergencyService> getServicesForCountry(String countryCode) {
    return _servicesByCountry[countryCode.toUpperCase()] ?? _getDefaultServices();
  }

  /// Get services by type for a country
  static List<EmergencyService> getServicesByType(String countryCode, String type) {
    final services = getServicesForCountry(countryCode);
    return services.where((service) => service.type == type).toList();
  }

  /// Get crisis services specifically
  static List<EmergencyService> getCrisisServices(String countryCode) {
    return getServicesByType(countryCode, 'crisis');
  }

  /// Get emergency services specifically
  static List<EmergencyService> getEmergencyServices(String countryCode) {
    return getServicesByType(countryCode, 'emergency');
  }

  /// Get abuse support services
  static List<EmergencyService> getAbuseServices(String countryCode) {
    return getServicesByType(countryCode, 'abuse');
  }

  /// Get mental health services
  static List<EmergencyService> getMentalHealthServices(String countryCode) {
    return getServicesByType(countryCode, 'mental_health');
  }

  /// Get all available country codes
  static List<String> getAvailableCountries() {
    return _servicesByCountry.keys.toList();
  }

  /// Get default services (US-based) if country not found
  static List<EmergencyService> _getDefaultServices() {
    return _servicesByCountry['US'] ?? [];
  }

  /// Check if country is supported
  static bool isCountrySupported(String countryCode) {
    return _servicesByCountry.containsKey(countryCode.toUpperCase());
  }
}
