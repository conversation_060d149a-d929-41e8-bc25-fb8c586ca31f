.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JFR" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jfr - print and manipulate Flight Recorder files
.SH SYNOPSIS
.PP
To print the contents of a flight recording to standard out:
.PP
\f[V]jfr\f[R] \f[V]print\f[R] [\f[I]options\f[R]] \f[I]file\f[R]
.PP
To display aggregated event data on standard out:
.PP
\f[V]jfr\f[R] \f[V]view\f[R] [\f[I]options\f[R]] \f[I]file\f[R]
.PP
To configure a .jfc settings file:
.PP
\f[V]jfr\f[R] \f[V]configure\f[R] [\f[I]options\f[R]]
.PP
To print metadata information about flight recording events:
.PP
\f[V]jfr\f[R] \f[V]metadata\f[R] [\f[I]file\f[R]]
.PP
To view the summary statistics for a flight recording file:
.PP
\f[V]jfr\f[R] \f[V]summary\f[R] \f[I]file\f[R]
.PP
To remove events from a flight recording file:
.PP
\f[V]jfr\f[R] \f[V]scrub\f[R] [\f[I]options\f[R]] \f[I]file\f[R]
.PP
To assemble chunk files into a flight recording file:
.PP
\f[V]jfr\f[R] \f[V]assemble\f[R] \f[I]repository\f[R] \f[I]file\f[R]
.PP
To disassemble a flight recording file into chunk files:
.PP
\f[V]jfr\f[R] \f[V]disassmble\f[R] [\f[I]options\f[R]] \f[I]file\f[R]
.TP
\f[I]options\f[R]
Optional: Specifies command-line options separated by spaces.
See the individual subcomponent sections for descriptions of the
available options.
.TP
\f[I]file\f[R]
Specifies the name of the target flight recording file (\f[V].jfr\f[R]).
.TP
\f[I]repository\f[R]
Specifies the location of the chunk files which are to be assembled into
a flight recording.
.SH DESCRIPTION
.PP
The \f[V]jfr\f[R] command provides a tool for interacting with flight
recorder files (\f[V].jfr\f[R]).
The main function is to filter, summarize and output flight recording
files into human readable format.
There is also support for scrubbing, merging and splitting recording
files.
.PP
Flight recording files are created and saved as binary formatted files.
Having a tool that can extract the contents from a flight recording and
manipulate the contents and translate them into human readable format
helps developers to debug performance issues with Java applications.
.SS Subcommands
.PP
The \f[V]jfr\f[R] command has several subcommands:
.IP \[bu] 2
\f[V]print\f[R]
.IP \[bu] 2
\f[V]view\f[R]
.IP \[bu] 2
\f[V]configure\f[R]
.IP \[bu] 2
\f[V]metadata\f[R]
.IP \[bu] 2
\f[V]summary\f[R]
.IP \[bu] 2
\f[V]scrub\f[R]
.IP \[bu] 2
\f[V]assemble\f[R]
.IP \[bu] 2
\f[V]disassemble\f[R]
.SS \f[V]jfr print\f[R] subcommand
.PP
Use \f[V]jfr print\f[R] to print the contents of a flight recording file
to standard out.
.PP
The syntax is:
.PP
\f[V]jfr print\f[R] [\f[V]--xml\f[R]|\f[V]--json\f[R]]
[\f[V]--categories\f[R] <\f[I]filters\f[R]>] [\f[V]--events\f[R]
<\f[I]filters\f[R]>] [\f[V]--stack-depth\f[R] <\f[I]depth\f[R]>]
<\f[I]file\f[R]>
.PP
where:
.TP
\f[V]--xml\f[R]
Print the recording in XML format.
.TP
\f[V]--json\f[R]
Print the recording in JSON format.
.TP
\f[V]--categories\f[R] <\f[I]filters\f[R]>
Select events matching a category name.
The filter is a comma-separated list of names, simple and/or qualified,
and/or quoted glob patterns.
.TP
\f[V]--events\f[R] <\f[I]filters\f[R]>
Select events matching an event name.
The filter is a comma-separated list of names, simple and/or qualified,
and/or quoted glob patterns.
.TP
\f[V]--stack-depth\f[R] <\f[I]depth\f[R]>
Number of frames in stack traces, by default 5.
.TP
<\f[I]file\f[R]>
Location of the recording file (\f[V].jfr\f[R])
.PP
The default format for printing the contents of the flight recording
file is human readable form unless either \f[V]xml\f[R] or
\f[V]json\f[R] is specified.
These options provide machine-readable output that can be further parsed
or processed by user created scripts.
.PP
Use \f[V]jfr --help print\f[R] to see example usage of filters.
.PP
To reduce the amount of data displayed, it is possible to filter out
events or categories of events.
The filter operates on the symbolic name of an event, set by using the
\f[V]\[at]Name\f[R] annotation, or the category name, set by using the
\f[V]\[at]Category\f[R] annotation.
If multiple filters are used, events from both filters will be included.
If no filter is used, all the events will be printed.
If a combination of a category filter and event filter is used, the
selected events will be the union of the two filters.
.PP
For example, to show all GC events and the CPULoad event, the following
command could be used:
.PP
\f[V]jfr print --categories GC --events CPULoad recording.jfr\f[R]
.PP
Event values are formatted according to the content types that are being
used.
For example, a field with the \f[V]jdk.jfr.Percentage\f[R] annotation
that has the value 0.52 is formatted as 52%.
.PP
Stack traces are by default truncated to 5 frames, but the number can be
increased/decreased using the \f[V]--stack-depth\f[R] command-line
option.
.SS \f[V]jfr view\f[R] subcommand
.PP
Use \f[V]jfr view\f[R] to aggregate and display event data on standard
out.
.PP
The syntax is:
.PP
\f[V]jfr view\f[R] [\f[V]--verbose\f[R]] [\f[V]--width\f[R]
<\f[I]integer\f[R]>] [\f[V]--truncate\f[R] <\f[I]mode\f[R]>]
[\f[V]--cell-height\f[R] <\f[I]integer\f[R]>] <\f[I]view\f[R]>
<\f[I]file\f[R]>
.PP
where:
.TP
\f[V]--verbose\f[R]
Displays the query that makes up the view.
.TP
\f[V]--width\f[R] <\f[I]integer\f[R]>
The width of the view in characters.
Default value depends on the view.
.TP
\f[V]--truncate\f[R] <\f[I]mode\f[R]>
How to truncate content that exceeds space in a table cell.
Mode can be \[aq]beginning\[aq] or \[aq]end\[aq].
Default value is \[aq]end\[aq].
.TP
\f[V]--cell-height\f[R] <\f[I]integer\f[R]>
Maximum number of rows in a table cell.
Default value depends on the view.
.TP
<\f[I]view\f[R]>
Name of the view or event type to display.
Use \f[V]jfr --help view\f[R] to see a list of available views.
.TP
<\f[I]file\f[R]>
Location of the recording file (.jfr)
.PP
The <\f[I]view\f[R]> parameter can be an event type name.
Use the \f[V]jfr view types <file>\f[R] to see a list.
To display all views, use \f[V]jfr view all-views <file>\f[R].
To display all events, use \f[V]jfr view all-events <file>\f[R].
.SS \f[V]jfr configure\f[R] subcommand
.PP
Use \f[V]jfr configure\f[R] to configure a .jfc settings file.
.PP
The syntax is:
.PP
\f[V]jfr configure\f[R] [--interactive] [--verbose] [--input <files>]
[--output <file>] [option=value]* [event-setting=value]*
.TP
\f[V]--interactive\f[R]
Interactive mode where the configuration is determined by a set of
questions.
.TP
\f[V]--verbose\f[R]
Displays the modified settings.
.TP
\f[V]--input\f[R] <\f[I]files\f[R]>
A comma-separated list of .jfc files from which the new configuration is
based.
If no file is specified, the default file in the JDK is used
(default.jfc).
If \[aq]none\[aq] is specified, the new configuration starts empty.
.TP
\f[V]--output\f[R] <\f[I]file\f[R]>
The filename of the generated output file.
If not specified, the filename custom.jfc will be used.
.TP
\f[I]option=value\f[R]
The option value to modify.
To see available options, use \f[V]jfr help configure\f[R]
.TP
\f[I]event-setting=value\f[R]
The event setting value to modify.
Use the form:
<\f[I]event-name\f[R]>#<\f[I]setting-name\f[R]>=<\f[I]value\f[R]> To add
a new event setting, prefix the event name with \[aq]+\[aq].
.PP
The whitespace delimiter can be omitted for timespan values, i.e.
20ms.
For more information about the settings syntax, see Javadoc of the
jdk.jfr package.
.SS \f[V]jfr metadata\f[R] subcommand
.PP
Use \f[V]jfr metadata\f[R] to display information about events, such as
event names, categories and field layout within a flight recording file.
.PP
The syntax is:
.PP
\f[V]jfr metadata\f[R] [--categories <filter>] [--events <filter>]
[<file>]
.TP
\f[V]--categories\f[R] <\f[I]filter\f[R]>
Select events matching a category name.
The filter is a comma-separated list of names, simple and/or qualified,
and/or quoted glob patterns.
.TP
\f[V]--events\f[R] <\f[I]filter\f[R]>
Select events matching an event name.
The filter is a comma-separated list of names, simple and/or qualified,
and/or quoted glob patterns.
.TP
<\f[I]file\f[R]>
Location of the recording file (.jfr)
.PP
If the <file> parameter is omitted, metadata from the JDK where the
\[aq]jfr\[aq] tool is located will be used.
.SS \f[V]jfr summary\f[R] subcommand
.PP
Use \f[V]jfr summary\f[R] to print statistics for a recording.
For example, a summary can illustrate the number of recorded events and
how much disk space they used.
This is useful for troubleshooting and understanding the impact of event
settings.
.PP
The syntax is:
.PP
\f[V]jfr summary\f[R] <\f[I]file\f[R]>
.PP
where:
.TP
<\f[I]file\f[R]>
Location of the flight recording file (\f[V].jfr\f[R])
.SS \f[V]jfr scrub\f[R] subcommand
.PP
Use \f[V]jfr scrub\f[R] to remove sensitive contents from a file or to
reduce its size.
.PP
The syntax is:
.PP
\f[V]jfr scrub\f[R] [--include-events <\f[I]filter\f[R]>]
[--exclude-events <\f[I]filter\f[R]>] [--include-categories
<\f[I]filter\f[R]>] [--exclude-categories <\f[I]filter\f[R]>]
[--include-threads <\f[I]filter\f[R]>] [--exclude-threads
<\f[I]filter\f[R]>] <\f[I]input-file\f[R]> [<\f[I]output-file\f[R]>]
.TP
\f[V]--include-events\f[R] <\f[I]filter\f[R]>
Select events matching an event name.
.TP
\f[V]--exclude-events\f[R] <\f[I]filter\f[R]>
Exclude events matching an event name.
.TP
\f[V]--include-categories\f[R] <\f[I]filter\f[R]>
Select events matching a category name.
.TP
\f[V]--exclude-categories\f[R] <\f[I]filter\f[R]>
Exclude events matching a category name.
.TP
\f[V]--include-threads\f[R] <\f[I]filter\f[R]>
Select events matching a thread name.
.TP
\f[V]--exclude-threads\f[R] <\f[I]filter\f[R]>
Exclude events matching a thread name.
.TP
<\f[I]input-file\f[R]>
The input file to read events from.
.TP
<\f[I]output-file\f[R]>
The output file to write filter events to.
If no file is specified, it will be written to the same path as the
input file, but with \[dq]-scrubbed\[dq] appended to the filename.
.PP
The filter is a comma-separated list of names, simple and/or qualified,
and/or quoted glob patterns.
If multiple filters are used, they are applied in the specified order.
.SS \f[V]jfr assemble\f[R] subcommand
.PP
Use jfr \f[V]assemble\f[R] to assemble chunk files into a recording
file.
.PP
The syntax is:
.PP
\f[V]jfr assemble\f[R] <\f[I]repository\f[R]> <\f[I]file\f[R]>
.PP
where:
.TP
<\f[I]repository\f[R]>
Directory where the repository containing chunk files is located.
.TP
<\f[I]file\f[R]>
Location of the flight recording file (\f[V].jfr\f[R]).
.PP
Flight recording information is written in chunks.
A chunk contains all of the information necessary for parsing.
A chunk typically contains events useful for troubleshooting.
If a JVM should crash, these chunks can be recovered and used to create
a flight recording file using this \f[V]jfr assemble\f[R] command.
These chunk files are concatenated in chronological order and chunk
files that are not finished (.part) are excluded.
.SS \f[V]jfr disassemble\f[R] subcommand
.PP
Use \f[V]jfr disassemble\f[R] to decompose a flight recording file into
its chunk file pieces.
.PP
The syntax is:
.PP
\f[V]jfr disassemble\f[R] [\f[V]--max-chunks\f[R] <\f[I]chunks\f[R]>]
[\f[V]--output\f[R] <\f[I]directory\f[R]>] <\f[I]file\f[R]>
.PP
where:
.TP
\f[V]--output\f[R] <\f[I]directory\f[R]>
The location to write the disassembled file, by default the current
directory
.TP
\f[V]--max-chunks\f[R] <\f[I]chunks\f[R]>
Maximum number of chunks per file, by default 5.
The chunk size varies, but is typically around 15 MB.
.TP
\f[V]--max-size\f[R] <\f[I]size\f[R]>
Maximum number of bytes per file.
.TP
<\f[I]file\f[R]>
Location of the flight recording file (\f[V].jfr\f[R])
.PP
This function can be useful for repairing a broken file by removing the
faulty chunk.
It can also be used to reduce the size of a file that is too large to
transfer.
The resulting chunk files are named \f[V]myfile_1.jfr\f[R],
\f[V]myfile_2.jfr\f[R], etc.
If needed, the resulting file names will be padded with zeros to
preserve chronological order.
For example, the chunk file name is \f[V]myfile_001.jfr\f[R] if the
recording consists of more than 100 chunks.
.SS jfr version and help subcommands
.PP
Use \f[V]jfr --version\f[R] or \f[V]jfr version\f[R] to view the version
string information for this jfr command.
.PP
To get help on any of the jfr subcommands, use:
.PP
\f[V]jfr <--help|help>\f[R] [\f[I]subcommand\f[R]]
.PP
where:
.PP
[\f[I]subcommand\f[R]] is any of:
.IP \[bu] 2
\f[V]print\f[R]
.IP \[bu] 2
\f[V]view\f[R]
.IP \[bu] 2
\f[V]configure\f[R]
.IP \[bu] 2
\f[V]metadata\f[R]
.IP \[bu] 2
\f[V]summary\f[R]
.IP \[bu] 2
\f[V]scrub\f[R]
.IP \[bu] 2
\f[V]assemble\f[R]
.IP \[bu] 2
\f[V]disassemble\f[R]
