.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JAVAP" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
javap - disassemble one or more class files
.SH SYNOPSIS
.PP
\f[V]javap\f[R] [\f[I]options\f[R]] \f[I]classes\f[R]...
.TP
\f[I]options\f[R]
Specifies the command-line options.
See \f[B]Options for javap\f[R].
.TP
\f[I]classes\f[R]
Specifies one or more classes separated by spaces to be processed for
annotations.
You can specify a class that can be found in the class path by its file
name, URL, or by its fully qualified class name.
.RS
.PP
Examples:
.RS
.PP
\f[V]path/to/MyClass.class\f[R]
.RE
.RS
.PP
\f[V]jar:file:///path/to/MyJar.jar!/mypkg/MyClass.class\f[R]
.RE
.RS
.PP
\f[V]java.lang.Object\f[R]
.RE
.RE
.SH DESCRIPTION
.PP
The \f[V]javap\f[R] command disassembles one or more class files.
The output depends on the options used.
When no options are used, the \f[V]javap\f[R] command prints the
protected and public fields, and methods of the classes passed to it.
.PP
The \f[V]javap\f[R] command isn\[aq]t multirelease JAR aware.
Using the class path form of the command results in viewing the base
entry in all JAR files, multirelease or not.
Using the URL form, you can use the URL form of an argument to specify a
specific version of a class to be disassembled.
.PP
The \f[V]javap\f[R] command prints its output to \f[V]stdout\f[R].
.PP
\f[B]Note:\f[R]
.PP
In tools that support \f[V]--\f[R] style options, the GNU-style options
can use the equal sign (\f[V]=\f[R]) instead of a white space to
separate the name of an option from its value.
.SH OPTIONS FOR JAVAP
.TP
\f[V]--help\f[R], \f[V]-help\f[R] , \f[V]-h\f[R], or \f[V]-?\f[R]
Prints a help message for the \f[V]javap\f[R] command.
.TP
\f[V]-version\f[R]
Prints release information.
.TP
\f[V]-verbose\f[R] or \f[V]-v\f[R]
Prints additional information about the selected class.
.TP
\f[V]-l\f[R]
Prints line and local variable tables.
.TP
\f[V]-public\f[R]
Shows only public classes and members.
.TP
\f[V]-protected\f[R]
Shows only protected and public classes and members.
.TP
\f[V]-package\f[R]
Shows package/protected/public classes and members (default).
.TP
\f[V]-private\f[R] or \f[V]-p\f[R]
Shows all classes and members.
.TP
\f[V]-c\f[R]
Prints disassembled code, for example, the instructions that comprise
the Java bytecodes, for each of the methods in the class.
.TP
\f[V]-s\f[R]
Prints internal type signatures.
.TP
\f[V]-sysinfo\f[R]
Shows system information (path, size, date, SHA-256 hash) of the class
being processed.
.TP
\f[V]-verify\f[R]
Prints additional class verification info.
.TP
\f[V]-constants\f[R]
Shows \f[V]static final\f[R] constants.
.TP
\f[V]--module\f[R] \f[I]module\f[R] or \f[V]-m\f[R] \f[I]module\f[R]
Specifies the module containing classes to be disassembled.
.TP
\f[V]--module-path\f[R] \f[I]path\f[R]
Specifies where to find application modules.
.TP
\f[V]--system\f[R] \f[I]jdk\f[R]
Specifies where to find system modules.
.TP
\f[V]--class-path\f[R] \f[I]path\f[R], \f[V]-classpath\f[R] \f[I]path\f[R], or \f[V]-cp\f[R] \f[I]path\f[R]
Specifies the path that the \f[V]javap\f[R] command uses to find user
class files.
It overrides the default or the \f[V]CLASSPATH\f[R] environment variable
when it\[aq]s set.
.TP
\f[V]-bootclasspath\f[R] \f[I]path\f[R]
Overrides the location of bootstrap class files.
.TP
\f[V]--multi-release\f[R] \f[I]version\f[R]
Specifies the version to select in multi-release JAR files.
.TP
\f[V]-J\f[R]\f[I]option\f[R]
Passes the specified option to the JVM.
For example:
.RS
.IP
.nf
\f[CB]
javap -J-version

javap -J-Djava.security.manager -J-Djava.security.policy=MyPolicy MyClassName
\f[R]
.fi
.PP
See \f[I]Overview of Java Options\f[R] in \f[B]java\f[R].
.RE
.SH JAVAP EXAMPLE
.PP
Compile the following \f[V]HelloWorldFrame\f[R] class:
.IP
.nf
\f[CB]
import java.awt.Graphics;

import javax.swing.JFrame;
import javax.swing.JPanel;

public class HelloWorldFrame extends JFrame {

   String message = \[dq]Hello World!\[dq];

   public HelloWorldFrame(){
        setContentPane(new JPanel(){
            \[at]Override
            protected void paintComponent(Graphics g) {
                g.drawString(message, 15, 30);
            }
        });
        setSize(100, 100);
    }
    public static void main(String[] args) {
        HelloWorldFrame frame = new HelloWorldFrame();
        frame.setVisible(true);

    }

}
\f[R]
.fi
.PP
The output from the \f[V]javap HelloWorldFrame.class\f[R] command yields
the following:
.IP
.nf
\f[CB]
Compiled from \[dq]HelloWorldFrame.java\[dq]
public class HelloWorldFrame extends javax.swing.JFrame {
  java.lang.String message;
  public HelloWorldFrame();
  public static void main(java.lang.String[]);
}
\f[R]
.fi
.PP
The output from the \f[V]javap -c HelloWorldFrame.class\f[R] command
yields the following:
.IP
.nf
\f[CB]
Compiled from \[dq]HelloWorldFrame.java\[dq]
public class HelloWorldFrame extends javax.swing.JFrame {
  java.lang.String message;

  public HelloWorldFrame();
    Code:
       0: aload_0
       1: invokespecial #1        // Method javax/swing/JFrame.\[dq]<init>\[dq]:()V
       4: aload_0
       5: ldc           #2        // String Hello World!
       7: putfield      #3        // Field message:Ljava/lang/String;
      10: aload_0
      11: new           #4        // class HelloWorldFrame$1
      14: dup
      15: aload_0
      16: invokespecial #5        // Method HelloWorldFrame$1.\[dq]<init>\[dq]:(LHelloWorldFrame;)V
      19: invokevirtual #6        // Method setContentPane:(Ljava/awt/Container;)V
      22: aload_0
      23: bipush        100
      25: bipush        100
      27: invokevirtual #7        // Method setSize:(II)V
      30: return

  public static void main(java.lang.String[]);
    Code:
       0: new           #8        // class HelloWorldFrame
       3: dup
       4: invokespecial #9        // Method \[dq]<init>\[dq]:()V
       7: astore_1
       8: aload_1
       9: iconst_1
      10: invokevirtual #10       // Method setVisible:(Z)V
      13: return
}
\f[R]
.fi
