.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "RMIREGISTRY" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
rmiregistry - create and start a remote object registry on the specified
port on the current host
.SH SYNOPSIS
.PP
\f[V]rmiregistry\f[R] [\f[I]options\f[R]] [\f[I]port\f[R]]
.TP
\f[I]options\f[R]
This represents the option for the \f[V]rmiregistry\f[R] command.
See \f[B]Options\f[R]
.TP
\f[I]port\f[R]
The number of a port on the current host at which to start the remote
object registry.
.SH DESCRIPTION
.PP
The \f[V]rmiregistry\f[R] command creates and starts a remote object
registry on the specified port on the current host.
If the port is omitted, then the registry is started on port 1099.
The \f[V]rmiregistry\f[R] command produces no output and is typically
run in the background, for example:
.RS
.PP
\f[V]rmiregistry &\f[R]
.RE
.PP
A remote object registry is a bootstrap naming service that\[aq]s used
by RMI servers on the same host to bind remote objects to names.
Clients on local and remote hosts can then look up remote objects and
make remote method invocations.
.PP
The registry is typically used to locate the first remote object on
which an application needs to call methods.
That object then provides application-specific support for finding other
objects.
.PP
The methods of the \f[V]java.rmi.registry.LocateRegistry\f[R] class are
used to get a registry operating on the local host or local host and
port.
.PP
The URL-based methods of the \f[V]java.rmi.Naming\f[R] class operate on
a registry and can be used to:
.IP \[bu] 2
Bind the specified name to a remote object
.IP \[bu] 2
Return an array of the names bound in the registry
.IP \[bu] 2
Return a reference, a stub, for the remote object associated with the
specified name
.IP \[bu] 2
Rebind the specified name to a new remote object
.IP \[bu] 2
Destroy the binding for the specified name that\[aq]s associated with a
remote object
.SH OPTIONS
.TP
\f[V]-J\f[R]\f[I]option\f[R]
Used with any Java option to pass the \f[I]option\f[R] following the
\f[V]-J\f[R] (no spaces between the \f[V]-J\f[R] and the option) to the
Java interpreter.
