'\" t
.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JAVA" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
java - launch a Java application
.SH SYNOPSIS
.PP
To launch a class file:
.PP
\f[V]java\f[R] [\f[I]options\f[R]] \f[I]mainclass\f[R] [\f[I]args\f[R]
\&...]
.PP
To launch the main class in a JAR file:
.PP
\f[V]java\f[R] [\f[I]options\f[R]] \f[V]-jar\f[R] \f[I]jarfile\f[R]
[\f[I]args\f[R] ...]
.PP
To launch the main class in a module:
.PP
\f[V]java\f[R] [\f[I]options\f[R]] \f[V]-m\f[R]
\f[I]module\f[R][\f[V]/\f[R]\f[I]mainclass\f[R]] [\f[I]args\f[R] ...]
.PP
or
.PP
\f[V]java\f[R] [\f[I]options\f[R]] \f[V]--module\f[R]
\f[I]module\f[R][\f[V]/\f[R]\f[I]mainclass\f[R]] [\f[I]args\f[R] ...]
.PP
To launch a source-file program:
.PP
\f[V]java\f[R] [\f[I]options\f[R]] \f[I]source-file\f[R] [\f[I]args\f[R]
\&...]
.TP
\f[I]options\f[R]
Optional: Specifies command-line options separated by spaces.
See \f[B]Overview of Java Options\f[R] for a description of available
options.
.TP
\f[I]mainclass\f[R]
Specifies the name of the class to be launched.
Command-line entries following \f[V]classname\f[R] are the arguments for
the main method.
.TP
\f[V]-jar\f[R] \f[I]jarfile\f[R]
Executes a program encapsulated in a JAR file.
The \f[I]jarfile\f[R] argument is the name of a JAR file with a manifest
that contains a line in the form
\f[V]Main-Class:\f[R]\f[I]classname\f[R] that defines the class with the
\f[V]public static void main(String[] args)\f[R] method that serves as
your application\[aq]s starting point.
When you use \f[V]-jar\f[R], the specified JAR file is the source of all
user classes, and other class path settings are ignored.
If you\[aq]re using JAR files, then see \f[B]jar\f[R].
.TP
\f[V]-m\f[R] or \f[V]--module\f[R] \f[I]module\f[R][\f[V]/\f[R]\f[I]mainclass\f[R]]
Executes the main class in a module specified by \f[I]mainclass\f[R] if
it is given, or, if it is not given, the value in the \f[I]module\f[R].
In other words, \f[I]mainclass\f[R] can be used when it is not specified
by the module, or to override the value when it is specified.
.RS
.PP
See \f[B]Standard Options for Java\f[R].
.RE
.TP
\f[I]source-file\f[R]
Only used to launch a source-file program.
Specifies the source file that contains the main class when using
source-file mode.
See \f[B]Using Source-File Mode to Launch Source-Code Programs\f[R]
.TP
\f[I]args\f[R] ...
Optional: Arguments following \f[I]mainclass\f[R],
\f[I]source-file\f[R], \f[V]-jar\f[R] \f[I]jarfile\f[R], and
\f[V]-m\f[R] or \f[V]--module\f[R]
\f[I]module\f[R]\f[V]/\f[R]\f[I]mainclass\f[R] are passed as arguments
to the main class.
.SH DESCRIPTION
.PP
The \f[V]java\f[R] command starts a Java application.
It does this by starting the Java Virtual Machine (JVM), loading the
specified class, and calling that class\[aq]s \f[V]main()\f[R] method.
The method must be declared \f[V]public\f[R] and \f[V]static\f[R], it
must not return any value, and it must accept a \f[V]String\f[R] array
as a parameter.
The method declaration has the following form:
.RS
.PP
\f[V]public static void main(String[] args)\f[R]
.RE
.PP
In source-file mode, the \f[V]java\f[R] command can launch a class
declared in a source file.
See \f[B]Using Source-File Mode to Launch Source-Code Programs\f[R] for
a description of using the source-file mode.
.RS
.PP
\f[B]Note:\f[R] You can use the \f[V]JDK_JAVA_OPTIONS\f[R] launcher
environment variable to prepend its content to the actual command line
of the \f[V]java\f[R] launcher.
See \f[B]Using the JDK_JAVA_OPTIONS Launcher Environment Variable\f[R].
.RE
.PP
By default, the first argument that isn\[aq]t an option of the
\f[V]java\f[R] command is the fully qualified name of the class to be
called.
If \f[V]-jar\f[R] is specified, then its argument is the name of the JAR
file containing class and resource files for the application.
The startup class must be indicated by the \f[V]Main-Class\f[R] manifest
header in its manifest file.
.PP
Arguments after the class file name or the JAR file name are passed to
the \f[V]main()\f[R] method.
.SS \f[V]javaw\f[R]
.PP
\f[B]Windows:\f[R] The \f[V]javaw\f[R] command is identical to
\f[V]java\f[R], except that with \f[V]javaw\f[R] there\[aq]s no
associated console window.
Use \f[V]javaw\f[R] when you don\[aq]t want a command prompt window to
appear.
The \f[V]javaw\f[R] launcher will, however, display a dialog box with
error information if a launch fails.
.SH USING SOURCE-FILE MODE TO LAUNCH SOURCE-CODE PROGRAMS
.PP
To launch a class declared in a source file, run the \f[V]java\f[R]
launcher in source-file mode.
Entering source-file mode is determined by two items on the
\f[V]java\f[R] command line:
.IP \[bu] 2
The first item on the command line that is not an option or part of an
option.
In other words, the item in the command line that would otherwise be the
main class name.
.IP \[bu] 2
The \f[V]--source\f[R] \f[I]version\f[R] option, if present.
.PP
If the class identifies an existing file that has a \f[V].java\f[R]
extension, or if the \f[V]--source\f[R] option is specified, then
source-file mode is selected.
The source file is then compiled and run.
The \f[V]--source\f[R] option can be used to specify the source
\f[I]version\f[R] or \f[I]N\f[R] of the source code.
This determines the API that can be used.
When you set \f[V]--source\f[R] \f[I]N\f[R], you can only use the public
API that was defined in JDK \f[I]N\f[R].
.RS
.PP
\f[B]Note:\f[R] The valid values of \f[I]N\f[R] change for each release,
with new values added and old values removed.
You\[aq]ll get an error message if you use a value of \f[I]N\f[R] that
is no longer supported.
The supported values of \f[I]N\f[R] are the current Java SE release
(\f[V]24\f[R]) and a limited number of previous releases, detailed in
the command-line help for \f[V]javac\f[R], under the \f[V]--source\f[R]
and \f[V]--release\f[R] options.
.RE
.PP
If the file does not have the \f[V].java\f[R] extension, the
\f[V]--source\f[R] option must be used to tell the \f[V]java\f[R]
command to use the source-file mode.
The \f[V]--source\f[R] option is used for cases when the source file is
a \[dq]script\[dq] to be executed and the name of the source file does
not follow the normal naming conventions for Java source files.
.PP
In source-file mode, the effect is as though the source file is compiled
into memory, and the first class found in the source file is executed.
Any arguments placed after the name of the source file in the original
command line are passed to the compiled class when it is executed.
.PP
For example, if a file were named \f[V]HelloWorld.java\f[R] and
contained a class named \f[V]HelloWorld\f[R], then the source-file mode
command to launch the class would be:
.RS
.PP
\f[V]java HelloWorld.java\f[R]
.RE
.PP
This use of source-file mode is informally equivalent to using the
following two commands:
.IP
.nf
\f[CB]
javac -d <memory> --source-path <source-root> HelloWorld.java
java --class-path <memory> HelloWorld
\f[R]
.fi
.PP
where \f[V]<source-root>\f[R] is computed
.PP
\f[B]In source-file mode, any additional command-line options are
processed as follows:\f[R]
.IP \[bu] 2
The launcher scans the options specified before the source file for any
that are relevant in order to compile the source file.
.RS 2
.PP
This includes: \f[V]--class-path\f[R], \f[V]--module-path\f[R],
\f[V]--add-exports\f[R], \f[V]--add-modules\f[R],
\f[V]--limit-modules\f[R], \f[V]--patch-module\f[R],
\f[V]--upgrade-module-path\f[R], and any variant forms of those options.
It also includes the new \f[V]--enable-preview\f[R] option, described in
JEP 12.
.RE
.IP \[bu] 2
No provision is made to pass any additional options to the compiler,
such as \f[V]-processor\f[R] or \f[V]-Werror\f[R].
.IP \[bu] 2
Command-line argument files (\f[V]\[at]\f[R]-files) may be used in the
standard way.
Long lists of arguments for either the VM or the program being invoked
may be placed in files specified on the command-line by prefixing the
filename with an \f[V]\[at]\f[R] character.
.PP
\f[B]In source-file mode, compilation proceeds as follows:\f[R]
.IP \[bu] 2
Any command-line options that are relevant to the compilation
environment are taken into account.
These include:
\f[V]--class-path\f[R]/\f[V]-classpath\f[R]/\f[V]-cp\f[R],
\f[V]--module-path\f[R]/\f[V]-p\f[R], \f[V]--add-exports\f[R],
\f[V]--add-modules\f[R], \f[V]--limit-modules\f[R],
\f[V]--patch-module\f[R], \f[V]--upgrade-module-path\f[R],
\f[V]--enable-preview\f[R].
.IP \[bu] 2
The root of the source tree, \f[V]<source-root>\f[R] is computed from
the package of the class being launched.
For example, if \f[V]HelloWorld.java\f[R] declared its classes to be in
the \f[V]hello\f[R] package, then the file \f[V]HelloWorld.java\f[R] is
expected to reside in the directory \f[V]somedir/hello/\f[R].
In this case, \f[V]somedir\f[R] is computed to be the root of the source
tree.
.IP \[bu] 2
The root of the source tree serves as the source-path for compilation,
so that other source files found in that tree and are needed by
\f[V]HelloWorld\f[R] could be compiled.
.IP \[bu] 2
Annotation processing is disabled, as if \f[V]-proc:none\f[R] is in
effect.
.IP \[bu] 2
If a version is specified, via the \f[V]--source\f[R] option, the value
is used as the argument for an implicit \f[V]--release\f[R] option for
the compilation.
This sets both the source version accepted by compiler and the system
API that may be used by the code in the source file.
.IP \[bu] 2
If \f[V]--enable-preview\f[R] is specified, the \f[V]--source N\f[R]
arguments can be omitted.
If the Java runtime version is \f[V]N\f[R], then \f[V]--release N\f[R]
is implied when compiling source files.
.IP \[bu] 2
If a \f[V]module-info.java\f[R] file exists in the
\f[V]<source-root>\f[R] directory, its module declaration is used to
define a named module that will contain all the classes compiled from
\f[V].java\f[R] files in the source tree.
If \f[V]module-info.java\f[R] does not exist, all the classes compiled
from source files will be compiled in the context of the unnamed module.
.IP \[bu] 2
The source file that is launched should contain one or more top-level
classes, the first of which is taken as the class to be executed.
.IP \[bu] 2
For the source file that is launched, the compiler does not enforce the
optional restriction defined at the end of JLS 7.6, that a type in a
named package should exist in a file whose name is composed from the
type name followed by the \f[V].java\f[R] extension.
.IP \[bu] 2
If a source file contains errors, appropriate error messages are written
to the standard error stream, and the launcher exits with a non-zero
exit code.
.PP
\f[B]In source-file mode, execution proceeds as follows:\f[R]
.IP \[bu] 2
The class to be executed is the first top-level class found in the
source file.
It must contain a declaration of an entry \f[V]main\f[R] method.
.IP \[bu] 2
The compiled classes are loaded by a custom class loader, that delegates
to the application class loader.
This implies that classes appearing on the application class path cannot
refer to any classes declared in source files.
.IP \[bu] 2
If a \f[V]module-info.java\f[R] file exists in the
\f[V]<source-root>\f[R] directory, then all the classes compiled from
\f[V].java\f[R] files in the source tree will be in that module, which
will serve as the root module for the execution of the program.
If \f[V]module-info.java\f[R] does not exist, the compiled classes are
executed in the context of an unnamed module, as though
\f[V]--add-modules=ALL-DEFAULT\f[R] is in effect.
This is in addition to any other \f[V]--add-module\f[R] options that may
be have been specified on the command line.
.IP \[bu] 2
Any arguments appearing after the name of the file on the command line
are passed to the main method in the obvious way.
.IP \[bu] 2
It is an error if there is a class on the application class path whose
name is the same as that of the class to be executed.
.PP
See \f[B]JEP 458: Launch Multi-File Source-Code Programs\f[R]
[https://openjdk.org/jeps/458] for complete details.
.SH USING THE JDK_JAVA_OPTIONS LAUNCHER ENVIRONMENT VARIABLE
.PP
\f[V]JDK_JAVA_OPTIONS\f[R] prepends its content to the options parsed
from the command line.
The content of the \f[V]JDK_JAVA_OPTIONS\f[R] environment variable is a
list of arguments separated by white-space characters (as determined by
\f[V]isspace()\f[R]).
These are prepended to the command line arguments passed to
\f[V]java\f[R] launcher.
The encoding requirement for the environment variable is the same as the
\f[V]java\f[R] command line on the system.
\f[V]JDK_JAVA_OPTIONS\f[R] environment variable content is treated in
the same manner as that specified in the command line.
.PP
Single (\f[V]\[aq]\f[R]) or double (\f[V]\[dq]\f[R]) quotes can be used
to enclose arguments that contain whitespace characters.
All content between the open quote and the first matching close quote
are preserved by simply removing the pair of quotes.
In case a matching quote is not found, the launcher will abort with an
error message.
\f[V]\[at]\f[R]-files are supported as they are specified in the command
line.
Any wildcard literal \f[V]*\f[R] in the \f[V]JDK_JAVA_OPTIONS\f[R]
environment variable content isn\[aq]t expanded and is passed as-is to
the starting VM.
In order to mitigate potential misuse of \f[V]JDK_JAVA_OPTIONS\f[R]
behavior, options that specify the main class (such as \f[V]-jar\f[R])
or cause the \f[V]java\f[R] launcher to exit without executing the main
class (such as \f[V]-h\f[R]) are disallowed in the environment variable.
If any of these options appear in the environment variable, the launcher
will abort with an error message.
When \f[V]JDK_JAVA_OPTIONS\f[R] is set, the launcher prints a message to
stderr as a reminder.
.PP
\f[B]Example:\f[R]
.IP
.nf
\f[CB]
$ export JDK_JAVA_OPTIONS=\[aq]-g \[at]file1 -Dprop=value \[at]file2 -Dws.prop=\[dq]white spaces\[dq]\[aq]
$ java -Xint \[at]file3
\f[R]
.fi
.PP
is equivalent to the command line:
.IP
.nf
\f[CB]
java -g \[at]file1 -Dprop=value \[at]file2 -Dws.prop=\[dq]white spaces\[dq] -Xint \[at]file3
\f[R]
.fi
.SH OVERVIEW OF JAVA OPTIONS
.PP
The \f[V]java\f[R] command supports a wide range of options in the
following categories:
.IP \[bu] 2
\f[B]Standard Options for Java\f[R]: Options guaranteed to be supported
by all implementations of the Java Virtual Machine (JVM).
They\[aq]re used for common actions, such as checking the version of the
JRE, setting the class path, enabling verbose output, and so on.
.IP \[bu] 2
\f[B]Extra Options for Java\f[R]: General purpose options that are
specific to the Java HotSpot Virtual Machine.
They aren\[aq]t guaranteed to be supported by all JVM implementations,
and are subject to change.
These options start with \f[V]-X\f[R].
.PP
The advanced options aren\[aq]t recommended for casual use.
These are developer options used for tuning specific areas of the Java
HotSpot Virtual Machine operation that often have specific system
requirements and may require privileged access to system configuration
parameters.
Several examples of performance tuning are provided in \f[B]Performance
Tuning Examples\f[R].
These options aren\[aq]t guaranteed to be supported by all JVM
implementations and are subject to change.
Advanced options start with \f[V]-XX\f[R].
.IP \[bu] 2
\f[B]Advanced Runtime Options for Java\f[R]: Control the runtime
behavior of the Java HotSpot VM.
.IP \[bu] 2
\f[B]Advanced JIT Compiler Options for java\f[R]: Control the dynamic
just-in-time (JIT) compilation performed by the Java HotSpot VM.
.IP \[bu] 2
\f[B]Advanced Serviceability Options for Java\f[R]: Enable gathering
system information and performing extensive debugging.
.IP \[bu] 2
\f[B]Advanced Garbage Collection Options for Java\f[R]: Control how
garbage collection (GC) is performed by the Java HotSpot
.PP
Boolean options are used to either enable a feature that\[aq]s disabled
by default or disable a feature that\[aq]s enabled by default.
Such options don\[aq]t require a parameter.
Boolean \f[V]-XX\f[R] options are enabled using the plus sign
(\f[V]-XX:+\f[R]\f[I]OptionName\f[R]) and disabled using the minus sign
(\f[V]-XX:-\f[R]\f[I]OptionName\f[R]).
.PP
For options that require an argument, the argument may be separated from
the option name by a space, a colon (:), or an equal sign (=), or the
argument may directly follow the option (the exact syntax differs for
each option).
If you\[aq]re expected to specify the size in bytes, then you can use no
suffix, or use the suffix \f[V]k\f[R] or \f[V]K\f[R] for kilobytes (KB),
\f[V]m\f[R] or \f[V]M\f[R] for megabytes (MB), or \f[V]g\f[R] or
\f[V]G\f[R] for gigabytes (GB).
For example, to set the size to 8 GB, you can specify either
\f[V]8g\f[R], \f[V]8192m\f[R], \f[V]8388608k\f[R], or
\f[V]8589934592\f[R] as the argument.
If you are expected to specify the percentage, then use a number from 0
to 1.
For example, specify \f[V]0.25\f[R] for 25%.
.PP
The following sections describe the options that are deprecated,
obsolete, and removed:
.IP \[bu] 2
\f[B]Deprecated Java Options\f[R]: Accepted and acted upon --- a warning
is issued when they\[aq]re used.
.IP \[bu] 2
\f[B]Obsolete Java Options\f[R]: Accepted but ignored --- a warning is
issued when they\[aq]re used.
.IP \[bu] 2
\f[B]Removed Java Options\f[R]: Removed --- using them results in an
error.
.SH STANDARD OPTIONS FOR JAVA
.PP
These are the most commonly used options supported by all
implementations of the JVM.
.RS
.PP
\f[B]Note:\f[R] To specify an argument for a long option, you can use
either \f[V]--\f[R]\f[I]name\f[R]\f[V]=\f[R]\f[I]value\f[R] or
\f[V]--\f[R]\f[I]name\f[R] \f[I]value\f[R].
.RE
.TP
\f[V]-agentlib:\f[R]\f[I]libname\f[R][\f[V]=\f[R]\f[I]options\f[R]]
Loads the specified native agent library.
After the library name, a comma-separated list of options specific to
the library can be used.
If the option \f[V]-agentlib:foo\f[R] is specified, then the JVM
attempts to load the library named \f[V]foo\f[R] using the platform
specific naming conventions and locations:
.RS
.IP \[bu] 2
\f[B]Linux and other POSIX-like platforms:\f[R] The JVM attempts to load
the library named \f[V]libfoo.so\f[R] in the location specified by the
\f[V]LD_LIBRARY_PATH\f[R] system variable.
.IP \[bu] 2
\f[B]macOS:\f[R] The JVM attempts to load the library named
\f[V]libfoo.dylib\f[R] in the location specified by the
\f[V]DYLD_LIBRARY_PATH\f[R] system variable.
.IP \[bu] 2
\f[B]Windows:\f[R] The JVM attempts to load the library named
\f[V]foo.dll\f[R] in the location specified by the \f[V]PATH\f[R] system
variable.
.RS 2
.PP
The following example shows how to load the Java Debug Wire Protocol
(JDWP) library and listen for the socket connection on port 8000,
suspending the JVM before the main class loads:
.RS
.PP
\f[V]-agentlib:jdwp=transport=dt_socket,server=y,address=8000\f[R]
.RE
.RE
.RE
.TP
\f[V]-agentpath:\f[R]\f[I]pathname\f[R][\f[V]=\f[R]\f[I]options\f[R]]
Loads the native agent library specified by the absolute path name.
This option is equivalent to \f[V]-agentlib\f[R] but uses the full path
and file name of the library.
.TP
\f[V]--class-path\f[R] \f[I]classpath\f[R], \f[V]-classpath\f[R] \f[I]classpath\f[R], or \f[V]-cp\f[R] \f[I]classpath\f[R]
Specifies a list of directories, JAR files, and ZIP archives to search
for class files.
.RS
.PP
On Windows, semicolons (\f[V];\f[R]) separate entities in this list; on
other platforms it is a colon (\f[V]:\f[R]).
.PP
Specifying \f[I]classpath\f[R] overrides any setting of the
\f[V]CLASSPATH\f[R] environment variable.
If the class path option isn\[aq]t used and \f[I]classpath\f[R]
isn\[aq]t set, then the user class path consists of the current
directory (.).
.PP
As a special convenience, a class path element that contains a base name
of an asterisk (*) is considered equivalent to specifying a list of all
the files in the directory with the extension \f[V].jar\f[R] or
\f[V].JAR\f[R] .
A Java program can\[aq]t tell the difference between the two
invocations.
For example, if the directory mydir contains \f[V]a.jar\f[R] and
\f[V]b.JAR\f[R], then the class path element mydir/* is expanded to
\f[V]A.jar:b.JAR\f[R], except that the order of JAR files is
unspecified.
All \f[V].jar\f[R] files in the specified directory, even hidden ones,
are included in the list.
A class path entry consisting of an asterisk (*) expands to a list of
all the jar files in the current directory.
The \f[V]CLASSPATH\f[R] environment variable, where defined, is
similarly expanded.
Any class path wildcard expansion that occurs before the Java VM is
started.
Java programs never see wildcards that aren\[aq]t expanded except by
querying the environment, such as by calling
\f[V]System.getenv(\[dq]CLASSPATH\[dq])\f[R].
.RE
.TP
\f[V]--disable-\[at]files\f[R]
Can be used anywhere on the command line, including in an argument file,
to prevent further \f[V]\[at]filename\f[R] expansion.
This option stops expanding \f[V]\[at]\f[R]-argfiles after the option.
.TP
\f[V]--enable-preview\f[R]
Allows classes to depend on \f[B]preview features\f[R]
[https://docs.oracle.com/en/java/javase/12/language/index.html#JSLAN-GUID-5A82FE0E-0CA4-4F1F-B075-564874FE2823]
of the release.
.TP
\f[V]--enable-native-access\f[R] \f[I]module\f[R][\f[V],\f[R]\f[I]module\f[R]...]
Native access involves access to code or data outside the Java runtime.
This is generally unsafe and, if done incorrectly, might crash the JVM
or result in memory corruption.
Native access can occur as a result of calling a method that is either
\f[B]restricted\f[R] [https://openjdk.org/jeps/454#Safety], or
\f[V]native\f[R].
This option allows code in the specified modules to perform native
access.
Native access occurring in a module that has not been explicitly enabled
is deemed \f[I]illegal\f[R].
.RS
.PP
\f[I]module\f[R] can be a module name, or \f[V]ALL-UNNAMED\f[R] to
indicate code on the class path.
.RE
.TP
-\f[V]--illegal-native-access=\f[R]\f[I]parameter\f[R]
This option specifies a mode for how illegal native access is handled:
.RS
.RS
.PP
\f[B]Note:\f[R] This option will be removed in a future release.
.RE
.IP \[bu] 2
\f[V]allow\f[R]: This mode allows illegal native access in all modules,
without any warings.
.IP \[bu] 2
\f[V]warn\f[R]: This mode is identical to \f[V]allow\f[R] except that a
warning message is issued for the first illegal native access found in a
module.
This mode is the default for the current JDK but will change in a future
release.
.IP \[bu] 2
\f[V]deny\f[R]: This mode disables illegal native access.
That is, any illegal native access causes an
\f[V]IllegalCallerException\f[R].
This mode will become the default in a future release.
.PP
To verify that your application is ready for a future version of the
JDK, run it with \f[V]--illegal-native-access=deny\f[R] along with any
necessary \f[V]--enable-native-access\f[R] options.
.RE
.TP
\f[V]--finalization=\f[R]\f[I]value\f[R]
Controls whether the JVM performs finalization of objects.
Valid values are \[dq]enabled\[dq] and \[dq]disabled\[dq].
Finalization is enabled by default, so the value \[dq]enabled\[dq] does
nothing.
The value \[dq]disabled\[dq] disables finalization, so that no
finalizers are invoked.
.TP
\f[V]--module-path\f[R] \f[I]modulepath\f[R]... or \f[V]-p\f[R] \f[I]modulepath\f[R]
Specifies where to find application modules with a list of path
elements.
The elements of a module path can be a file path to a module or a
directory containing modules.
Each module is either a modular JAR or an exploded-module directory.
.RS
.PP
On Windows, semicolons (\f[V];\f[R]) separate path elements in this
list; on other platforms it is a colon (\f[V]:\f[R]).
.RE
.TP
\f[V]--upgrade-module-path\f[R] \f[I]modulepath\f[R]...
Specifies where to find module replacements of upgradeable modules in
the runtime image with a list of path elements.
The elements of a module path can be a file path to a module or a
directory containing modules.
Each module is either a modular JAR or an exploded-module directory.
.RS
.PP
On Windows, semicolons (\f[V];\f[R]) separate path elements in this
list; on other platforms it is a colon (\f[V]:\f[R]).
.RE
.TP
\f[V]--add-modules\f[R] \f[I]module\f[R][\f[V],\f[R]\f[I]module\f[R]...]
Specifies the root modules to resolve in addition to the initial module.
\f[I]module\f[R] can also be \f[V]ALL-DEFAULT\f[R],
\f[V]ALL-SYSTEM\f[R], and \f[V]ALL-MODULE-PATH\f[R].
.TP
\f[V]--list-modules\f[R]
Lists the observable modules and then exits.
.TP
\f[V]-d\f[R] \f[I]module_name\f[R] or \f[V]--describe-module\f[R] \f[I]module_name\f[R]
Describes a specified module and then exits.
.TP
\f[V]--dry-run\f[R]
Creates the VM but doesn\[aq]t execute the main method.
This \f[V]--dry-run\f[R] option might be useful for validating the
command-line options such as the module system configuration.
.TP
\f[V]--validate-modules\f[R]
Validates all modules and exit.
This option is helpful for finding conflicts and other errors with
modules on the module path.
.TP
\f[V]-D\f[R]\f[I]property\f[R]\f[V]=\f[R]\f[I]value\f[R]
Sets a system property value.
The \f[I]property\f[R] variable is a string with no spaces that
represents the name of the property.
The \f[I]value\f[R] variable is a string that represents the value of
the property.
If \f[I]value\f[R] is a string with spaces, then enclose it in quotation
marks (for example \f[V]-Dfoo=\[dq]foo bar\[dq]\f[R]).
.TP
\f[V]-disableassertions\f[R][\f[V]:\f[R][\f[I]packagename\f[R]]...|\f[V]:\f[R]\f[I]classname\f[R]] or \f[V]-da\f[R][\f[V]:\f[R][\f[I]packagename\f[R]]...|\f[V]:\f[R]\f[I]classname\f[R]]
Disables assertions.
By default, assertions are disabled in all packages and classes.
With no arguments, \f[V]-disableassertions\f[R] (\f[V]-da\f[R]) disables
assertions in all packages and classes.
With the \f[I]packagename\f[R] argument ending in \f[V]...\f[R], the
switch disables assertions in the specified package and any subpackages.
If the argument is simply \f[V]...\f[R], then the switch disables
assertions in the unnamed package in the current working directory.
With the \f[I]classname\f[R] argument, the switch disables assertions in
the specified class.
.RS
.PP
The \f[V]-disableassertions\f[R] (\f[V]-da\f[R]) option applies to all
class loaders and to system classes (which don\[aq]t have a class
loader).
There\[aq]s one exception to this rule: If the option is provided with
no arguments, then it doesn\[aq]t apply to system classes.
This makes it easy to disable assertions in all classes except for
system classes.
The \f[V]-disablesystemassertions\f[R] option enables you to disable
assertions in all system classes.
To explicitly enable assertions in specific packages or classes, use the
\f[V]-enableassertions\f[R] (\f[V]-ea\f[R]) option.
Both options can be used at the same time.
For example, to run the \f[V]MyClass\f[R] application with assertions
enabled in the package \f[V]com.wombat.fruitbat\f[R] (and any
subpackages) but disabled in the class
\f[V]com.wombat.fruitbat.Brickbat\f[R], use the following command:
.RS
.PP
\f[V]java -ea:com.wombat.fruitbat... -da:com.wombat.fruitbat.Brickbat MyClass\f[R]
.RE
.RE
.TP
\f[V]-disablesystemassertions\f[R] or \f[V]-dsa\f[R]
Disables assertions in all system classes.
.TP
\f[V]-enableassertions\f[R][\f[V]:\f[R][\f[I]packagename\f[R]]...|\f[V]:\f[R]\f[I]classname\f[R]] or \f[V]-ea\f[R][\f[V]:\f[R][\f[I]packagename\f[R]]...|\f[V]:\f[R]\f[I]classname\f[R]]
Enables assertions.
By default, assertions are disabled in all packages and classes.
With no arguments, \f[V]-enableassertions\f[R] (\f[V]-ea\f[R]) enables
assertions in all packages and classes.
With the \f[I]packagename\f[R] argument ending in \f[V]...\f[R], the
switch enables assertions in the specified package and any subpackages.
If the argument is simply \f[V]...\f[R], then the switch enables
assertions in the unnamed package in the current working directory.
With the \f[I]classname\f[R] argument, the switch enables assertions in
the specified class.
.RS
.PP
The \f[V]-enableassertions\f[R] (\f[V]-ea\f[R]) option applies to all
class loaders and to system classes (which don\[aq]t have a class
loader).
There\[aq]s one exception to this rule: If the option is provided with
no arguments, then it doesn\[aq]t apply to system classes.
This makes it easy to enable assertions in all classes except for system
classes.
The \f[V]-enablesystemassertions\f[R] option provides a separate switch
to enable assertions in all system classes.
To explicitly disable assertions in specific packages or classes, use
the \f[V]-disableassertions\f[R] (\f[V]-da\f[R]) option.
If a single command contains multiple instances of these switches, then
they\[aq]re processed in order, before loading any classes.
For example, to run the \f[V]MyClass\f[R] application with assertions
enabled only in the package \f[V]com.wombat.fruitbat\f[R] (and any
subpackages) but disabled in the class
\f[V]com.wombat.fruitbat.Brickbat\f[R], use the following command:
.RS
.PP
\f[V]java -ea:com.wombat.fruitbat... -da:com.wombat.fruitbat.Brickbat MyClass\f[R]
.RE
.RE
.TP
\f[V]-enablesystemassertions\f[R] or \f[V]-esa\f[R]
Enables assertions in all system classes.
.TP
\f[V]-help\f[R], \f[V]-h\f[R], or \f[V]-?\f[R]
Prints the help message to the error stream.
.TP
\f[V]--help\f[R]
Prints the help message to the output stream.
.TP
\f[V]-javaagent:\f[R]\f[I]jarpath\f[R][\f[V]=\f[R]\f[I]options\f[R]]
Loads the specified Java programming language agent.
See \f[V]java.lang.instrument\f[R].
.TP
\f[V]--show-version\f[R]
Prints the product version to the output stream and continues.
.TP
\f[V]-showversion\f[R]
Prints the product version to the error stream and continues.
.TP
\f[V]--show-module-resolution\f[R]
Shows module resolution output during startup.
.TP
\f[V]-splash:\f[R]\f[I]imagepath\f[R]
Shows the splash screen with the image specified by \f[I]imagepath\f[R].
HiDPI scaled images are automatically supported and used if available.
The unscaled image file name, such as \f[V]image.ext\f[R], should always
be passed as the argument to the \f[V]-splash\f[R] option.
The most appropriate scaled image provided is picked up automatically.
.RS
.PP
For example, to show the \f[V]splash.gif\f[R] file from the
\f[V]images\f[R] directory when starting your application, use the
following option:
.RS
.PP
\f[V]-splash:images/splash.gif\f[R]
.RE
.PP
See the SplashScreen API documentation for more information.
.RE
.TP
\f[V]-verbose:class\f[R]
Displays information about each loaded class.
.TP
\f[V]-verbose:gc\f[R]
Displays information about each garbage collection (GC) event.
.TP
\f[V]-verbose:jni\f[R]
Displays information about the use of native methods and other Java
Native Interface (JNI) activity.
.TP
\f[V]-verbose:module\f[R]
Displays information about the modules in use.
.TP
\f[V]--version\f[R]
Prints product version to the output stream and exits.
.TP
\f[V]-version\f[R]
Prints product version to the error stream and exits.
.TP
\f[V]-X\f[R]
Prints the help on extra options to the error stream.
.TP
\f[V]--help-extra\f[R]
Prints the help on extra options to the output stream.
.TP
\f[V]\[at]\f[R]\f[I]argfile\f[R]
Specifies one or more argument files prefixed by \f[V]\[at]\f[R] used by
the \f[V]java\f[R] command.
It isn\[aq]t uncommon for the \f[V]java\f[R] command line to be very
long because of the \f[V].jar\f[R] files needed in the classpath.
The \f[V]\[at]\f[R]\f[I]argfile\f[R] option overcomes command-line
length limitations by enabling the launcher to expand the contents of
argument files after shell expansion, but before argument processing.
Contents in the argument files are expanded because otherwise, they
would be specified on the command line until the
\f[V]--disable-\[at]files\f[R] option was encountered.
.RS
.PP
The argument files can also contain the main class name and all options.
If an argument file contains all of the options required by the
\f[V]java\f[R] command, then the command line could simply be:
.RS
.PP
\f[V]java \[at]\f[R]\f[I]argfile\f[R]
.RE
.PP
See \f[B]java Command-Line Argument Files\f[R] for a description and
examples of using \f[V]\[at]\f[R]-argfiles.
.RE
.SH EXTRA OPTIONS FOR JAVA
.PP
The following \f[V]java\f[R] options are general purpose options that
are specific to the Java HotSpot Virtual Machine.
.TP
\f[V]-Xbatch\f[R]
Disables background compilation.
By default, the JVM compiles the method as a background task, running
the method in interpreter mode until the background compilation is
finished.
The \f[V]-Xbatch\f[R] flag disables background compilation so that
compilation of all methods proceeds as a foreground task until
completed.
This option is equivalent to \f[V]-XX:-BackgroundCompilation\f[R].
.TP
\f[V]-Xbootclasspath/a:\f[R]\f[I]directories\f[R]|\f[I]zip\f[R]|\f[I]JAR-files\f[R]
Specifies a list of directories, JAR files, and ZIP archives to append
to the end of the default bootstrap class path.
.RS
.PP
On Windows, semicolons (\f[V];\f[R]) separate entities in this list; on
other platforms it is a colon (\f[V]:\f[R]).
.RE
.TP
\f[V]-Xcheck:jni\f[R]
Performs additional checks for Java Native Interface (JNI) functions.
.RS
.PP
The following checks are considered indicative of significant problems
with the native code, and the JVM terminates with an irrecoverable error
in such cases:
.IP \[bu] 2
The thread doing the call is not attached to the JVM.
.IP \[bu] 2
The thread doing the call is using the \f[V]JNIEnv\f[R] belonging to
another thread.
.IP \[bu] 2
A parameter validation check fails:
.RS 2
.IP \[bu] 2
A \f[V]jfieldID\f[R], or \f[V]jmethodID\f[R], is detected as being
invalid.
For example:
.RS 2
.IP \[bu] 2
Of the wrong type
.IP \[bu] 2
Associated with the wrong class
.RE
.IP \[bu] 2
A parameter of the wrong type is detected.
.IP \[bu] 2
An invalid parameter value is detected.
For example:
.RS 2
.IP \[bu] 2
NULL where not permitted
.IP \[bu] 2
An out-of-bounds array index, or frame capacity
.IP \[bu] 2
A non-UTF-8 string
.IP \[bu] 2
An invalid JNI reference
.IP \[bu] 2
An attempt to use a \f[V]ReleaseXXX\f[R] function on a parameter not
produced by the corresponding \f[V]GetXXX\f[R] function
.RE
.RE
.PP
The following checks only result in warnings being printed:
.IP \[bu] 2
A JNI call was made without checking for a pending exception from a
previous JNI call, and the current call is not safe when an exception
may be pending.
.IP \[bu] 2
A class descriptor is in decorated format (\f[V]Lname;\f[R]) when it
should not be.
.IP \[bu] 2
A \f[V]NULL\f[R] parameter is allowed, but its use is questionable.
.IP \[bu] 2
Calling other JNI functions in the scope of
\f[V]Get/ReleasePrimitiveArrayCritical\f[R] or
\f[V]Get/ReleaseStringCritical\f[R]
.PP
Expect a performance degradation when this option is used.
.RE
.TP
\f[V]-Xcomp\f[R]
Testing mode to exercise JIT compilers.
This option should not be used in production environments.
.TP
\f[V]-Xdebug\f[R]
Does nothing; deprecated for removal in a future release.
.TP
\f[V]-Xdiag\f[R]
Shows additional diagnostic messages.
.TP
\f[V]-Xint\f[R]
Runs the application in interpreted-only mode.
Compilation to native code is disabled, and all bytecode is executed by
the interpreter.
The performance benefits offered by the just-in-time (JIT) compiler
aren\[aq]t present in this mode.
.TP
\f[V]-Xinternalversion\f[R]
Displays more detailed JVM version information than the
\f[V]-version\f[R] option, and then exits.
.TP
\f[V]-Xlog:\f[R]\f[I]option\f[R]
Configure or enable logging with the Java Virtual Machine (JVM) unified
logging framework.
See \f[B]Enable Logging with the JVM Unified Logging Framework\f[R].
.TP
\f[V]-Xmixed\f[R]
Executes all bytecode by the interpreter except for hot methods, which
are compiled to native code.
On by default.
Use \f[V]-Xint\f[R] to switch off.
.TP
\f[V]-Xmn\f[R] \f[I]size\f[R]
Sets the initial and maximum size (in bytes) of the heap for the young
generation (nursery) in the generational collectors.
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
The young generation region of the heap is used for new objects.
GC is performed in this region more often than in other regions.
If the size for the young generation is too small, then a lot of minor
garbage collections are performed.
If the size is too large, then only full garbage collections are
performed, which can take a long time to complete.
It is recommended that you do not set the size for the young generation
for the G1 collector, and keep the size for the young generation greater
than 25% and less than 50% of the overall heap size for other
collectors.
The following examples show how to set the initial and maximum size of
young generation to 256 MB using various units:
.RS
.IP
.nf
\f[CB]
-Xmn256m
-Xmn262144k
-Xmn268435456
\f[R]
.fi
.PP
Instead of the \f[V]-Xmn\f[R] option to set both the initial and maximum
size of the heap for the young generation, you can use
\f[V]-XX:NewSize\f[R] to set the initial size and
\f[V]-XX:MaxNewSize\f[R] to set the maximum size.
.RE
.TP
\f[V]-Xms\f[R] \f[I]size\f[R]
Sets the minimum and the initial size (in bytes) of the heap.
This value must be a multiple of 1024 and greater than 1 MB.
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
The following examples show how to set the size of allocated memory to 6
MB using various units:
.RS
.IP
.nf
\f[CB]
-Xms6291456
-Xms6144k
-Xms6m
\f[R]
.fi
.PP
If you do not set this option, then the initial size will be set as the
sum of the sizes allocated for the old generation and the young
generation.
The initial size of the heap for the young generation can be set using
the \f[V]-Xmn\f[R] option or the \f[V]-XX:NewSize\f[R] option.
.PP
Note that the \f[V]-XX:InitialHeapSize\f[R] option can also be used to
set the initial heap size.
If it appears after \f[V]-Xms\f[R] on the command line, then the initial
heap size gets set to the value specified with
\f[V]-XX:InitialHeapSize\f[R].
.RE
.TP
\f[V]-Xmx\f[R] \f[I]size\f[R]
Specifies the maximum size (in bytes) of the heap.
This value must be a multiple of 1024 and greater than 2 MB.
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
The default value is chosen at runtime based on system configuration.
For server deployments, \f[V]-Xms\f[R] and \f[V]-Xmx\f[R] are often set
to the same value.
The following examples show how to set the maximum allowed size of
allocated memory to 80 MB using various units:
.RS
.IP
.nf
\f[CB]
-Xmx83886080
-Xmx81920k
-Xmx80m
\f[R]
.fi
.PP
The \f[V]-Xmx\f[R] option is equivalent to \f[V]-XX:MaxHeapSize\f[R].
.RE
.TP
\f[V]-Xnoclassgc\f[R]
Disables garbage collection (GC) of classes.
This can save some GC time, which shortens interruptions during the
application run.
When you specify \f[V]-Xnoclassgc\f[R] at startup, the class objects in
the application are left untouched during GC and are always be
considered live.
This can result in more memory being permanently occupied which, if not
used carefully, throws an out-of-memory exception.
.TP
\f[V]-Xrs\f[R]
Reduces the use of operating system signals by the JVM.
Shutdown hooks enable the orderly shutdown of a Java application by
running user cleanup code (such as closing database connections) at
shutdown, even if the JVM terminates abruptly.
.RS
.IP \[bu] 2
\f[B]Non-Windows:\f[R]
.RS 2
.IP \[bu] 2
The JVM catches signals to implement shutdown hooks for unexpected
termination.
The JVM uses \f[V]SIGHUP\f[R], \f[V]SIGINT\f[R], and \f[V]SIGTERM\f[R]
to initiate the running of shutdown hooks.
.IP \[bu] 2
Applications embedding the JVM frequently need to trap signals such as
\f[V]SIGINT\f[R] or \f[V]SIGTERM\f[R], which can lead to interference
with the JVM signal handlers.
The \f[V]-Xrs\f[R] option is available to address this issue.
When \f[V]-Xrs\f[R] is used, the signal masks for \f[V]SIGINT\f[R],
\f[V]SIGTERM\f[R], \f[V]SIGHUP\f[R], and \f[V]SIGQUIT\f[R] aren\[aq]t
changed by the JVM, and signal handlers for these signals aren\[aq]t
installed.
.RE
.IP \[bu] 2
\f[B]Windows:\f[R]
.RS 2
.IP \[bu] 2
The JVM watches for console control events to implement shutdown hooks
for unexpected termination.
Specifically, the JVM registers a console control handler that begins
shutdown-hook processing and returns \f[V]TRUE\f[R] for
\f[V]CTRL_C_EVENT\f[R], \f[V]CTRL_CLOSE_EVENT\f[R],
\f[V]CTRL_LOGOFF_EVENT\f[R], and \f[V]CTRL_SHUTDOWN_EVENT\f[R].
.IP \[bu] 2
The JVM uses a similar mechanism to implement the feature of dumping
thread stacks for debugging purposes.
The JVM uses \f[V]CTRL_BREAK_EVENT\f[R] to perform thread dumps.
.IP \[bu] 2
If the JVM is run as a service (for example, as a servlet engine for a
web server), then it can receive \f[V]CTRL_LOGOFF_EVENT\f[R] but
shouldn\[aq]t initiate shutdown because the operating system doesn\[aq]t
actually terminate the process.
To avoid possible interference such as this, the \f[V]-Xrs\f[R] option
can be used.
When the \f[V]-Xrs\f[R] option is used, the JVM doesn\[aq]t install a
console control handler, implying that it doesn\[aq]t watch for or
process \f[V]CTRL_C_EVENT\f[R], \f[V]CTRL_CLOSE_EVENT\f[R],
\f[V]CTRL_LOGOFF_EVENT\f[R], or \f[V]CTRL_SHUTDOWN_EVENT\f[R].
.RE
.PP
There are two consequences of specifying \f[V]-Xrs\f[R]:
.IP \[bu] 2
\f[B]Non-Windows:\f[R] \f[V]SIGQUIT\f[R] thread dumps aren\[aq]t
available.
.IP \[bu] 2
\f[B]Windows:\f[R] Ctrl + Break thread dumps aren\[aq]t available.
.PP
User code is responsible for causing shutdown hooks to run, for example,
by calling \f[V]System.exit()\f[R] when the JVM is to be terminated.
.RE
.TP
\f[V]-Xshare:\f[R]\f[I]mode\f[R]
Sets the class data sharing (CDS) mode.
.RS
.PP
Possible \f[I]mode\f[R] arguments for this option include the following:
.TP
\f[V]auto\f[R]
Use shared class data if possible (default).
.TP
\f[V]on\f[R]
Require using shared class data, otherwise fail.
.RS
.PP
\f[B]Note:\f[R] The \f[V]-Xshare:on\f[R] option is used for testing
purposes only.
It may cause the VM to unexpectedly exit during start-up when the CDS
archive cannot be used (for example, when certain VM parameters are
changed, or when a different JDK is used).
This option should not be used in production environments.
.RE
.TP
\f[V]off\f[R]
Do not attempt to use shared class data.
.RE
.TP
\f[V]-XshowSettings\f[R]
Shows all settings and then continues.
.TP
\f[V]-XshowSettings:\f[R]\f[I]category\f[R]
Shows settings and continues.
Possible \f[I]category\f[R] arguments for this option include the
following:
.RS
.TP
\f[V]all\f[R]
Shows all categories of settings in \f[B]verbose\f[R] detail.
.TP
\f[V]locale\f[R]
Shows settings related to locale.
.TP
\f[V]properties\f[R]
Shows settings related to system properties.
.TP
\f[V]security\f[R]
Shows all settings related to security.
.RS
.PP
sub-category arguments for \f[V]security\f[R] include the following:
.IP \[bu] 2
\f[V]security:all\f[R] : shows all security settings
.IP \[bu] 2
\f[V]security:properties\f[R] : shows security properties
.IP \[bu] 2
\f[V]security:providers\f[R] : shows static security provider settings
.IP \[bu] 2
\f[V]security:tls\f[R] : shows TLS related security settings
.RE
.TP
\f[V]vm\f[R]
Shows the settings of the JVM.
.TP
\f[V]system\f[R]
\f[B]Linux only:\f[R] Shows host system or container configuration and
continues.
.RE
.TP
\f[V]-Xss\f[R] \f[I]size\f[R]
Sets the thread stack size (in bytes).
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate KB, \f[V]m\f[R]
or \f[V]M\f[R] to indicate MB, or \f[V]g\f[R] or \f[V]G\f[R] to indicate
GB.
The actual size may be rounded up to a multiple of the system page size
as required by the operating system.
The default value depends on the platform.
For example:
.RS
.IP \[bu] 2
Linux/x64: 1024 KB
.IP \[bu] 2
Linux/Aarch64: 2048 KB
.IP \[bu] 2
macOS/x64: 1024 KB
.IP \[bu] 2
macOS/Aarch64: 2048 KB
.IP \[bu] 2
Windows: The default value depends on virtual memory
.PP
The following examples set the thread stack size to 1024 KB in different
units:
.IP
.nf
\f[CB]
-Xss1m
-Xss1024k
-Xss1048576
\f[R]
.fi
.PP
This option is similar to \f[V]-XX:ThreadStackSize\f[R].
.RE
.TP
\f[V]--add-reads\f[R] \f[I]module\f[R]\f[V]=\f[R]\f[I]target-module\f[R](\f[V],\f[R]\f[I]target-module\f[R])*
Updates \f[I]module\f[R] to read the \f[I]target-module\f[R], regardless
of the module declaration.
\f[I]target-module\f[R] can be \f[V]ALL-UNNAMED\f[R] to read all unnamed
modules.
.TP
\f[V]--add-exports\f[R] \f[I]module\f[R]\f[V]/\f[R]\f[I]package\f[R]\f[V]=\f[R]\f[I]target-module\f[R](\f[V],\f[R]\f[I]target-module\f[R])*
Updates \f[I]module\f[R] to export \f[I]package\f[R] to
\f[I]target-module\f[R], regardless of module declaration.
\f[I]target-module\f[R] can be \f[V]ALL-UNNAMED\f[R] to export to all
unnamed modules.
.TP
\f[V]--add-opens\f[R] \f[I]module\f[R]\f[V]/\f[R]\f[I]package\f[R]\f[V]=\f[R]\f[I]target-module\f[R](\f[V],\f[R]\f[I]target-module\f[R])*
Updates \f[I]module\f[R] to open \f[I]package\f[R] to
\f[I]target-module\f[R], regardless of module declaration.
.TP
\f[V]--limit-modules\f[R] \f[I]module\f[R][\f[V],\f[R]\f[I]module\f[R]...]
Specifies the limit of the universe of observable modules.
.TP
\f[V]--patch-module\f[R] \f[I]module\f[R]\f[V]=\f[R]\f[I]file\f[R](\f[V];\f[R]\f[I]file\f[R])*
Overrides or augments a module with classes and resources in JAR files
or directories.
.TP
\f[V]--source\f[R] \f[I]version\f[R]
Sets the version of the source in source-file mode.
.TP
\f[V]--sun-misc-unsafe-memory-access=\f[R] \f[I]value\f[R]
Allow or deny usage of unsupported API \f[V]sun.misc.Unsafe\f[R].
\f[I]value\f[R] is one of:
.RS
.TP
\f[V]allow\f[R]
Allow use of the memory-access methods with no warnings at run time.
.TP
\f[V]warn\f[R]
Allow use of the memory-access methods, but issues a warning on the
first occasion that any memory-access method is used.
At most one warning is issued.
.TP
\f[V]debug\f[R]
Allow use of the memory-access methods, but issue a one-line warning and
a stack trace when any memory-access method is used.
.TP
\f[V]deny\f[R]
Disallow use of the memory-access methods by throwing an
\f[V]UnsupportedOperationException\f[R] on every usage.
.PP
The default value when the option is not specified is \f[V]warn\f[R].
.RE
.SH EXTRA OPTIONS FOR MACOS
.PP
The following extra options are macOS specific.
.TP
\f[V]-XstartOnFirstThread\f[R]
Runs the \f[V]main()\f[R] method on the first (AppKit) thread.
.TP
\f[V]-Xdock:name=\f[R]\f[I]application_name\f[R]
Overrides the default application name displayed in dock.
.TP
\f[V]-Xdock:icon=\f[R]\f[I]path_to_icon_file\f[R]
Overrides the default icon displayed in dock.
.SH ADVANCED OPTIONS FOR JAVA
.PP
These \f[V]java\f[R] options can be used to enable other advanced
options.
.TP
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R]
Unlocks the options intended for diagnosing the JVM.
By default, this option is disabled and diagnostic options aren\[aq]t
available.
.RS
.PP
Command line options that are enabled with the use of this option are
not supported.
If you encounter issues while using any of these options, it is very
likely that you will be required to reproduce the problem without using
any of these unsupported options before Oracle Support can assist with
an investigation.
It is also possible that any of these options may be removed or their
behavior changed without any warning.
.RE
.TP
\f[V]-XX:+UnlockExperimentalVMOptions\f[R]
Unlocks the options that provide experimental features in the JVM.
By default, this option is disabled and experimental features aren\[aq]t
available.
.SH ADVANCED RUNTIME OPTIONS FOR JAVA
.PP
These \f[V]java\f[R] options control the runtime behavior of the Java
HotSpot VM.
.TP
\f[V]-XX:ActiveProcessorCount=\f[R]\f[I]x\f[R]
Overrides the number of CPUs that the VM will use to calculate the size
of thread pools it will use for various operations such as Garbage
Collection and ForkJoinPool.
.RS
.PP
The VM normally determines the number of available processors from the
operating system.
This flag can be useful for partitioning CPU resources when running
multiple Java processes in docker containers.
This flag is honored even if \f[V]UseContainerSupport\f[R] is not
enabled.
See \f[V]-XX:-UseContainerSupport\f[R] for a description of enabling and
disabling container support.
.RE
.TP
\f[V]-XX:AllocateHeapAt=\f[R]\f[I]path\f[R]
Takes a path to the file system and uses memory mapping to allocate the
object heap on the memory device.
Using this option enables the HotSpot VM to allocate the Java object
heap on an alternative memory device, such as an NV-DIMM, specified by
the user.
.RS
.PP
Alternative memory devices that have the same semantics as DRAM,
including the semantics of atomic operations, can be used instead of
DRAM for the object heap without changing the existing application code.
All other memory structures (such as the code heap, metaspace, and
thread stacks) continue to reside in DRAM.
.PP
Some operating systems expose non-DRAM memory through the file system.
Memory-mapped files in these file systems bypass the page cache and
provide a direct mapping of virtual memory to the physical memory on the
device.
The existing heap related flags (such as \f[V]-Xmx\f[R] and
\f[V]-Xms\f[R]) and garbage-collection related flags continue to work as
before.
.RE
.TP
\f[V]-XX:-CompactStrings\f[R]
Disables the Compact Strings feature.
By default, this option is enabled.
When this option is enabled, Java Strings containing only single-byte
characters are internally represented and stored as
single-byte-per-character Strings using ISO-8859-1 / Latin-1 encoding.
This reduces, by 50%, the amount of space required for Strings
containing only single-byte characters.
For Java Strings containing at least one multibyte character: these are
represented and stored as 2 bytes per character using UTF-16 encoding.
Disabling the Compact Strings feature forces the use of UTF-16 encoding
as the internal representation for all Java Strings.
.RS
.PP
Cases where it may be beneficial to disable Compact Strings include the
following:
.IP \[bu] 2
When it\[aq]s known that an application overwhelmingly will be
allocating multibyte character Strings
.IP \[bu] 2
In the unexpected event where a performance regression is observed in
migrating from Java SE 8 to Java SE 9 and an analysis shows that Compact
Strings introduces the regression
.PP
In both of these scenarios, disabling Compact Strings makes sense.
.RE
.TP
\f[V]-XX:ErrorFile=\f[R]\f[I]filename\f[R]
Specifies the path and file name to which error data is written when an
irrecoverable error occurs.
By default, this file is created in the current working directory and
named \f[V]hs_err_pid\f[R]\f[I]pid\f[R]\f[V].log\f[R] where
\f[I]pid\f[R] is the identifier of the process that encountered the
error.
.RS
.PP
The following example shows how to set the default log file (note that
the identifier of the process is specified as \f[V]%p\f[R]):
.RS
.PP
\f[V]-XX:ErrorFile=./hs_err_pid%p.log\f[R]
.RE
.IP \[bu] 2
\f[B]Non-Windows:\f[R] The following example shows how to set the error
log to \f[V]/var/log/java/java_error.log\f[R]:
.RS 2
.RS
.PP
\f[V]-XX:ErrorFile=/var/log/java/java_error.log\f[R]
.RE
.RE
.IP \[bu] 2
\f[B]Windows:\f[R] The following example shows how to set the error log
file to \f[V]C:/log/java/java_error.log\f[R]:
.RS 2
.RS
.PP
\f[V]-XX:ErrorFile=C:/log/java/java_error.log\f[R]
.RE
.RE
.PP
If the file exists, and is writeable, then it will be overwritten.
Otherwise, if the file can\[aq]t be created in the specified directory
(due to insufficient space, permission problem, or another issue), then
the file is created in the temporary directory for the operating system:
.IP \[bu] 2
\f[B]Non-Windows:\f[R] The temporary directory is \f[V]/tmp\f[R].
.IP \[bu] 2
\f[B]Windows:\f[R] The temporary directory is specified by the value of
the \f[V]TMP\f[R] environment variable; if that environment variable
isn\[aq]t defined, then the value of the \f[V]TEMP\f[R] environment
variable is used.
.RE
.TP
\f[V]-XX:+ExtensiveErrorReports\f[R]
Enables the reporting of more extensive error information in the
\f[V]ErrorFile\f[R].
This option can be turned on in environments where maximal information
is desired - even if the resulting logs may be quite large and/or
contain information that might be considered sensitive.
The information can vary from release to release, and across different
platforms.
By default this option is disabled.
.TP
\f[V]-XX:FlightRecorderOptions=\f[R]\f[I]parameter\f[R]\f[V]=\f[R]\f[I]value\f[R] (or) \f[V]-XX:FlightRecorderOptions:\f[R]\f[I]parameter\f[R]\f[V]=\f[R]\f[I]value\f[R]
Sets the parameters that control the behavior of JFR.
Multiple parameters can be specified by separating them with a comma.
.RS
.PP
The following list contains the available JFR
\f[I]parameter\f[R]\f[V]=\f[R]\f[I]value\f[R] entries:
.TP
\f[V]globalbuffersize=\f[R]\f[I]size\f[R]
Specifies the total amount of primary memory used for data retention.
The default value is based on the value specified for
\f[V]memorysize\f[R].
Change the \f[V]memorysize\f[R] parameter to alter the size of global
buffers.
.TP
\f[V]maxchunksize=\f[R]\f[I]size\f[R]
Specifies the maximum size (in bytes) of the data chunks in a recording.
Append \f[V]m\f[R] or \f[V]M\f[R] to specify the size in megabytes (MB),
or \f[V]g\f[R] or \f[V]G\f[R] to specify the size in gigabytes (GB).
By default, the maximum size of data chunks is set to 12 MB.
The minimum allowed is 1 MB.
.TP
\f[V]memorysize=\f[R]\f[I]size\f[R]
Determines how much buffer memory should be used, and sets the
\f[V]globalbuffersize\f[R] and \f[V]numglobalbuffers\f[R] parameters
based on the size specified.
Append \f[V]m\f[R] or \f[V]M\f[R] to specify the size in megabytes (MB),
or \f[V]g\f[R] or \f[V]G\f[R] to specify the size in gigabytes (GB).
By default, the memory size is set to 10 MB.
.TP
\f[V]numglobalbuffers\f[R]
Specifies the number of global buffers used.
The default value is based on the memory size specified.
Change the \f[V]memorysize\f[R] parameter to alter the number of global
buffers.
.TP
\f[V]old-object-queue-size=number-of-objects\f[R]
Maximum number of old objects to track.
By default, the number of objects is set to 256.
.TP
\f[V]preserve-repository=\f[R]{\f[V]true\f[R]|\f[V]false\f[R]}
Specifies whether files stored in the disk repository should be kept
after the JVM has exited.
If false, files are deleted.
By default, this parameter is disabled.
.TP
\f[V]repository=\f[R]\f[I]path\f[R]
Specifies the repository (a directory) for temporary disk storage.
By default, the system\[aq]s temporary directory is used.
.TP
\f[V]retransform=\f[R]{\f[V]true\f[R]|\f[V]false\f[R]}
Specifies whether event classes should be retransformed using JVMTI.
If false, instrumentation is added when event classes are loaded.
By default, this parameter is enabled.
.TP
\f[V]stackdepth=\f[R]\f[I]depth\f[R]
Stack depth for stack traces.
By default, the depth is set to 64 method calls.
The maximum is 2048.
Values greater than 64 could create significant overhead and reduce
performance.
.TP
\f[V]threadbuffersize=\f[R]\f[I]size\f[R]
Specifies the per-thread local buffer size (in bytes).
By default, the local buffer size is set to 8 kilobytes, with a minimum
value of 4 kilobytes.
Overriding this parameter could reduce performance and is not
recommended.
.RE
.TP
\f[V]-XX:LargePageSizeInBytes=\f[R]\f[I]size\f[R]
Sets the maximum large page size (in bytes) used by the JVM.
The \f[I]size\f[R] argument must be a valid page size supported by the
environment to have any effect.
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
By default, the size is set to 0, meaning that the JVM will use the
default large page size for the environment as the maximum size for
large pages.
See \f[B]Large Pages\f[R].
.RS
.PP
The following example describes how to set the large page size to 1
gigabyte (GB):
.RS
.PP
\f[V]-XX:LargePageSizeInBytes=1g\f[R]
.RE
.RE
.TP
\f[V]-XX:MaxDirectMemorySize=\f[R]\f[I]size\f[R]
Sets the maximum total size (in bytes) of the \f[V]java.nio\f[R]
package, direct-buffer allocations.
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
If not set, the flag is ignored and the JVM chooses the size for NIO
direct-buffer allocations automatically.
.RS
.PP
The following examples illustrate how to set the NIO size to 1024 KB in
different units:
.IP
.nf
\f[CB]
-XX:MaxDirectMemorySize=1m
-XX:MaxDirectMemorySize=1024k
-XX:MaxDirectMemorySize=1048576
\f[R]
.fi
.RE
.TP
\f[V]-XX:-MaxFDLimit\f[R]
Disables the attempt to set the soft limit for the number of open file
descriptors to the hard limit.
By default, this option is enabled on all platforms, but is ignored on
Windows.
The only time that you may need to disable this is on macOS, where its
use imposes a maximum of 10240, which is lower than the actual system
maximum.
.TP
\f[V]-XX:NativeMemoryTracking=\f[R]\f[I]mode\f[R]
Specifies the mode for tracking JVM native memory usage.
Possible \f[I]mode\f[R] arguments for this option include the following:
.RS
.TP
\f[V]off\f[R]
Instructs not to track JVM native memory usage.
This is the default behavior if you don\[aq]t specify the
\f[V]-XX:NativeMemoryTracking\f[R] option.
.TP
\f[V]summary\f[R]
Tracks memory usage only by JVM subsystems, such as Java heap, class,
code, and thread.
.TP
\f[V]detail\f[R]
In addition to tracking memory usage by JVM subsystems, track memory
usage by individual \f[V]CallSite\f[R], individual virtual memory region
and its committed regions.
.RE
.TP
\f[V]-XX:TrimNativeHeapInterval=\f[R]\f[I]millis\f[R]
Interval, in ms, at which the JVM will trim the native heap.
Lower values will reclaim memory more eagerly at the cost of higher
overhead.
A value of 0 (default) disables native heap trimming.
Native heap trimming is performed in a dedicated thread.
.RS
.PP
This option is only supported on Linux with GNU C Library (glibc).
.RE
.TP
\f[V]-XX:+NeverActAsServerClassMachine\f[R]
Enable the \[dq]Client VM emulation\[dq] mode which only uses the C1 JIT
compiler, a 32Mb CodeCache and the Serial GC.
The maximum amount of memory that the JVM may use (controlled by the
\f[V]-XX:MaxRAM=n\f[R] flag) is set to 1GB by default.
The string \[dq]emulated-client\[dq] is added to the JVM version string.
.RS
.PP
By default the flag is set to \f[V]true\f[R] only on Windows in 32-bit
mode and \f[V]false\f[R] in all other cases.
.PP
The \[dq]Client VM emulation\[dq] mode will not be enabled if any of the
following flags are used on the command line:
.IP
.nf
\f[CB]
-XX:{+|-}TieredCompilation
-XX:CompilationMode=mode
-XX:TieredStopAtLevel=n
-XX:{+|-}EnableJVMCI
-XX:{+|-}UseJVMCICompiler
\f[R]
.fi
.RE
.TP
\f[V]-XX:ObjectAlignmentInBytes=\f[R]\f[I]alignment\f[R]
Sets the memory alignment of Java objects (in bytes).
By default, the value is set to 8 bytes.
The specified value should be a power of 2, and must be within the range
of 8 and 256 (inclusive).
This option makes it possible to use compressed pointers with large Java
heap sizes.
.RS
.PP
The heap size limit in bytes is calculated as:
.RS
.PP
\f[V]4GB * ObjectAlignmentInBytes\f[R]
.RE
.RS
.PP
\f[B]Note:\f[R] As the alignment value increases, the unused space
between objects also increases.
As a result, you may not realize any benefits from using compressed
pointers with large Java heap sizes.
.RE
.RE
.TP
\f[V]-XX:OnError=\f[R]\f[I]string\f[R]
Sets a custom command or a series of semicolon-separated commands to run
when an irrecoverable error occurs.
If the string contains spaces, then it must be enclosed in quotation
marks.
.RS
.IP \[bu] 2
\f[B]Non-Windows:\f[R] The following example shows how the
\f[V]-XX:OnError\f[R] option can be used to run the \f[V]gcore\f[R]
command to create a core image, and start the \f[V]gdb\f[R] debugger to
attach to the process in case of an irrecoverable error (the
\f[V]%p\f[R] designates the current process identifier):
.RS 2
.RS
.PP
\f[V]-XX:OnError=\[dq]gcore %p;gdb -p %p\[dq]\f[R]
.RE
.RE
.IP \[bu] 2
\f[B]Windows:\f[R] The following example shows how the
\f[V]-XX:OnError\f[R] option can be used to run the
\f[V]userdump.exe\f[R] utility to obtain a crash dump in case of an
irrecoverable error (the \f[V]%p\f[R] designates the current process
identifier).
This example assumes that the path to the \f[V]userdump.exe\f[R] utility
is specified in the \f[V]PATH\f[R] environment variable:
.RS 2
.RS
.PP
\f[V]-XX:OnError=\[dq]userdump.exe %p\[dq]\f[R]
.RE
.RE
.RE
.TP
\f[V]-XX:OnOutOfMemoryError=\f[R]\f[I]string\f[R]
Sets a custom command or a series of semicolon-separated commands to run
when an \f[V]OutOfMemoryError\f[R] exception is first thrown.
If the string contains spaces, then it must be enclosed in quotation
marks.
For an example of a command string, see the description of the
\f[V]-XX:OnError\f[R] option.
.TP
\f[V]-XX:+PrintCommandLineFlags\f[R]
Enables printing of ergonomically selected JVM flags that appeared on
the command line.
It can be useful to know the ergonomic values set by the JVM, such as
the heap space size and the selected garbage collector.
By default, this option is disabled and flags aren\[aq]t printed.
.TP
\f[V]-XX:+PreserveFramePointer\f[R]
Selects between using the RBP register as a general purpose register
(\f[V]-XX:-PreserveFramePointer\f[R]) and using the RBP register to hold
the frame pointer of the currently executing method
(\f[V]-XX:+PreserveFramePointer\f[R] .
If the frame pointer is available, then external profiling tools (for
example, Linux perf) can construct more accurate stack traces.
.TP
\f[V]-XX:+PrintNMTStatistics\f[R]
Enables printing of collected native memory tracking data at JVM exit
when native memory tracking is enabled (see
\f[V]-XX:NativeMemoryTracking\f[R]).
By default, this option is disabled and native memory tracking data
isn\[aq]t printed.
.TP
\f[V]-XX:SharedArchiveFile=\f[R]\f[I]path\f[R]
Specifies the path and name of the class data sharing (CDS) archive file
.RS
.PP
See \f[B]Application Class Data Sharing\f[R].
.RE
.TP
\f[V]-XX:+VerifySharedSpaces\f[R]
If this option is specified, the JVM will load a CDS archive file only
if it passes an integrity check based on CRC32 checksums.
The purpose of this flag is to check for unintentional damage to CDS
archive files in transmission or storage.
To guarantee the security and proper operation of CDS, the user must
ensure that the CDS archive files used by Java applications cannot be
modified without proper authorization.
.TP
\f[V]-XX:SharedArchiveConfigFile=\f[R]\f[I]shared_config_file\f[R]
Specifies additional shared data added to the archive file.
.TP
\f[V]-XX:SharedClassListFile=\f[R]\f[I]file_name\f[R]
Specifies the text file that contains the names of the classes to store
in the class data sharing (CDS) archive.
This file contains the full name of one class per line, except slashes
(\f[V]/\f[R]) replace dots (\f[V].\f[R]).
For example, to specify the classes \f[V]java.lang.Object\f[R] and
\f[V]hello.Main\f[R], create a text file that contains the following two
lines:
.RS
.IP
.nf
\f[CB]
java/lang/Object
hello/Main
\f[R]
.fi
.PP
The classes that you specify in this text file should include the
classes that are commonly used by the application.
They may include any classes from the application, extension, or
bootstrap class paths.
.PP
See \f[B]Application Class Data Sharing\f[R].
.RE
.TP
\f[V]-XX:+ShowCodeDetailsInExceptionMessages\f[R]
Enables printing of improved \f[V]NullPointerException\f[R] messages.
When an application throws a \f[V]NullPointerException\f[R], the option
enables the JVM to analyze the program\[aq]s bytecode instructions to
determine precisely which reference is \f[V]null\f[R], and describes the
source with a null-detail message.
The null-detail message is calculated and returned by
\f[V]NullPointerException.getMessage()\f[R], and will be printed as the
exception message along with the method, filename, and line number.
By default, this option is enabled.
.TP
\f[V]-XX:+ShowMessageBoxOnError\f[R]
Enables the display of a dialog box when the JVM experiences an
irrecoverable error.
This prevents the JVM from exiting and keeps the process active so that
you can attach a debugger to it to investigate the cause of the error.
By default, this option is disabled.
.TP
\f[V]-XX:StartFlightRecording:\f[R]\f[I]parameter\f[R]\f[V]=\f[R]\f[I]value\f[R]
Starts a JFR recording for the Java application.
This option is equivalent to the \f[V]JFR.start\f[R] diagnostic command
that starts a recording during runtime.
\f[V]-XX:StartFlightRecording:help\f[R] prints available options and
example command lines.
You can set the following \f[I]parameter\f[R]\f[V]=\f[R]\f[I]value\f[R]
entries when starting a JFR recording:
.RS
.TP
\f[V]delay=\f[R]\f[I]time\f[R]
Specifies the delay between the Java application launch time and the
start of the recording.
Append \f[V]s\f[R] to specify the time in seconds, \f[V]m\f[R] for
minutes, \f[V]h\f[R] for hours, or \f[V]d\f[R] for days (for example,
specifying \f[V]10m\f[R] means 10 minutes).
By default, there\[aq]s no delay, and this parameter is set to 0.
.TP
\f[V]disk=\f[R]{\f[V]true\f[R]|\f[V]false\f[R]}
Specifies whether to write data to disk while recording.
By default, this parameter is enabled.
.TP
\f[V]dumponexit=\f[R]{\f[V]true\f[R]|\f[V]false\f[R]}
Specifies if the running recording is dumped when the JVM shuts down.
If enabled and a \f[V]filename\f[R] is not entered, the recording is
written to a file in the directory where the process was started.
The file name is a system-generated name that contains the process ID,
recording ID, and current timestamp, similar to
\f[V]hotspot-pid-47496-id-1-2018_01_25_19_10_41.jfr\f[R].
By default, this parameter is disabled.
.TP
\f[V]duration=\f[R]\f[I]time\f[R]
Specifies the duration of the recording.
Append \f[V]s\f[R] to specify the time in seconds, \f[V]m\f[R] for
minutes, \f[V]h\f[R] for hours, or \f[V]d\f[R] for days (for example,
specifying \f[V]5h\f[R] means 5 hours).
By default, the duration isn\[aq]t limited, and this parameter is set to
0.
.TP
\f[V]filename=\f[R]\f[I]path\f[R]
Specifies the path and name of the file to which the recording is
written when the recording is stopped, for example:
.RS
.IP \[bu] 2
\f[V]recording.jfr\f[R]
.IP \[bu] 2
\f[V]/home/<USER>/recordings/recording.jfr\f[R]
.IP \[bu] 2
\f[V]c:\[rs]recordings\[rs]recording.jfr\f[R]
.PP
If %p and/or %t is specified in the filename, it expands to the
JVM\[aq]s PID and the current timestamp, respectively.
The filename may also be a directory in which case, the filename is
generated from the PID and the current date in the specified directory.
.RE
.TP
\f[V]name=\f[R]\f[I]identifier\f[R]
Takes both the name and the identifier of a recording.
.TP
\f[V]maxage=\f[R]\f[I]time\f[R]
Specifies the maximum age of disk data to keep for the recording.
This parameter is valid only when the \f[V]disk\f[R] parameter is set to
\f[V]true\f[R].
Append \f[V]s\f[R] to specify the time in seconds, \f[V]m\f[R] for
minutes, \f[V]h\f[R] for hours, or \f[V]d\f[R] for days (for example,
specifying \f[V]30s\f[R] means 30 seconds).
By default, the maximum age isn\[aq]t limited, and this parameter is set
to \f[V]0s\f[R].
.TP
\f[V]maxsize=\f[R]\f[I]size\f[R]
Specifies the maximum size (in bytes) of disk data to keep for the
recording.
This parameter is valid only when the \f[V]disk\f[R] parameter is set to
\f[V]true\f[R].
The value must not be less than the value for the \f[V]maxchunksize\f[R]
parameter set with \f[V]-XX:FlightRecorderOptions\f[R].
Append \f[V]m\f[R] or \f[V]M\f[R] to specify the size in megabytes, or
\f[V]g\f[R] or \f[V]G\f[R] to specify the size in gigabytes.
By default, the maximum size of disk data isn\[aq]t limited, and this
parameter is set to \f[V]0\f[R].
.TP
\f[V]path-to-gc-roots=\f[R]{\f[V]true\f[R]|\f[V]false\f[R]}
Specifies whether to collect the path to garbage collection (GC) roots
at the end of a recording.
By default, this parameter is disabled.
.RS
.PP
The path to GC roots is useful for finding memory leaks, but collecting
it is time-consuming.
Enable this option only when you start a recording for an application
that you suspect has a memory leak.
If the \f[V]settings\f[R] parameter is set to \f[V]profile\f[R], the
stack trace from where the potential leaking object was allocated is
included in the information collected.
.RE
.TP
\f[V]settings=\f[R]\f[I]path\f[R]
Specifies the path and name of the event settings file (of type JFC).
By default, the \f[V]default.jfc\f[R] file is used, which is located in
\f[V]JAVA_HOME/lib/jfr\f[R].
This default settings file collects a predefined set of information with
low overhead, so it has minimal impact on performance and can be used
with recordings that run continuously.
.RS
.PP
A second settings file is also provided, profile.jfc, which provides
more data than the default configuration, but can have more overhead and
impact performance.
Use this configuration for short periods of time when more information
is needed.
.RE
.PP
You can specify values for multiple parameters by separating them with a
comma.
Event settings and .jfc options can be specified using the following
syntax:
.TP
\f[V]option=\f[R]\f[I]value\f[R]
Specifies the option value to modify.
To list available options, use the \f[V]JAVA_HOME\f[R]/bin/jfr tool.
.TP
\f[V]event-setting=\f[R]\f[I]value\f[R]
Specifies the event setting value to modify.
Use the form: \f[V]<event-name>#<setting-name>=<value>\f[R].
To add a new event setting, prefix the event name with \[aq]+\[aq].
.PP
You can specify values for multiple event settings and .jfc options by
separating them with a comma.
In case of a conflict between a parameter and a .jfc option, the
parameter will take precedence.
The whitespace delimiter can be omitted for timespan values, i.e.
20ms.
For more information about the settings syntax, see Javadoc of the
jdk.jfr package.
.PP
To only see warnings and errors from JFR during startup set
-Xlog:jfr+startup=warning.
.RE
.TP
\f[V]-XX:ThreadStackSize=\f[R]\f[I]size\f[R]
Sets the Java thread stack size (in kilobytes).
Use of a scaling suffix, such as \f[V]k\f[R], results in the scaling of
the kilobytes value so that \f[V]-XX:ThreadStackSize=1k\f[R] sets the
Java thread stack size to 1024*1024 bytes or 1 megabyte.
The default value depends on the platform.
For example:
.RS
.IP \[bu] 2
Linux/x64: 1024 KB
.IP \[bu] 2
Linux/Aarch64: 2048 KB
.IP \[bu] 2
macOS/x64: 1024 KB
.IP \[bu] 2
macOS/Aarch64: 2048 KB
.IP \[bu] 2
Windows: The default value depends on virtual memory
.PP
The following examples show how to set the thread stack size to 1
megabyte in different units:
.IP
.nf
\f[CB]
-XX:ThreadStackSize=1k
-XX:ThreadStackSize=1024
\f[R]
.fi
.PP
This option is similar to \f[V]-Xss\f[R].
.RE
.TP
\f[V]-XX:-UseCompressedOops\f[R]
Disables the use of compressed pointers.
By default, this option is enabled, and compressed pointers are used.
This will automatically limit the maximum ergonomically determined Java
heap size to the maximum amount of memory that can be covered by
compressed pointers.
By default this range is 32 GB.
.RS
.PP
With compressed oops enabled, object references are represented as
32-bit offsets instead of 64-bit pointers, which typically increases
performance when running the application with Java heap sizes smaller
than the compressed oops pointer range.
This option works only for 64-bit JVMs.
.PP
It\[aq]s possible to use compressed pointers with Java heap sizes
greater than 32 GB.
See the \f[V]-XX:ObjectAlignmentInBytes\f[R] option.
.RE
.TP
\f[V]-XX:-UseContainerSupport\f[R]
\f[B]Linux only:\f[R] The VM now provides automatic container detection
support, which allows the VM to determine the amount of memory and
number of processors that are available to a Java process running in
docker containers.
It uses this information to allocate system resources.
The default for this flag is \f[V]true\f[R], and container support is
enabled by default.
It can be disabled with \f[V]-XX:-UseContainerSupport\f[R].
.RS
.PP
Unified Logging is available to help to diagnose issues related to this
support.
.PP
Use \f[V]-Xlog:os+container=trace\f[R] for maximum logging of container
information.
See \f[B]Enable Logging with the JVM Unified Logging Framework\f[R] for
a description of using Unified Logging.
.RE
.TP
\f[V]-XX:+UseLargePages\f[R]
Enables the use of large page memory.
By default, this option is disabled and large page memory isn\[aq]t
used.
.RS
.PP
See \f[B]Large Pages\f[R].
.RE
.TP
\f[V]-XX:+UseTransparentHugePages\f[R]
\f[B]Linux only:\f[R] Enables the use of large pages that can
dynamically grow or shrink.
This option is disabled by default.
You may encounter performance problems with transparent huge pages as
the OS moves other pages around to create huge pages; this option is
made available for experimentation.
.TP
\f[V]-XX:+AllowUserSignalHandlers\f[R]
\f[B]Non-Windows:\f[R] Enables installation of signal handlers by the
application.
By default, this option is disabled and the application isn\[aq]t
allowed to install signal handlers.
.TP
\f[V]-XX:VMOptionsFile=\f[R]\f[I]filename\f[R]
Allows user to specify VM options in a file, for example,
\f[V]java -XX:VMOptionsFile=/var/my_vm_options HelloWorld\f[R].
.TP
\f[V]-XX:UseBranchProtection=\f[R]\f[I]mode\f[R]
\f[B]Linux AArch64 only:\f[R] Specifies the branch protection mode.
All options other than \f[V]none\f[R] require the VM to have been built
with branch protection enabled.
In addition, for full protection, any native libraries provided by
applications should be compiled with the same level of protection.
.RS
.PP
Possible \f[I]mode\f[R] arguments for this option include the following:
.TP
\f[V]none\f[R]
Do not use branch protection.
This is the default value.
.TP
\f[V]standard\f[R]
Enables all branch protection modes available on the current platform.
.TP
\f[V]pac-ret\f[R]
Enables protection against ROP based attacks.
(AArch64 8.3+ only)
.RE
.SH ADVANCED JIT COMPILER OPTIONS FOR JAVA
.PP
These \f[V]java\f[R] options control the dynamic just-in-time (JIT)
compilation performed by the Java HotSpot VM.
.TP
\f[V]-XX:AllocateInstancePrefetchLines=\f[R]\f[I]lines\f[R]
Sets the number of lines to prefetch ahead of the instance allocation
pointer.
By default, the number of lines to prefetch is set to 1:
.RS
.RS
.PP
\f[V]-XX:AllocateInstancePrefetchLines=1\f[R]
.RE
.RE
.TP
\f[V]-XX:AllocatePrefetchDistance=\f[R]\f[I]size\f[R]
Sets the size (in bytes) of the prefetch distance for object allocation.
Memory about to be written with the value of new objects is prefetched
up to this distance starting from the address of the last allocated
object.
Each Java thread has its own allocation point.
.RS
.PP
Negative values denote that prefetch distance is chosen based on the
platform.
Positive values are bytes to prefetch.
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
The default value is set to -1.
.PP
The following example shows how to set the prefetch distance to 1024
bytes:
.RS
.PP
\f[V]-XX:AllocatePrefetchDistance=1024\f[R]
.RE
.RE
.TP
\f[V]-XX:AllocatePrefetchInstr=\f[R]\f[I]instruction\f[R]
Sets the prefetch instruction to prefetch ahead of the allocation
pointer.
Possible values are from 0 to 3.
The actual instructions behind the values depend on the platform.
By default, the prefetch instruction is set to 0:
.RS
.RS
.PP
\f[V]-XX:AllocatePrefetchInstr=0\f[R]
.RE
.RE
.TP
\f[V]-XX:AllocatePrefetchLines=\f[R]\f[I]lines\f[R]
Sets the number of cache lines to load after the last object allocation
by using the prefetch instructions generated in compiled code.
The default value is 1 if the last allocated object was an instance, and
3 if it was an array.
.RS
.PP
The following example shows how to set the number of loaded cache lines
to 5:
.RS
.PP
\f[V]-XX:AllocatePrefetchLines=5\f[R]
.RE
.RE
.TP
\f[V]-XX:AllocatePrefetchStepSize=\f[R]\f[I]size\f[R]
Sets the step size (in bytes) for sequential prefetch instructions.
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
By default, the step size is set to 16 bytes:
.RS
.RS
.PP
\f[V]-XX:AllocatePrefetchStepSize=16\f[R]
.RE
.RE
.TP
\f[V]-XX:AllocatePrefetchStyle=\f[R]\f[I]style\f[R]
Sets the generated code style for prefetch instructions.
The \f[I]style\f[R] argument is an integer from 0 to 3:
.RS
.TP
\f[V]0\f[R]
Don\[aq]t generate prefetch instructions.
.TP
\f[V]1\f[R]
Execute prefetch instructions after each allocation.
This is the default setting.
.TP
\f[V]2\f[R]
Use the thread-local allocation block (TLAB) watermark pointer to
determine when prefetch instructions are executed.
.TP
\f[V]3\f[R]
Generate one prefetch instruction per cache line.
.RE
.TP
\f[V]-XX:+BackgroundCompilation\f[R]
Enables background compilation.
This option is enabled by default.
To disable background compilation, specify
\f[V]-XX:-BackgroundCompilation\f[R] (this is equivalent to specifying
\f[V]-Xbatch\f[R]).
.TP
\f[V]-XX:CICompilerCount=\f[R]\f[I]threads\f[R]
Sets the number of compiler threads to use for compilation.
By default, the number of compiler threads is selected automatically
depending on the number of CPUs and memory available for compiled code.
The following example shows how to set the number of threads to 2:
.RS
.RS
.PP
\f[V]-XX:CICompilerCount=2\f[R]
.RE
.RE
.TP
\f[V]-XX:+UseDynamicNumberOfCompilerThreads\f[R]
Dynamically create compiler thread up to the limit specified by
\f[V]-XX:CICompilerCount\f[R].
This option is enabled by default.
.TP
\f[V]-XX:CompileCommand=\f[R]\f[I]command\f[R]\f[V],\f[R]\f[I]method\f[R][\f[V],\f[R]\f[I]option\f[R]]
Specifies a \f[I]command\f[R] to perform on a \f[I]method\f[R].
For example, to exclude the \f[V]indexOf()\f[R] method of the
\f[V]String\f[R] class from being compiled, use the following:
.RS
.RS
.PP
\f[V]-XX:CompileCommand=exclude,java/lang/String.indexOf\f[R]
.RE
.PP
Note that the full class name is specified, including all packages and
subpackages separated by a slash (\f[V]/\f[R]).
For easier cut-and-paste operations, it\[aq]s also possible to use the
method name format produced by the \f[V]-XX:+PrintCompilation\f[R] and
\f[V]-XX:+LogCompilation\f[R] options:
.RS
.PP
\f[V]-XX:CompileCommand=exclude,java.lang.String::indexOf\f[R]
.RE
.PP
If the method is specified without the signature, then the command is
applied to all methods with the specified name.
However, you can also specify the signature of the method in the class
file format.
In this case, you should enclose the arguments in quotation marks,
because otherwise the shell treats the semicolon as a command end.
For example, if you want to exclude only the \f[V]indexOf(String)\f[R]
method of the \f[V]String\f[R] class from being compiled, use the
following:
.RS
.PP
\f[V]-XX:CompileCommand=\[dq]exclude,java/lang/String.indexOf,(Ljava/lang/String;)I\[dq]\f[R]
.RE
.PP
You can also use the asterisk (*) as a wildcard for class and method
names.
For example, to exclude all \f[V]indexOf()\f[R] methods in all classes
from being compiled, use the following:
.RS
.PP
\f[V]-XX:CompileCommand=exclude,*.indexOf\f[R]
.RE
.PP
The commas and periods are aliases for spaces, making it easier to pass
compiler commands through a shell.
You can pass arguments to \f[V]-XX:CompileCommand\f[R] using spaces as
separators by enclosing the argument in quotation marks:
.RS
.PP
\f[V]-XX:CompileCommand=\[dq]exclude java/lang/String indexOf\[dq]\f[R]
.RE
.PP
Note that after parsing the commands passed on the command line using
the \f[V]-XX:CompileCommand\f[R] options, the JIT compiler then reads
commands from the \f[V].hotspot_compiler\f[R] file.
You can add commands to this file or specify a different file using the
\f[V]-XX:CompileCommandFile\f[R] option.
.PP
To add several commands, either specify the \f[V]-XX:CompileCommand\f[R]
option multiple times, or separate each argument with the new line
separator (\f[V]\[rs]n\f[R]).
The following commands are available:
.TP
\f[V]break\f[R]
Sets a breakpoint when debugging the JVM to stop at the beginning of
compilation of the specified method.
.TP
\f[V]compileonly\f[R]
Excludes all methods from compilation except for the specified method.
As an alternative, you can use the \f[V]-XX:CompileOnly\f[R] option,
which lets you specify several methods.
.TP
\f[V]dontinline\f[R]
Prevents inlining of the specified method.
.TP
\f[V]exclude\f[R]
Excludes the specified method from compilation.
.TP
\f[V]help\f[R]
Prints a help message for the \f[V]-XX:CompileCommand\f[R] option.
.TP
\f[V]inline\f[R]
Attempts to inline the specified method.
.TP
\f[V]log\f[R]
Excludes compilation logging (with the \f[V]-XX:+LogCompilation\f[R]
option) for all methods except for the specified method.
By default, logging is performed for all compiled methods.
.TP
\f[V]option\f[R]
Passes a JIT compilation option to the specified method in place of the
last argument (\f[V]option\f[R]).
The compilation option is set at the end, after the method name.
For example, to enable the \f[V]BlockLayoutByFrequency\f[R] option for
the \f[V]append()\f[R] method of the \f[V]StringBuffer\f[R] class, use
the following:
.RS
.RS
.PP
\f[V]-XX:CompileCommand=option,java/lang/StringBuffer.append,BlockLayoutByFrequency\f[R]
.RE
.PP
You can specify multiple compilation options, separated by commas or
spaces.
.RE
.TP
\f[V]print\f[R]
Prints generated assembler code after compilation of the specified
method.
.TP
\f[V]quiet\f[R]
Instructs not to print the compile commands.
By default, the commands that you specify with the
\f[V]-XX:CompileCommand\f[R] option are printed; for example, if you
exclude from compilation the \f[V]indexOf()\f[R] method of the
\f[V]String\f[R] class, then the following is printed to standard
output:
.RS
.RS
.PP
\f[V]CompilerOracle: exclude java/lang/String.indexOf\f[R]
.RE
.PP
You can suppress this by specifying the
\f[V]-XX:CompileCommand=quiet\f[R] option before other
\f[V]-XX:CompileCommand\f[R] options.
.RE
.RE
.TP
\f[V]-XX:CompileCommandFile=\f[R]\f[I]filename\f[R]
Sets the file from which JIT compiler commands are read.
By default, the \f[V].hotspot_compiler\f[R] file is used to store
commands performed by the JIT compiler.
.RS
.PP
Each line in the command file represents a command, a class name, and a
method name for which the command is used.
For example, this line prints assembly code for the \f[V]toString()\f[R]
method of the \f[V]String\f[R] class:
.RS
.PP
\f[V]print java/lang/String toString\f[R]
.RE
.PP
If you\[aq]re using commands for the JIT compiler to perform on methods,
then see the \f[V]-XX:CompileCommand\f[R] option.
.RE
.TP
\f[V]-XX:CompilerDirectivesFile=\f[R]\f[I]file\f[R]
Adds directives from a file to the directives stack when a program
starts.
See \f[B]Compiler Control\f[R]
[https://docs.oracle.com/en/java/javase/12/vm/compiler-control1.html#GUID-94AD8194-786A-4F19-BFFF-278F8E237F3A].
.RS
.PP
The \f[V]-XX:CompilerDirectivesFile\f[R] option has to be used together
with the \f[V]-XX:UnlockDiagnosticVMOptions\f[R] option that unlocks
diagnostic JVM options.
.RE
.TP
\f[V]-XX:+CompilerDirectivesPrint\f[R]
Prints the directives stack when the program starts or when a new
directive is added.
.RS
.PP
The \f[V]-XX:+CompilerDirectivesPrint\f[R] option has to be used
together with the \f[V]-XX:UnlockDiagnosticVMOptions\f[R] option that
unlocks diagnostic JVM options.
.RE
.TP
\f[V]-XX:CompileOnly=\f[R]\f[I]methods\f[R]
Sets the list of methods (separated by commas) to which compilation
should be restricted.
Only the specified methods are compiled.
.RS
.PP
\f[V]-XX:CompileOnly=method1,method2,...,methodN\f[R] is an alias for:
.IP
.nf
\f[CB]
-XX:CompileCommand=compileonly,method1
-XX:CompileCommand=compileonly,method2
\&...
-XX:CompileCommand=compileonly,methodN
\f[R]
.fi
.RE
.TP
\f[V]-XX:CompileThresholdScaling=\f[R]\f[I]scale\f[R]
Provides unified control of first compilation.
This option controls when methods are first compiled for both the tiered
and the nontiered modes of operation.
The \f[V]CompileThresholdScaling\f[R] option has a floating point value
between 0 and +Inf and scales the thresholds corresponding to the
current mode of operation (both tiered and nontiered).
Setting \f[V]CompileThresholdScaling\f[R] to a value less than 1.0
results in earlier compilation while values greater than 1.0 delay
compilation.
Setting \f[V]CompileThresholdScaling\f[R] to 0 is equivalent to
disabling compilation.
.TP
\f[V]-XX:+DoEscapeAnalysis\f[R]
Enables the use of escape analysis.
This option is enabled by default.
To disable the use of escape analysis, specify
\f[V]-XX:-DoEscapeAnalysis\f[R].
.TP
\f[V]-XX:InitialCodeCacheSize=\f[R]\f[I]size\f[R]
Sets the initial code cache size (in bytes).
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
The default value depends on the platform.
The initial code cache size shouldn\[aq]t be less than the system\[aq]s
minimal memory page size.
The following example shows how to set the initial code cache size to 32
KB:
.RS
.RS
.PP
\f[V]-XX:InitialCodeCacheSize=32k\f[R]
.RE
.RE
.TP
\f[V]-XX:+Inline\f[R]
Enables method inlining.
This option is enabled by default to increase performance.
To disable method inlining, specify \f[V]-XX:-Inline\f[R].
.TP
\f[V]-XX:InlineSmallCode=\f[R]\f[I]size\f[R]
Sets the maximum code size (in bytes) for already compiled methods that
may be inlined.
This flag only applies to the C2 compiler.
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
The default value depends on the platform and on whether tiered
compilation is enabled.
In the following example it is set to 1000 bytes:
.RS
.RS
.PP
\f[V]-XX:InlineSmallCode=1000\f[R]
.RE
.RE
.TP
\f[V]-XX:+LogCompilation\f[R]
Enables logging of compilation activity to a file named
\f[V]hotspot.log\f[R] in the current working directory.
You can specify a different log file path and name using the
\f[V]-XX:LogFile\f[R] option.
.RS
.PP
By default, this option is disabled and compilation activity isn\[aq]t
logged.
The \f[V]-XX:+LogCompilation\f[R] option has to be used together with
the \f[V]-XX:UnlockDiagnosticVMOptions\f[R] option that unlocks
diagnostic JVM options.
.PP
You can enable verbose diagnostic output with a message printed to the
console every time a method is compiled by using the
\f[V]-XX:+PrintCompilation\f[R] option.
.RE
.TP
\f[V]-XX:FreqInlineSize=\f[R]\f[I]size\f[R]
Sets the maximum bytecode size (in bytes) of a hot method to be inlined.
This flag only applies to the C2 compiler.
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
The default value depends on the platform.
In the following example it is set to 325 bytes:
.RS
.RS
.PP
\f[V]-XX:FreqInlineSize=325\f[R]
.RE
.RE
.TP
\f[V]-XX:MaxInlineSize=\f[R]\f[I]size\f[R]
Sets the maximum bytecode size (in bytes) of a cold method to be
inlined.
This flag only applies to the C2 compiler.
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
By default, the maximum bytecode size is set to 35 bytes:
.RS
.RS
.PP
\f[V]-XX:MaxInlineSize=35\f[R]
.RE
.RE
.TP
\f[V]-XX:C1MaxInlineSize=\f[R]\f[I]size\f[R]
Sets the maximum bytecode size (in bytes) of a cold method to be
inlined.
This flag only applies to the C1 compiler.
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
By default, the maximum bytecode size is set to 35 bytes:
.RS
.RS
.PP
\f[V]-XX:MaxInlineSize=35\f[R]
.RE
.RE
.TP
\f[V]-XX:MaxTrivialSize=\f[R]\f[I]size\f[R]
Sets the maximum bytecode size (in bytes) of a trivial method to be
inlined.
This flag only applies to the C2 compiler.
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
By default, the maximum bytecode size of a trivial method is set to 6
bytes:
.RS
.RS
.PP
\f[V]-XX:MaxTrivialSize=6\f[R]
.RE
.RE
.TP
\f[V]-XX:C1MaxTrivialSize=\f[R]\f[I]size\f[R]
Sets the maximum bytecode size (in bytes) of a trivial method to be
inlined.
This flag only applies to the C1 compiler.
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
By default, the maximum bytecode size of a trivial method is set to 6
bytes:
.RS
.RS
.PP
\f[V]-XX:MaxTrivialSize=6\f[R]
.RE
.RE
.TP
\f[V]-XX:MaxNodeLimit=\f[R]\f[I]nodes\f[R]
Sets the maximum number of nodes to be used during single method
compilation.
By default the value depends on the features enabled.
In the following example the maximum number of nodes is set to 100,000:
.RS
.RS
.PP
\f[V]-XX:MaxNodeLimit=100000\f[R]
.RE
.RE
.TP
\f[V]-XX:NonNMethodCodeHeapSize=\f[R]\f[I]size\f[R]
Sets the size in bytes of the code segment containing nonmethod code.
.RS
.PP
A nonmethod code segment containing nonmethod code, such as compiler
buffers and the bytecode interpreter.
This code type stays in the code cache forever.
This flag is used only if \f[V]-XX:SegmentedCodeCache\f[R] is enabled.
.RE
.TP
\f[V]-XX:NonProfiledCodeHeapSize=\f[R]\f[I]size\f[R]
Sets the size in bytes of the code segment containing nonprofiled
methods.
This flag is used only if \f[V]-XX:SegmentedCodeCache\f[R] is enabled.
.TP
\f[V]-XX:+OptimizeStringConcat\f[R]
Enables the optimization of \f[V]String\f[R] concatenation operations.
This option is enabled by default.
To disable the optimization of \f[V]String\f[R] concatenation
operations, specify \f[V]-XX:-OptimizeStringConcat\f[R].
.TP
\f[V]-XX:+PrintAssembly\f[R]
Enables printing of assembly code for bytecoded and native methods by
using the external \f[V]hsdis-<arch>.so\f[R] or \f[V].dll\f[R] library.
For 64-bit VM on Windows, it\[aq]s \f[V]hsdis-amd64.dll\f[R].
This lets you to see the generated code, which may help you to diagnose
performance issues.
.RS
.PP
By default, this option is disabled and assembly code isn\[aq]t printed.
The \f[V]-XX:+PrintAssembly\f[R] option has to be used together with the
\f[V]-XX:UnlockDiagnosticVMOptions\f[R] option that unlocks diagnostic
JVM options.
.RE
.TP
\f[V]-XX:ProfiledCodeHeapSize=\f[R]\f[I]size\f[R]
Sets the size in bytes of the code segment containing profiled methods.
This flag is used only if \f[V]-XX:SegmentedCodeCache\f[R] is enabled.
.TP
\f[V]-XX:+PrintCompilation\f[R]
Enables verbose diagnostic output from the JVM by printing a message to
the console every time a method is compiled.
This lets you to see which methods actually get compiled.
By default, this option is disabled and diagnostic output isn\[aq]t
printed.
.RS
.PP
You can also log compilation activity to a file by using the
\f[V]-XX:+LogCompilation\f[R] option.
.RE
.TP
\f[V]-XX:+PrintInlining\f[R]
Enables printing of inlining decisions.
This let\[aq]s you see which methods are getting inlined.
.RS
.PP
By default, this option is disabled and inlining information isn\[aq]t
printed.
The \f[V]-XX:+PrintInlining\f[R] option has to be used together with the
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R] option that unlocks diagnostic
JVM options.
.RE
.TP
\f[V]-XX:ReservedCodeCacheSize=\f[R]\f[I]size\f[R]
Sets the maximum code cache size (in bytes) for JIT-compiled code.
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
The default maximum code cache size is 240 MB; if you disable tiered
compilation with the option \f[V]-XX:-TieredCompilation\f[R], then the
default size is 48 MB.
This option has a limit of 2 GB; otherwise, an error is generated.
The maximum code cache size shouldn\[aq]t be less than the initial code
cache size; see the option \f[V]-XX:InitialCodeCacheSize\f[R].
.TP
\f[V]-XX:+SegmentedCodeCache\f[R]
Enables segmentation of the code cache, without which the code cache
consists of one large segment.
With \f[V]-XX:+SegmentedCodeCache\f[R], separate segments will be used
for non-method, profiled method, and non-profiled method code.
The segments are not resized at runtime.
The advantages are better control of the memory footprint, reduced code
fragmentation, and better CPU iTLB (instruction translation lookaside
buffer) and instruction cache behavior due to improved locality.
.RS
.PP
The feature is enabled by default if tiered compilation is enabled
(\f[V]-XX:+TieredCompilation\f[R] ) and the reserved code cache size
(\f[V]-XX:ReservedCodeCacheSize\f[R]) is at least 240 MB.
.RE
.TP
\f[V]-XX:StartAggressiveSweepingAt=\f[R]\f[I]percent\f[R]
Forces stack scanning of active methods to aggressively remove unused
code when only the given percentage of the code cache is free.
The default value is 10%.
.TP
\f[V]-XX:-TieredCompilation\f[R]
Disables the use of tiered compilation.
By default, this option is enabled.
.TP
\f[V]-XX:UseSSE=\f[R]\f[I]version\f[R]
Enables the use of SSE instruction set of a specified version.
Is set by default to the highest supported version available (x86 only).
.TP
\f[V]-XX:UseAVX=\f[R]\f[I]version\f[R]
Enables the use of AVX instruction set of a specified version.
Is set by default to the highest supported version available (x86 only).
.TP
\f[V]-XX:+UseAES\f[R]
Enables hardware-based AES intrinsics for hardware that supports it.
This option is on by default on hardware that has the necessary
instructions.
The \f[V]-XX:+UseAES\f[R] is used in conjunction with
\f[V]UseAESIntrinsics\f[R].
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.TP
\f[V]-XX:+UseAESIntrinsics\f[R]
Enables AES intrinsics.
Specifying \f[V]-XX:+UseAESIntrinsics\f[R] is equivalent to also
enabling \f[V]-XX:+UseAES\f[R].
To disable hardware-based AES intrinsics, specify
\f[V]-XX:-UseAES -XX:-UseAESIntrinsics\f[R].
For example, to enable hardware AES, use the following flags:
.RS
.RS
.PP
\f[V]-XX:+UseAES -XX:+UseAESIntrinsics\f[R]
.RE
.PP
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.RE
.TP
\f[V]-XX:+UseAESCTRIntrinsics\f[R]
Analogous to \f[V]-XX:+UseAESIntrinsics\f[R] enables AES/CTR intrinsics.
.TP
\f[V]-XX:+UseGHASHIntrinsics\f[R]
Controls the use of GHASH intrinsics.
Enabled by default on platforms that support the corresponding
instructions.
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.TP
\f[V]-XX:+UseChaCha20Intrinsics\f[R]
Enable ChaCha20 intrinsics.
This option is on by default for supported platforms.
To disable ChaCha20 intrinsics, specify
\f[V]-XX:-UseChaCha20Intrinsics\f[R].
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.TP
\f[V]-XX:+UsePoly1305Intrinsics\f[R]
Enable Poly1305 intrinsics.
This option is on by default for supported platforms.
To disable Poly1305 intrinsics, specify
\f[V]-XX:-UsePoly1305Intrinsics\f[R].
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.TP
\f[V]-XX:+UseBASE64Intrinsics\f[R]
Controls the use of accelerated BASE64 encoding routines for
\f[V]java.util.Base64\f[R].
Enabled by default on platforms that support it.
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.TP
\f[V]-XX:+UseAdler32Intrinsics\f[R]
Controls the use of Adler32 checksum algorithm intrinsic for
\f[V]java.util.zip.Adler32\f[R].
Enabled by default on platforms that support it.
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.TP
\f[V]-XX:+UseCRC32Intrinsics\f[R]
Controls the use of CRC32 intrinsics for \f[V]java.util.zip.CRC32\f[R].
Enabled by default on platforms that support it.
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.TP
\f[V]-XX:+UseCRC32CIntrinsics\f[R]
Controls the use of CRC32C intrinsics for
\f[V]java.util.zip.CRC32C\f[R].
Enabled by default on platforms that support it.
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.TP
\f[V]-XX:+UseSHA\f[R]
Enables hardware-based intrinsics for SHA crypto hash functions for some
hardware.
The \f[V]UseSHA\f[R] option is used in conjunction with the
\f[V]UseSHA1Intrinsics\f[R], \f[V]UseSHA256Intrinsics\f[R], and
\f[V]UseSHA512Intrinsics\f[R] options.
.RS
.PP
The \f[V]UseSHA\f[R] and \f[V]UseSHA*Intrinsics\f[R] flags are enabled
by default on machines that support the corresponding instructions.
.PP
This feature is applicable only when using the
\f[V]sun.security.provider.Sun\f[R] provider for SHA operations.
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.PP
To disable all hardware-based SHA intrinsics, specify the
\f[V]-XX:-UseSHA\f[R].
To disable only a particular SHA intrinsic, use the appropriate
corresponding option.
For example: \f[V]-XX:-UseSHA256Intrinsics\f[R].
.RE
.TP
\f[V]-XX:+UseSHA1Intrinsics\f[R]
Enables intrinsics for SHA-1 crypto hash function.
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.TP
\f[V]-XX:+UseSHA256Intrinsics\f[R]
Enables intrinsics for SHA-224 and SHA-256 crypto hash functions.
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.TP
\f[V]-XX:+UseSHA512Intrinsics\f[R]
Enables intrinsics for SHA-384 and SHA-512 crypto hash functions.
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.TP
\f[V]-XX:+UseMathExactIntrinsics\f[R]
Enables intrinsification of various \f[V]java.lang.Math.*Exact()\f[R]
functions.
Enabled by default.
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.TP
\f[V]-XX:+UseMultiplyToLenIntrinsic\f[R]
Enables intrinsification of \f[V]BigInteger.multiplyToLen()\f[R].
Enabled by default on platforms that support it.
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.TP
-XX:+UseSquareToLenIntrinsic
Enables intrinsification of \f[V]BigInteger.squareToLen()\f[R].
Enabled by default on platforms that support it.
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.TP
-XX:+UseMulAddIntrinsic
Enables intrinsification of \f[V]BigInteger.mulAdd()\f[R].
Enabled by default on platforms that support it.
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.TP
-XX:+UseMontgomeryMultiplyIntrinsic
Enables intrinsification of \f[V]BigInteger.montgomeryMultiply()\f[R].
Enabled by default on platforms that support it.
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.TP
-XX:+UseMontgomerySquareIntrinsic
Enables intrinsification of \f[V]BigInteger.montgomerySquare()\f[R].
Enabled by default on platforms that support it.
Flags that control intrinsics now require the option
\f[V]-XX:+UnlockDiagnosticVMOptions\f[R].
.TP
\f[V]-XX:+UseCMoveUnconditionally\f[R]
Generates CMove (scalar and vector) instructions regardless of
profitability analysis.
.TP
\f[V]-XX:+UseCodeCacheFlushing\f[R]
Enables flushing of the code cache before shutting down the compiler.
This option is enabled by default.
To disable flushing of the code cache before shutting down the compiler,
specify \f[V]-XX:-UseCodeCacheFlushing\f[R].
.TP
\f[V]-XX:+UseCondCardMark\f[R]
Enables checking if the card is already marked before updating the card
table.
This option is disabled by default.
It should be used only on machines with multiple sockets, where it
increases the performance of Java applications that rely on concurrent
operations.
.TP
\f[V]-XX:+UseCountedLoopSafepoints\f[R]
Keeps safepoints in counted loops.
Its default value depends on whether the selected garbage collector
requires low latency safepoints.
.TP
\f[V]-XX:LoopStripMiningIter=\f[R]\f[I]number_of_iterations\f[R]
Controls the number of iterations in the inner strip mined loop.
Strip mining transforms counted loops into two level nested loops.
Safepoints are kept in the outer loop while the inner loop can execute
at full speed.
This option controls the maximum number of iterations in the inner loop.
The default value is 1,000.
.TP
\f[V]-XX:LoopStripMiningIterShortLoop=\f[R]\f[I]number_of_iterations\f[R]
Controls loop strip mining optimization.
Loops with the number of iterations less than specified will not have
safepoints in them.
Default value is 1/10th of \f[V]-XX:LoopStripMiningIter\f[R].
.TP
\f[V]-XX:+UseFMA\f[R]
Enables hardware-based FMA intrinsics for hardware where FMA
instructions are available (such as, Intel and ARM64).
FMA intrinsics are generated for the
\f[V]java.lang.Math.fma(\f[R]\f[I]a\f[R]\f[V],\f[R]
\f[I]b\f[R]\f[V],\f[R] \f[I]c\f[R]\f[V])\f[R] methods that calculate the
value of \f[V](\f[R] \f[I]a\f[R] \f[V]*\f[R] \f[I]b\f[R] \f[V]+\f[R]
\f[I]c\f[R] \f[V])\f[R] expressions.
.TP
\f[V]-XX:+UseSuperWord\f[R]
Enables the transformation of scalar operations into superword
operations.
Superword is a vectorization optimization.
This option is enabled by default.
To disable the transformation of scalar operations into superword
operations, specify \f[V]-XX:-UseSuperWord\f[R].
.SH ADVANCED SERVICEABILITY OPTIONS FOR JAVA
.PP
These \f[V]java\f[R] options provide the ability to gather system
information and perform extensive debugging.
.TP
\f[V]-XX:+DisableAttachMechanism\f[R]
Disables the mechanism that lets tools attach to the JVM.
By default, this option is disabled, meaning that the attach mechanism
is enabled and you can use diagnostics and troubleshooting tools such as
\f[V]jcmd\f[R], \f[V]jstack\f[R], \f[V]jmap\f[R], and \f[V]jinfo\f[R].
.RS
.RS
.PP
\f[B]Note:\f[R] The tools such as \f[B]jcmd\f[R], \f[B]jinfo\f[R],
\f[B]jmap\f[R], and \f[B]jstack\f[R] shipped with the JDK aren\[aq]t
supported when using the tools from one JDK version to troubleshoot a
different JDK version.
.RE
.RE
.TP
\f[V]-XX:+DTraceAllocProbes\f[R]
\f[B]Linux and macOS:\f[R] Enable \f[V]dtrace\f[R] tool probes for
object allocation.
.TP
\f[V]-XX:+DTraceMethodProbes\f[R]
\f[B]Linux and macOS:\f[R] Enable \f[V]dtrace\f[R] tool probes for
method-entry and method-exit.
.TP
\f[V]-XX:+DTraceMonitorProbes\f[R]
\f[B]Linux and macOS:\f[R] Enable \f[V]dtrace\f[R] tool probes for
monitor events.
.TP
\f[V]-XX:+HeapDumpOnOutOfMemoryError\f[R]
Enables the dumping of the Java heap to a file in the current directory
by using the heap profiler (HPROF) when a
\f[V]java.lang.OutOfMemoryError\f[R] exception is thrown.
You can explicitly set the heap dump file path and name using the
\f[V]-XX:HeapDumpPath\f[R] option.
By default, this option is disabled and the heap isn\[aq]t dumped when
an \f[V]OutOfMemoryError\f[R] exception is thrown.
.TP
\f[V]-XX:HeapDumpPath=\f[R]\f[I]path\f[R]
Sets the path and file name for writing the heap dump provided by the
heap profiler (HPROF) when the \f[V]-XX:+HeapDumpOnOutOfMemoryError\f[R]
option is set.
By default, the file is created in the current working directory, and
it\[aq]s named \f[V]java_pid<pid>.hprof\f[R] where \f[V]<pid>\f[R] is
the identifier of the process that caused the error.
The following example shows how to set the default file explicitly
(\f[V]%p\f[R] represents the current process identifier):
.RS
.RS
.PP
\f[V]-XX:HeapDumpPath=./java_pid%p.hprof\f[R]
.RE
.IP \[bu] 2
\f[B]Non-Windows:\f[R] The following example shows how to set the heap
dump file to \f[V]/var/log/java/java_heapdump.hprof\f[R]:
.RS 2
.RS
.PP
\f[V]-XX:HeapDumpPath=/var/log/java/java_heapdump.hprof\f[R]
.RE
.RE
.IP \[bu] 2
\f[B]Windows:\f[R] The following example shows how to set the heap dump
file to \f[V]C:/log/java/java_heapdump.log\f[R]:
.RS 2
.RS
.PP
\f[V]-XX:HeapDumpPath=C:/log/java/java_heapdump.log\f[R]
.RE
.RE
.RE
.TP
\f[V]-XX:LogFile=\f[R]\f[I]path\f[R]
Sets the path and file name to where log data is written.
By default, the file is created in the current working directory, and
it\[aq]s named \f[V]hotspot.log\f[R].
.RS
.IP \[bu] 2
\f[B]Non-Windows:\f[R] The following example shows how to set the log
file to \f[V]/var/log/java/hotspot.log\f[R]:
.RS 2
.RS
.PP
\f[V]-XX:LogFile=/var/log/java/hotspot.log\f[R]
.RE
.RE
.IP \[bu] 2
\f[B]Windows:\f[R] The following example shows how to set the log file
to \f[V]C:/log/java/hotspot.log\f[R]:
.RS 2
.RS
.PP
\f[V]-XX:LogFile=C:/log/java/hotspot.log\f[R]
.RE
.RE
.RE
.TP
\f[V]-XX:+PrintClassHistogram\f[R]
Enables printing of a class instance histogram after one of the
following events:
.RS
.IP \[bu] 2
\f[B]Non-Windows:\f[R] \f[V]Control+\[rs]\f[R] (\f[V]SIGQUIT\f[R])
.IP \[bu] 2
\f[B]Windows:\f[R] \f[V]Control+C\f[R] (\f[V]SIGTERM\f[R])
.PP
By default, this option is disabled.
.PP
Setting this option is equivalent to running the \f[V]jmap -histo\f[R]
command, or the \f[V]jcmd\f[R] \f[I]pid\f[R]
\f[V]GC.class_histogram\f[R] command, where \f[I]pid\f[R] is the current
Java process identifier.
.RE
.TP
\f[V]-XX:+PrintConcurrentLocks\f[R]
Enables printing of \f[V]java.util.concurrent\f[R] locks after one of
the following events:
.RS
.IP \[bu] 2
\f[B]Non-Windows:\f[R] \f[V]Control+\[rs]\f[R] (\f[V]SIGQUIT\f[R])
.IP \[bu] 2
\f[B]Windows:\f[R] \f[V]Control+C\f[R] (\f[V]SIGTERM\f[R])
.PP
By default, this option is disabled.
.PP
Setting this option is equivalent to running the \f[V]jstack -l\f[R]
command or the \f[V]jcmd\f[R] \f[I]pid\f[R] \f[V]Thread.print -l\f[R]
command, where \f[I]pid\f[R] is the current Java process identifier.
.RE
.TP
\f[V]-XX:+PrintFlagsRanges\f[R]
Prints the range specified and allows automatic testing of the values.
See \f[B]Validate Java Virtual Machine Flag Arguments\f[R].
.TP
\f[V]-XX:+PerfDataSaveToFile\f[R]
If enabled, saves \f[B]jstat\f[R] binary data when the Java application
exits.
This binary data is saved in a file named
\f[V]hsperfdata_\f[R]\f[I]pid\f[R], where \f[I]pid\f[R] is the process
identifier of the Java application that you ran.
Use the \f[V]jstat\f[R] command to display the performance data
contained in this file as follows:
.RS
.RS
.PP
\f[V]jstat -class file:///\f[R]\f[I]path\f[R]\f[V]/hsperfdata_\f[R]\f[I]pid\f[R]
.RE
.RS
.PP
\f[V]jstat -gc file:///\f[R]\f[I]path\f[R]\f[V]/hsperfdata_\f[R]\f[I]pid\f[R]
.RE
.RE
.TP
\f[V]-XX:+UsePerfData\f[R]
Enables the \f[V]perfdata\f[R] feature.
This option is enabled by default to allow JVM monitoring and
performance testing.
Disabling it suppresses the creation of the \f[V]hsperfdata_userid\f[R]
directories.
To disable the \f[V]perfdata\f[R] feature, specify
\f[V]-XX:-UsePerfData\f[R].
.SH ADVANCED GARBAGE COLLECTION OPTIONS FOR JAVA
.PP
These \f[V]java\f[R] options control how garbage collection (GC) is
performed by the Java HotSpot VM.
.TP
\f[V]-XX:+AggressiveHeap\f[R]
Enables Java heap optimization.
This sets various parameters to be optimal for long-running jobs with
intensive memory allocation, based on the configuration of the computer
(RAM and CPU).
By default, the option is disabled and the heap sizes are configured
less aggressively.
.TP
\f[V]-XX:+AlwaysPreTouch\f[R]
Requests the VM to touch every page on the Java heap after requesting it
from the operating system and before handing memory out to the
application.
By default, this option is disabled and all pages are committed as the
application uses the heap space.
.TP
\f[V]-XX:ConcGCThreads=\f[R]\f[I]threads\f[R]
Sets the number of threads used for concurrent GC.
Sets \f[I]\f[VI]threads\f[I]\f[R] to approximately 1/4 of the number of
parallel garbage collection threads.
The default value depends on the number of CPUs available to the JVM.
.RS
.PP
For example, to set the number of threads for concurrent GC to 2,
specify the following option:
.RS
.PP
\f[V]-XX:ConcGCThreads=2\f[R]
.RE
.RE
.TP
\f[V]-XX:+DisableExplicitGC\f[R]
Enables the option that disables processing of calls to the
\f[V]System.gc()\f[R] method.
This option is disabled by default, meaning that calls to
\f[V]System.gc()\f[R] are processed.
If processing of calls to \f[V]System.gc()\f[R] is disabled, then the
JVM still performs GC when necessary.
.TP
\f[V]-XX:+ExplicitGCInvokesConcurrent\f[R]
Enables invoking of concurrent GC by using the \f[V]System.gc()\f[R]
request.
This option is disabled by default and can be enabled only with the
\f[V]-XX:+UseG1GC\f[R] option.
.TP
\f[V]-XX:G1AdaptiveIHOPNumInitialSamples=\f[R]\f[I]number\f[R]
When \f[V]-XX:UseAdaptiveIHOP\f[R] is enabled, this option sets the
number of completed marking cycles used to gather samples until G1
adaptively determines the optimum value of
\f[V]-XX:InitiatingHeapOccupancyPercent\f[R].
Before, G1 uses the value of
\f[V]-XX:InitiatingHeapOccupancyPercent\f[R] directly for this purpose.
The default value is 3.
.TP
\f[V]-XX:G1HeapRegionSize=\f[R]\f[I]size\f[R]
Sets the size of the regions into which the Java heap is subdivided when
using the garbage-first (G1) collector.
The value is a power of 2 and can range from 1 MB to 32 MB.
The default region size is determined ergonomically based on the heap
size with a goal of approximately 2048 regions.
.RS
.PP
The following example sets the size of the subdivisions to 16 MB:
.RS
.PP
\f[V]-XX:G1HeapRegionSize=16m\f[R]
.RE
.RE
.TP
\f[V]-XX:G1HeapWastePercent=\f[R]\f[I]percent\f[R]
Sets the percentage of heap that you\[aq]re willing to waste.
The Java HotSpot VM doesn\[aq]t initiate the mixed garbage collection
cycle when the reclaimable percentage is less than the heap waste
percentage.
The default is 5 percent.
.TP
\f[V]-XX:G1MaxNewSizePercent=\f[R]\f[I]percent\f[R]
Sets the percentage of the heap size to use as the maximum for the young
generation size.
The default value is 60 percent of your Java heap.
.RS
.PP
This is an experimental flag.
This setting replaces the \f[V]-XX:DefaultMaxNewGenPercent\f[R] setting.
.RE
.TP
\f[V]-XX:G1MixedGCCountTarget=\f[R]\f[I]number\f[R]
Sets the target number of mixed garbage collections after a marking
cycle to collect old regions with at most
\f[V]G1MixedGCLIveThresholdPercent\f[R] live data.
The default is 8 mixed garbage collections.
The goal for mixed collections is to be within this target number.
.TP
\f[V]-XX:G1MixedGCLiveThresholdPercent=\f[R]\f[I]percent\f[R]
Sets the occupancy threshold for an old region to be included in a mixed
garbage collection cycle.
The default occupancy is 85 percent.
.RS
.PP
This is an experimental flag.
This setting replaces the
\f[V]-XX:G1OldCSetRegionLiveThresholdPercent\f[R] setting.
.RE
.TP
\f[V]-XX:G1NewSizePercent=\f[R]\f[I]percent\f[R]
Sets the percentage of the heap to use as the minimum for the young
generation size.
The default value is 5 percent of your Java heap.
.RS
.PP
This is an experimental flag.
This setting replaces the \f[V]-XX:DefaultMinNewGenPercent\f[R] setting.
.RE
.TP
\f[V]-XX:G1OldCSetRegionThresholdPercent=\f[R]\f[I]percent\f[R]
Sets an upper limit on the number of old regions to be collected during
a mixed garbage collection cycle.
The default is 10 percent of the Java heap.
.TP
\f[V]-XX:G1ReservePercent=\f[R]\f[I]percent\f[R]
Sets the percentage of the heap (0 to 50) that\[aq]s reserved as a false
ceiling to reduce the possibility of promotion failure for the G1
collector.
When you increase or decrease the percentage, ensure that you adjust the
total Java heap by the same amount.
By default, this option is set to 10%.
.RS
.PP
The following example sets the reserved heap to 20%:
.RS
.PP
\f[V]-XX:G1ReservePercent=20\f[R]
.RE
.RE
.TP
\f[V]-XX:+G1UseAdaptiveIHOP\f[R]
Controls adaptive calculation of the old generation occupancy to start
background work preparing for an old generation collection.
If enabled, G1 uses \f[V]-XX:InitiatingHeapOccupancyPercent\f[R] for the
first few times as specified by the value of
\f[V]-XX:G1AdaptiveIHOPNumInitialSamples\f[R], and after that adaptively
calculates a new optimum value for the initiating occupancy
automatically.
Otherwise, the old generation collection process always starts at the
old generation occupancy determined by
\f[V]-XX:InitiatingHeapOccupancyPercent\f[R].
.RS
.PP
The default is enabled.
.RE
.TP
\f[V]-XX:InitialHeapSize=\f[R]\f[I]size\f[R]
Sets the initial size (in bytes) of the memory allocation pool.
This value must be either 0, or a multiple of 1024 and greater than 1
MB.
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
The default value is selected at run time based on the system
configuration.
.RS
.PP
The following examples show how to set the size of allocated memory to 6
MB using various units:
.IP
.nf
\f[CB]
-XX:InitialHeapSize=6291456
-XX:InitialHeapSize=6144k
-XX:InitialHeapSize=6m
\f[R]
.fi
.PP
If you set this option to 0, then the initial size is set as the sum of
the sizes allocated for the old generation and the young generation.
The size of the heap for the young generation can be set using the
\f[V]-XX:NewSize\f[R] option.
Note that the \f[V]-Xms\f[R] option sets both the minimum and the
initial heap size of the heap.
If \f[V]-Xms\f[R] appears after \f[V]-XX:InitialHeapSize\f[R] on the
command line, then the initial heap size gets set to the value specified
with \f[V]-Xms\f[R].
.RE
.TP
\f[V]-XX:InitialRAMPercentage=\f[R]\f[I]percent\f[R]
Sets the initial amount of memory that the JVM will use for the Java
heap before applying ergonomics heuristics as a percentage of the
maximum amount determined as described in the \f[V]-XX:MaxRAM\f[R]
option.
The default value is 1.5625 percent.
.RS
.PP
The following example shows how to set the percentage of the initial
amount of memory used for the Java heap:
.RS
.PP
\f[V]-XX:InitialRAMPercentage=5\f[R]
.RE
.RE
.TP
\f[V]-XX:InitialSurvivorRatio=\f[R]\f[I]ratio\f[R]
Sets the initial survivor space ratio used by the throughput garbage
collector (which is enabled by the \f[V]-XX:+UseParallelGC\f[R] option).
Adaptive sizing is enabled by default with the throughput garbage
collector by using the \f[V]-XX:+UseParallelGC\f[R] option, and the
survivor space is resized according to the application behavior,
starting with the initial value.
If adaptive sizing is disabled (using the
\f[V]-XX:-UseAdaptiveSizePolicy\f[R] option), then the
\f[V]-XX:SurvivorRatio\f[R] option should be used to set the size of the
survivor space for the entire execution of the application.
.RS
.PP
The following formula can be used to calculate the initial size of
survivor space (S) based on the size of the young generation (Y), and
the initial survivor space ratio (R):
.RS
.PP
\f[V]S=Y/(R+2)\f[R]
.RE
.PP
The 2 in the equation denotes two survivor spaces.
The larger the value specified as the initial survivor space ratio, the
smaller the initial survivor space size.
.PP
By default, the initial survivor space ratio is set to 8.
If the default value for the young generation space size is used (2 MB),
then the initial size of the survivor space is 0.2 MB.
.PP
The following example shows how to set the initial survivor space ratio
to 4:
.RS
.PP
\f[V]-XX:InitialSurvivorRatio=4\f[R]
.RE
.RE
.TP
\f[V]-XX:InitiatingHeapOccupancyPercent=\f[R]\f[I]percent\f[R]
Sets the percentage of the old generation occupancy (0 to 100) at which
to start the first few concurrent marking cycles for the G1 garbage
collector.
.RS
.PP
By default, the initiating value is set to 45%.
A value of 0 implies nonstop concurrent GC cycles from the beginning
until G1 adaptively sets this value.
.PP
See also the \f[V]-XX:G1UseAdaptiveIHOP\f[R] and
\f[V]-XX:G1AdaptiveIHOPNumInitialSamples\f[R] options.
.PP
The following example shows how to set the initiating heap occupancy to
75%:
.RS
.PP
\f[V]-XX:InitiatingHeapOccupancyPercent=75\f[R]
.RE
.RE
.TP
\f[V]-XX:MaxGCPauseMillis=\f[R]\f[I]time\f[R]
Sets a target for the maximum GC pause time (in milliseconds).
This is a soft goal, and the JVM will make its best effort to achieve
it.
The specified value doesn\[aq]t adapt to your heap size.
By default, for G1 the maximum pause time target is 200 milliseconds.
The other generational collectors do not use a pause time goal by
default.
.RS
.PP
The following example shows how to set the maximum target pause time to
500 ms:
.RS
.PP
\f[V]-XX:MaxGCPauseMillis=500\f[R]
.RE
.RE
.TP
\f[V]-XX:MaxHeapSize=\f[R]\f[I]size\f[R]
Sets the maximum size (in byes) of the memory allocation pool.
This value must be a multiple of 1024 and greater than 2 MB.
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
The default value is selected at run time based on the system
configuration.
For server deployments, the options \f[V]-XX:InitialHeapSize\f[R] and
\f[V]-XX:MaxHeapSize\f[R] are often set to the same value.
.RS
.PP
The following examples show how to set the maximum allowed size of
allocated memory to 80 MB using various units:
.IP
.nf
\f[CB]
-XX:MaxHeapSize=83886080
-XX:MaxHeapSize=81920k
-XX:MaxHeapSize=80m
\f[R]
.fi
.PP
The \f[V]-XX:MaxHeapSize\f[R] option is equivalent to \f[V]-Xmx\f[R].
.RE
.TP
\f[V]-XX:MaxHeapFreeRatio=\f[R]\f[I]percent\f[R]
Sets the maximum allowed percentage of free heap space (0 to 100) after
a GC event.
If free heap space expands above this value, then the heap is shrunk.
By default, this value is set to 70%.
.RS
.PP
Minimize the Java heap size by lowering the values of the parameters
\f[V]MaxHeapFreeRatio\f[R] (default value is 70%) and
\f[V]MinHeapFreeRatio\f[R] (default value is 40%) with the command-line
options \f[V]-XX:MaxHeapFreeRatio\f[R] and
\f[V]-XX:MinHeapFreeRatio\f[R].
Lowering \f[V]MaxHeapFreeRatio\f[R] to as low as 10% and
\f[V]MinHeapFreeRatio\f[R] to 5% has successfully reduced the heap size
without too much performance regression; however, results may vary
greatly depending on your application.
Try different values for these parameters until they\[aq]re as low as
possible yet still retain acceptable performance.
.RS
.PP
\f[V]-XX:MaxHeapFreeRatio=10 -XX:MinHeapFreeRatio=5\f[R]
.RE
.PP
Customers trying to keep the heap small should also add the option
\f[V]-XX:-ShrinkHeapInSteps\f[R].
See \f[B]Performance Tuning Examples\f[R] for a description of using
this option to keep the Java heap small by reducing the dynamic
footprint for embedded applications.
.RE
.TP
\f[V]-XX:MaxMetaspaceSize=\f[R]\f[I]size\f[R]
Sets the maximum amount of native memory that can be allocated for class
metadata.
By default, the size isn\[aq]t limited.
The amount of metadata for an application depends on the application
itself, other running applications, and the amount of memory available
on the system.
.RS
.PP
The following example shows how to set the maximum class metadata size
to 256 MB:
.RS
.PP
\f[V]-XX:MaxMetaspaceSize=256m\f[R]
.RE
.RE
.TP
\f[V]-XX:MaxNewSize=\f[R]\f[I]size\f[R]
Sets the maximum size (in bytes) of the heap for the young generation
(nursery).
The default value is set ergonomically.
.TP
\f[V]-XX:MaxRAM=\f[R]\f[I]size\f[R]
Sets the maximum amount of memory that the JVM may use for the Java heap
before applying ergonomics heuristics.
The default value is the maximum amount of available memory to the JVM
process or 128 GB, whichever is lower.
.RS
.PP
The maximum amount of available memory to the JVM process is the minimum
of the machine\[aq]s physical memory and any constraints set by the
environment (e.g.
container).
.PP
Specifying this option disables automatic use of compressed oops if the
combined result of this and other options influencing the maximum amount
of memory is larger than the range of memory addressable by compressed
oops.
See \f[V]-XX:UseCompressedOops\f[R] for further information about
compressed oops.
.PP
The following example shows how to set the maximum amount of available
memory for sizing the Java heap to 2 GB:
.RS
.PP
\f[V]-XX:MaxRAM=2G\f[R]
.RE
.RE
.TP
\f[V]-XX:MaxRAMPercentage=\f[R]\f[I]percent\f[R]
Sets the maximum amount of memory that the JVM may use for the Java heap
before applying ergonomics heuristics as a percentage of the maximum
amount determined as described in the \f[V]-XX:MaxRAM\f[R] option.
The default value is 25 percent.
.RS
.PP
Specifying this option disables automatic use of compressed oops if the
combined result of this and other options influencing the maximum amount
of memory is larger than the range of memory addressable by compressed
oops.
See \f[V]-XX:UseCompressedOops\f[R] for further information about
compressed oops.
.PP
The following example shows how to set the percentage of the maximum
amount of memory used for the Java heap:
.RS
.PP
\f[V]-XX:MaxRAMPercentage=75\f[R]
.RE
.RE
.TP
\f[V]-XX:MinRAMPercentage=\f[R]\f[I]percent\f[R]
Sets the maximum amount of memory that the JVM may use for the Java heap
before applying ergonomics heuristics as a percentage of the maximum
amount determined as described in the \f[V]-XX:MaxRAM\f[R] option for
small heaps.
A small heap is a heap of approximately 125 MB.
The default value is 50 percent.
.RS
.PP
The following example shows how to set the percentage of the maximum
amount of memory used for the Java heap for small heaps:
.RS
.PP
\f[V]-XX:MinRAMPercentage=75\f[R]
.RE
.RE
.TP
\f[V]-XX:MaxTenuringThreshold=\f[R]\f[I]threshold\f[R]
Sets the maximum tenuring threshold for use in adaptive GC sizing.
The largest value is 15.
The default value is 15 for the parallel (throughput) collector.
.RS
.PP
The following example shows how to set the maximum tenuring threshold to
10:
.RS
.PP
\f[V]-XX:MaxTenuringThreshold=10\f[R]
.RE
.RE
.TP
\f[V]-XX:MetaspaceSize=\f[R]\f[I]size\f[R]
Sets the size of the allocated class metadata space that triggers a
garbage collection the first time it\[aq]s exceeded.
This threshold for a garbage collection is increased or decreased
depending on the amount of metadata used.
The default size depends on the platform.
.TP
\f[V]-XX:MinHeapFreeRatio=\f[R]\f[I]percent\f[R]
Sets the minimum allowed percentage of free heap space (0 to 100) after
a GC event.
If free heap space falls below this value, then the heap is expanded.
By default, this value is set to 40%.
.RS
.PP
Minimize Java heap size by lowering the values of the parameters
\f[V]MaxHeapFreeRatio\f[R] (default value is 70%) and
\f[V]MinHeapFreeRatio\f[R] (default value is 40%) with the command-line
options \f[V]-XX:MaxHeapFreeRatio\f[R] and
\f[V]-XX:MinHeapFreeRatio\f[R].
Lowering \f[V]MaxHeapFreeRatio\f[R] to as low as 10% and
\f[V]MinHeapFreeRatio\f[R] to 5% has successfully reduced the heap size
without too much performance regression; however, results may vary
greatly depending on your application.
Try different values for these parameters until they\[aq]re as low as
possible, yet still retain acceptable performance.
.RS
.PP
\f[V]-XX:MaxHeapFreeRatio=10 -XX:MinHeapFreeRatio=5\f[R]
.RE
.PP
Customers trying to keep the heap small should also add the option
\f[V]-XX:-ShrinkHeapInSteps\f[R].
See \f[B]Performance Tuning Examples\f[R] for a description of using
this option to keep the Java heap small by reducing the dynamic
footprint for embedded applications.
.RE
.TP
\f[V]-XX:MinHeapSize=\f[R]\f[I]size\f[R]
Sets the minimum size (in bytes) of the memory allocation pool.
This value must be either 0, or a multiple of 1024 and greater than 1
MB.
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
The default value is selected at run time based on the system
configuration.
.RS
.PP
The following examples show how to set the minimum size of allocated
memory to 6 MB using various units:
.IP
.nf
\f[CB]
-XX:MinHeapSize=6291456
-XX:MinHeapSize=6144k
-XX:MinHeapSize=6m
\f[R]
.fi
.PP
If you set this option to 0, then the minimum size is set to the same
value as the initial size.
.RE
.TP
\f[V]-XX:NewRatio=\f[R]\f[I]ratio\f[R]
Sets the ratio between young and old generation sizes.
By default, this option is set to 2.
The following example shows how to set the young-to-old ratio to 1:
.RS
.RS
.PP
\f[V]-XX:NewRatio=1\f[R]
.RE
.RE
.TP
\f[V]-XX:NewSize=\f[R]\f[I]size\f[R]
Sets the initial size (in bytes) of the heap for the young generation
(nursery).
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
.RS
.PP
The young generation region of the heap is used for new objects.
GC is performed in this region more often than in other regions.
If the size for the young generation is too low, then a large number of
minor GCs are performed.
If the size is too high, then only full GCs are performed, which can
take a long time to complete.
It is recommended that you keep the size for the young generation
greater than 25% and less than 50% of the overall heap size.
.PP
The following examples show how to set the initial size of the young
generation to 256 MB using various units:
.IP
.nf
\f[CB]
-XX:NewSize=256m
-XX:NewSize=262144k
-XX:NewSize=268435456
\f[R]
.fi
.PP
The \f[V]-XX:NewSize\f[R] option is equivalent to \f[V]-Xmn\f[R].
.RE
.TP
\f[V]-XX:ParallelGCThreads=\f[R]\f[I]threads\f[R]
Sets the number of the stop-the-world (STW) worker threads.
The default value depends on the number of CPUs available to the JVM and
the garbage collector selected.
.RS
.PP
For example, to set the number of threads for G1 GC to 2, specify the
following option:
.RS
.PP
\f[V]-XX:ParallelGCThreads=2\f[R]
.RE
.RE
.TP
\f[V]-XX:+ParallelRefProcEnabled\f[R]
Enables parallel reference processing.
By default, this option is disabled.
.TP
\f[V]-XX:+PrintAdaptiveSizePolicy\f[R]
Enables printing of information about adaptive-generation sizing.
By default, this option is disabled.
.TP
\f[V]-XX:SoftRefLRUPolicyMSPerMB=\f[R]\f[I]time\f[R]
Sets the amount of time (in milliseconds) a softly reachable object is
kept active on the heap after the last time it was referenced.
The default value is one second of lifetime per free megabyte in the
heap.
The \f[V]-XX:SoftRefLRUPolicyMSPerMB\f[R] option accepts integer values
representing milliseconds per one megabyte of the current heap size (for
Java HotSpot Client VM) or the maximum possible heap size (for Java
HotSpot Server VM).
This difference means that the Client VM tends to flush soft references
rather than grow the heap, whereas the Server VM tends to grow the heap
rather than flush soft references.
In the latter case, the value of the \f[V]-Xmx\f[R] option has a
significant effect on how quickly soft references are garbage collected.
.RS
.PP
The following example shows how to set the value to 2.5 seconds:
.PP
\f[V]-XX:SoftRefLRUPolicyMSPerMB=2500\f[R]
.RE
.TP
\f[V]-XX:-ShrinkHeapInSteps\f[R]
Incrementally reduces the Java heap to the target size, specified by the
option \f[V]-XX:MaxHeapFreeRatio\f[R].
This option is enabled by default.
If disabled, then it immediately reduces the Java heap to the target
size instead of requiring multiple garbage collection cycles.
Disable this option if you want to minimize the Java heap size.
You will likely encounter performance degradation when this option is
disabled.
.RS
.PP
See \f[B]Performance Tuning Examples\f[R] for a description of using the
\f[V]MaxHeapFreeRatio\f[R] option to keep the Java heap small by
reducing the dynamic footprint for embedded applications.
.RE
.TP
\f[V]-XX:StringDeduplicationAgeThreshold=\f[R]\f[I]threshold\f[R]
Identifies \f[V]String\f[R] objects reaching the specified age that are
considered candidates for deduplication.
An object\[aq]s age is a measure of how many times it has survived
garbage collection.
This is sometimes referred to as tenuring.
.RS
.RS
.PP
\f[B]Note:\f[R] \f[V]String\f[R] objects that are promoted to an old
heap region before this age has been reached are always considered
candidates for deduplication.
The default value for this option is \f[V]3\f[R].
See the \f[V]-XX:+UseStringDeduplication\f[R] option.
.RE
.RE
.TP
\f[V]-XX:SurvivorRatio=\f[R]\f[I]ratio\f[R]
Sets the ratio between eden space size and survivor space size.
By default, this option is set to 8.
The following example shows how to set the eden/survivor space ratio to
4:
.RS
.RS
.PP
\f[V]-XX:SurvivorRatio=4\f[R]
.RE
.RE
.TP
\f[V]-XX:TargetSurvivorRatio=\f[R]\f[I]percent\f[R]
Sets the desired percentage of survivor space (0 to 100) used after
young garbage collection.
By default, this option is set to 50%.
.RS
.PP
The following example shows how to set the target survivor space ratio
to 30%:
.RS
.PP
\f[V]-XX:TargetSurvivorRatio=30\f[R]
.RE
.RE
.TP
\f[V]-XX:TLABSize=\f[R]\f[I]size\f[R]
Sets the initial size (in bytes) of a thread-local allocation buffer
(TLAB).
Append the letter \f[V]k\f[R] or \f[V]K\f[R] to indicate kilobytes,
\f[V]m\f[R] or \f[V]M\f[R] to indicate megabytes, or \f[V]g\f[R] or
\f[V]G\f[R] to indicate gigabytes.
If this option is set to 0, then the JVM selects the initial size
automatically.
.RS
.PP
The following example shows how to set the initial TLAB size to 512 KB:
.RS
.PP
\f[V]-XX:TLABSize=512k\f[R]
.RE
.RE
.TP
\f[V]-XX:+UseAdaptiveSizePolicy\f[R]
Enables the use of adaptive generation sizing.
This option is enabled by default.
To disable adaptive generation sizing, specify
\f[V]-XX:-UseAdaptiveSizePolicy\f[R] and set the size of the memory
allocation pool explicitly.
See the \f[V]-XX:SurvivorRatio\f[R] option.
.TP
\f[V]-XX:+UseG1GC\f[R]
Enables the use of the garbage-first (G1) garbage collector.
It\[aq]s a server-style garbage collector, targeted for multiprocessor
machines with a large amount of RAM.
This option meets GC pause time goals with high probability, while
maintaining good throughput.
The G1 collector is recommended for applications requiring large heaps
(sizes of around 6 GB or larger) with limited GC latency requirements (a
stable and predictable pause time below 0.5 seconds).
By default, this option is enabled and G1 is used as the default garbage
collector.
.TP
\f[V]-XX:+UseGCOverheadLimit\f[R]
Enables the use of a policy that limits the proportion of time spent by
the JVM on GC before an \f[V]OutOfMemoryError\f[R] exception is thrown.
This option is enabled, by default, and the parallel GC will throw an
\f[V]OutOfMemoryError\f[R] if more than 98% of the total time is spent
on garbage collection and less than 2% of the heap is recovered.
When the heap is small, this feature can be used to prevent applications
from running for long periods of time with little or no progress.
To disable this option, specify the option
\f[V]-XX:-UseGCOverheadLimit\f[R].
.TP
\f[V]-XX:+UseNUMA\f[R]
Enables performance optimization of an application on a machine with
nonuniform memory architecture (NUMA) by increasing the
application\[aq]s use of lower latency memory.
By default, this option is disabled and no optimization for NUMA is
made.
The option is available only when the parallel garbage collector is used
(\f[V]-XX:+UseParallelGC\f[R]).
.TP
\f[V]-XX:+UseParallelGC\f[R]
Enables the use of the parallel scavenge garbage collector (also known
as the throughput collector) to improve the performance of your
application by leveraging multiple processors.
.RS
.PP
By default, this option is disabled and the default collector is used.
.RE
.TP
\f[V]-XX:+UseSerialGC\f[R]
Enables the use of the serial garbage collector.
This is generally the best choice for small and simple applications that
don\[aq]t require any special functionality from garbage collection.
By default, this option is disabled and the default collector is used.
.TP
\f[V]-XX:+UseStringDeduplication\f[R]
Enables string deduplication.
By default, this option is disabled.
To use this option, you must enable the garbage-first (G1) garbage
collector.
.RS
.PP
String deduplication reduces the memory footprint of \f[V]String\f[R]
objects on the Java heap by taking advantage of the fact that many
\f[V]String\f[R] objects are identical.
Instead of each \f[V]String\f[R] object pointing to its own character
array, identical \f[V]String\f[R] objects can point to and share the
same character array.
.RE
.TP
\f[V]-XX:+UseTLAB\f[R]
Enables the use of thread-local allocation blocks (TLABs) in the young
generation space.
This option is enabled by default.
To disable the use of TLABs, specify the option \f[V]-XX:-UseTLAB\f[R].
.TP
\f[V]-XX:+UseZGC\f[R]
Enables the use of the Z garbage collector (ZGC).
This is a low latency garbage collector, providing max pause times of a
few milliseconds, at some throughput cost.
Pause times are independent of what heap size is used.
Supports heap sizes from 8MB to 16TB.
.TP
\f[V]-XX:ZAllocationSpikeTolerance=\f[R]\f[I]factor\f[R]
Sets the allocation spike tolerance for ZGC.
By default, this option is set to 2.0.
This factor describes the level of allocation spikes to expect.
For example, using a factor of 3.0 means the current allocation rate can
be expected to triple at any time.
.TP
\f[V]-XX:ZCollectionInterval=\f[R]\f[I]seconds\f[R]
Sets the maximum interval (in seconds) between two GC cycles when using
ZGC.
By default, this option is set to 0 (disabled).
.TP
\f[V]-XX:ZFragmentationLimit=\f[R]\f[I]percent\f[R]
Sets the maximum acceptable heap fragmentation (in percent) for ZGC.
By default, this option is set to 25.
Using a lower value will cause the heap to be compacted more
aggressively, to reclaim more memory at the cost of using more CPU time.
.TP
\f[V]-XX:+ZProactive\f[R]
Enables proactive GC cycles when using ZGC.
By default, this option is enabled.
ZGC will start a proactive GC cycle if doing so is expected to have
minimal impact on the running application.
This is useful if the application is mostly idle or allocates very few
objects, but you still want to keep the heap size down and allow
reference processing to happen even when there are a lot of free space
on the heap.
.TP
\f[V]-XX:+ZUncommit\f[R]
Enables uncommitting of unused heap memory when using ZGC.
By default, this option is enabled.
Uncommitting unused heap memory will lower the memory footprint of the
JVM, and make that memory available for other processes to use.
.TP
\f[V]-XX:ZUncommitDelay=\f[R]\f[I]seconds\f[R]
Sets the amount of time (in seconds) that heap memory must have been
unused before being uncommitted.
By default, this option is set to 300 (5 minutes).
Committing and uncommitting memory are relatively expensive operations.
Using a lower value will cause heap memory to be uncommitted earlier, at
the risk of soon having to commit it again.
.SH DEPRECATED JAVA OPTIONS
.PP
These \f[V]java\f[R] options are deprecated and might be removed in a
future JDK release.
They\[aq]re still accepted and acted upon, but a warning is issued when
they\[aq]re used.
.TP
\f[V]-Xloggc:\f[R]\f[I]filename\f[R]
Sets the file to which verbose GC events information should be
redirected for logging.
The \f[V]-Xloggc\f[R] option overrides \f[V]-verbose:gc\f[R] if both are
given with the same java command.
\f[V]-Xloggc:\f[R]\f[I]filename\f[R] is replaced by
\f[V]-Xlog:gc:\f[R]\f[I]filename\f[R].
See Enable Logging with the JVM Unified Logging Framework.
.RS
.PP
Example:
.PP
\f[V]-Xlog:gc:garbage-collection.log\f[R]
.RE
.TP
\f[V]-XX:+FlightRecorder\f[R]
Enables the use of Java Flight Recorder (JFR) during the runtime of the
application.
Since JDK 8u40 this option has not been required to use JFR.
.SH OBSOLETE JAVA OPTIONS
.PP
These \f[V]java\f[R] options are still accepted but ignored, and a
warning is issued when they\[aq]re used.
.TP
\f[V]--illegal-access=\f[R]\f[I]parameter\f[R]
Controlled \f[I]relaxed strong encapsulation\f[R], as defined in
\f[B]JEP 261\f[R]
[https://openjdk.org/jeps/261#Relaxed-strong-encapsulation].
This option was deprecated in JDK 16 by \f[B]JEP 396\f[R]
[https://openjdk.org/jeps/396] and made obsolete in JDK 17 by \f[B]JEP
403\f[R] [https://openjdk.org/jeps/403].
.TP
\f[V]-XX:RTMAbortRatio=\f[R]\f[I]abort_ratio\f[R]
Specifies the RTM abort ratio is specified as a percentage (%) of all
executed RTM transactions.
If a number of aborted transactions becomes greater than this ratio,
then the compiled code is deoptimized.
This ratio is used when the \f[V]-XX:+UseRTMDeopt\f[R] option is
enabled.
The default value of this option is 50.
This means that the compiled code is deoptimized if 50% of all
transactions are aborted.
.TP
\f[V]-XX:RTMRetryCount=\f[R]\f[I]number_of_retries\f[R]
Specifies the number of times that the RTM locking code is retried, when
it is aborted or busy, before falling back to the normal locking
mechanism.
The default value for this option is 5.
The \f[V]-XX:UseRTMLocking\f[R] option must be enabled.
.TP
\f[V]-XX:+UseRTMDeopt\f[R]
Autotunes RTM locking depending on the abort ratio.
This ratio is specified by the \f[V]-XX:RTMAbortRatio\f[R] option.
If the number of aborted transactions exceeds the abort ratio, then the
method containing the lock is deoptimized and recompiled with all locks
as normal locks.
This option is disabled by default.
The \f[V]-XX:+UseRTMLocking\f[R] option must be enabled.
.TP
\f[V]-XX:+UseRTMLocking\f[R]
Generates Restricted Transactional Memory (RTM) locking code for all
inflated locks, with the normal locking mechanism as the fallback
handler.
This option is disabled by default.
Options related to RTM are available only on x86 CPUs that support
Transactional Synchronization Extensions (TSX).
.RS
.PP
RTM is part of Intel\[aq]s TSX, which is an x86 instruction set
extension and facilitates the creation of multithreaded applications.
RTM introduces the new instructions \f[V]XBEGIN\f[R], \f[V]XABORT\f[R],
\f[V]XEND\f[R], and \f[V]XTEST\f[R].
The \f[V]XBEGIN\f[R] and \f[V]XEND\f[R] instructions enclose a set of
instructions to run as a transaction.
If no conflict is found when running the transaction, then the memory
and register modifications are committed together at the \f[V]XEND\f[R]
instruction.
The \f[V]XABORT\f[R] instruction can be used to explicitly abort a
transaction and the \f[V]XTEST\f[R] instruction checks if a set of
instructions is being run in a transaction.
.PP
A lock on a transaction is inflated when another thread tries to access
the same transaction, thereby blocking the thread that didn\[aq]t
originally request access to the transaction.
RTM requires that a fallback set of operations be specified in case a
transaction aborts or fails.
An RTM lock is a lock that has been delegated to the TSX\[aq]s system.
.PP
RTM improves performance for highly contended locks with low conflict in
a critical region (which is code that must not be accessed by more than
one thread concurrently).
RTM also improves the performance of coarse-grain locking, which
typically doesn\[aq]t perform well in multithreaded applications.
(Coarse-grain locking is the strategy of holding locks for long periods
to minimize the overhead of taking and releasing locks, while
fine-grained locking is the strategy of trying to achieve maximum
parallelism by locking only when necessary and unlocking as soon as
possible.)
Also, for lightly contended locks that are used by different threads,
RTM can reduce false cache line sharing, also known as cache line
ping-pong.
This occurs when multiple threads from different processors are
accessing different resources, but the resources share the same cache
line.
As a result, the processors repeatedly invalidate the cache lines of
other processors, which forces them to read from main memory instead of
their cache.
.RE
.SH REMOVED JAVA OPTIONS
.PP
These \f[V]java\f[R] options have been removed in JDK 24 and using them
results in an error of:
.RS
.PP
\f[V]Unrecognized VM option\f[R] \f[I]option-name\f[R]
.RE
.TP
\f[V]-XX:InitialRAMFraction=\f[R]\f[I]ratio\f[R]
Sets the initial amount of memory that the JVM may use for the Java heap
before applying ergonomics heuristics as a ratio of the maximum amount
determined as described in the \f[V]-XX:MaxRAM\f[R] option.
The default value is 64.
.RS
.PP
Use the option \f[V]-XX:InitialRAMPercentage\f[R] instead.
.RE
.TP
\f[V]-XX:MaxRAMFraction=\f[R]\f[I]ratio\f[R]
Sets the maximum amount of memory that the JVM may use for the Java heap
before applying ergonomics heuristics as a fraction of the maximum
amount determined as described in the \f[V]-XX:MaxRAM\f[R] option.
The default value is 4.
.RS
.PP
Specifying this option disables automatic use of compressed oops if the
combined result of this and other options influencing the maximum amount
of memory is larger than the range of memory addressable by compressed
oops.
See \f[V]-XX:UseCompressedOops\f[R] for further information about
compressed oops.
.PP
Use the option \f[V]-XX:MaxRAMPercentage\f[R] instead.
.RE
.TP
\f[V]-XX:MinRAMFraction=\f[R]\f[I]ratio\f[R]
Sets the maximum amount of memory that the JVM may use for the Java heap
before applying ergonomics heuristics as a fraction of the maximum
amount determined as described in the \f[V]-XX:MaxRAM\f[R] option for
small heaps.
A small heap is a heap of approximately 125 MB.
The default value is 2.
.RS
.PP
Use the option \f[V]-XX:MinRAMPercentage\f[R] instead.
.RE
.TP
\f[V]-XX:+ScavengeBeforeFullGC\f[R]
Enables GC of the young generation before each full GC.
This option is enabled by default.
It is recommended that you \f[I]don\[aq]t\f[R] disable it, because
scavenging the young generation before a full GC can reduce the number
of objects reachable from the old generation space into the young
generation space.
To disable GC of the young generation before each full GC, specify the
option \f[V]-XX:-ScavengeBeforeFullGC\f[R].
.TP
\f[V]-Xfuture\f[R]
Enables strict class-file format checks that enforce close conformance
to the class-file format specification.
Developers should use this flag when developing new code.
Stricter checks may become the default in future releases.
.RS
.PP
Use the option \f[V]-Xverify:all\f[R] instead.
.RE
.PP
For the lists and descriptions of options removed in previous releases
see the \f[I]Removed Java Options\f[R] section in:
.IP \[bu] 2
\f[B]The \f[VB]java\f[B] Command, Release 23\f[R]
[https://docs.oracle.com/en/java/javase/23/docs/specs/man/java.html]
.IP \[bu] 2
\f[B]The \f[VB]java\f[B] Command, Release 22\f[R]
[https://docs.oracle.com/en/java/javase/22/docs/specs/man/java.html]
.IP \[bu] 2
\f[B]The \f[VB]java\f[B] Command, Release 21\f[R]
[https://docs.oracle.com/en/java/javase/21/docs/specs/man/java.html]
.IP \[bu] 2
\f[B]The \f[VB]java\f[B] Command, Release 20\f[R]
[https://docs.oracle.com/en/java/javase/20/docs/specs/man/java.html]
.IP \[bu] 2
\f[B]The \f[VB]java\f[B] Command, Release 19\f[R]
[https://docs.oracle.com/en/java/javase/19/docs/specs/man/java.html]
.IP \[bu] 2
\f[B]The \f[VB]java\f[B] Command, Release 18\f[R]
[https://docs.oracle.com/en/java/javase/18/docs/specs/man/java.html]
.IP \[bu] 2
\f[B]The \f[VB]java\f[B] Command, Release 17\f[R]
[https://docs.oracle.com/en/java/javase/17/docs/specs/man/java.html]
.IP \[bu] 2
\f[B]The \f[VB]java\f[B] Command, Release 16\f[R]
[https://docs.oracle.com/en/java/javase/16/docs/specs/man/java.html]
.IP \[bu] 2
\f[B]The \f[VB]java\f[B] Command, Release 15\f[R]
[https://docs.oracle.com/en/java/javase/15/docs/specs/man/java.html]
.IP \[bu] 2
\f[B]The \f[VB]java\f[B] Command, Release 14\f[R]
[https://docs.oracle.com/en/java/javase/14/docs/specs/man/java.html]
.IP \[bu] 2
\f[B]The \f[VB]java\f[B] Command, Release 13\f[R]
[https://docs.oracle.com/en/java/javase/13/docs/specs/man/java.html]
.IP \[bu] 2
\f[B]Java Platform, Standard Edition Tools Reference, Release 12\f[R]
[https://docs.oracle.com/en/java/javase/12/tools/java.html#GUID-3B1CE181-CD30-4178-9602-230B800D4FAE]
.IP \[bu] 2
\f[B]Java Platform, Standard Edition Tools Reference, Release 11\f[R]
[https://docs.oracle.com/en/java/javase/11/tools/java.html#GUID-741FC470-AA3E-494A-8D2B-1B1FE4A990D1]
.IP \[bu] 2
\f[B]Java Platform, Standard Edition Tools Reference, Release 10\f[R]
[https://docs.oracle.com/javase/10/tools/java.htm#JSWOR624]
.IP \[bu] 2
\f[B]Java Platform, Standard Edition Tools Reference, Release 9\f[R]
[https://docs.oracle.com/javase/9/tools/java.htm#JSWOR624]
.IP \[bu] 2
\f[B]Java Platform, Standard Edition Tools Reference, Release 8 for
Oracle JDK on Windows\f[R]
[https://docs.oracle.com/javase/8/docs/technotes/tools/windows/java.html#BGBCIEFC]
.IP \[bu] 2
\f[B]Java Platform, Standard Edition Tools Reference, Release 8 for
Oracle JDK on Solaris, Linux, and macOS\f[R]
[https://docs.oracle.com/javase/8/docs/technotes/tools/unix/java.html#BGBCIEFC]
.SH JAVA COMMAND-LINE ARGUMENT FILES
.PP
You can shorten or simplify the \f[V]java\f[R] command by using
\f[V]\[at]\f[R] argument files to specify one or more text files that
contain arguments, such as options and class names, which are passed to
the \f[V]java\f[R] command.
This let\[aq]s you to create \f[V]java\f[R] commands of any length on
any operating system.
.PP
In the command line, use the at sign (\f[V]\[at]\f[R]) prefix to
identify an argument file that contains \f[V]java\f[R] options and class
names.
When the \f[V]java\f[R] command encounters a file beginning with the at
sign (\f[V]\[at]\f[R]), it expands the contents of that file into an
argument list just as they would be specified on the command line.
.PP
The \f[V]java\f[R] launcher expands the argument file contents until it
encounters the \f[V]--disable-\[at]files\f[R] option.
You can use the \f[V]--disable-\[at]files\f[R] option anywhere on the
command line, including in an argument file, to stop \f[V]\[at]\f[R]
argument files expansion.
.PP
The following items describe the syntax of \f[V]java\f[R] argument
files:
.IP \[bu] 2
The argument file must contain only ASCII characters or characters in
system default encoding that\[aq]s ASCII friendly, such as UTF-8.
.IP \[bu] 2
The argument file size must not exceed MAXINT (2,147,483,647) bytes.
.IP \[bu] 2
The launcher doesn\[aq]t expand wildcards that are present within an
argument file.
That means an asterisk (\f[V]*\f[R]) is passed on as-is to the starting
VM.
For example \f[V]*.java\f[R] stays \f[V]*.java\f[R] and is not expanded
to \f[V]Foo.java Bar.java ...\f[R], as would happen with some command
line shells.
.IP \[bu] 2
Use white space or new line characters to separate arguments included in
the file.
.IP \[bu] 2
White space includes a white space character, \f[V]\[rs]t\f[R],
\f[V]\[rs]n\f[R], \f[V]\[rs]r\f[R], and \f[V]\[rs]f\f[R].
.RS 2
.PP
For example, it is possible to have a path with a space, such as
\f[V]c:\[rs]Program Files\f[R] that can be specified as either
\f[V]\[dq]c:\[rs]\[rs]Program Files\[dq]\f[R] or, to avoid an escape,
\f[V]c:\[rs]Program\[dq] \[dq]Files\f[R].
.RE
.IP \[bu] 2
Any option that contains spaces, such as a path component, must be
within quotation marks using quotation (\[aq]\[dq]\[aq]) characters in
its entirety.
.IP \[bu] 2
A string within quotation marks may contain the characters
\f[V]\[rs]n\f[R], \f[V]\[rs]r\f[R], \f[V]\[rs]t\f[R], and
\f[V]\[rs]f\f[R].
They are converted to their respective ASCII codes.
.IP \[bu] 2
If a file name contains embedded spaces, then put the whole file name in
double quotation marks.
.IP \[bu] 2
File names in an argument file are relative to the current directory,
not to the location of the argument file.
.IP \[bu] 2
Use the number sign \f[V]#\f[R] in the argument file to identify
comments.
All characters following the \f[V]#\f[R] are ignored until the end of
line.
.IP \[bu] 2
Additional at sign \f[V]\[at]\f[R] prefixes to \f[V]\[at]\f[R] prefixed
options act as an escape, (the first \f[V]\[at]\f[R] is removed and the
rest of the arguments are presented to the launcher literally).
.IP \[bu] 2
Lines may be continued using the continuation character
(\f[V]\[rs]\f[R]) at the end-of-line.
The two lines are concatenated with the leading white spaces trimmed.
To prevent trimming the leading white spaces, a continuation character
(\f[V]\[rs]\f[R]) may be placed at the first column.
.IP \[bu] 2
Because backslash (\[rs]) is an escape character, a backslash character
must be escaped with another backslash character.
.IP \[bu] 2
Partial quote is allowed and is closed by an end-of-file.
.IP \[bu] 2
An open quote stops at end-of-line unless \f[V]\[rs]\f[R] is the last
character, which then joins the next line by removing all leading white
space characters.
.IP \[bu] 2
Use of the at sign (\f[V]\[at]\f[R]) to recursively interpret files
isn\[aq]t supported.
.SS Example of Open or Partial Quotes in an Argument File
.PP
In the argument file,
.IP
.nf
\f[CB]
-cp \[dq]lib/
cool/
app/
jars
\f[R]
.fi
.PP
this is interpreted as:
.RS
.PP
\f[V]-cp lib/cool/app/jars\f[R]
.RE
.SS Example of a Backslash Character Escaped with Another Backslash Character in an Argument File
.PP
To output the following:
.RS
.PP
\f[V]-cp c:\[rs]Program Files (x86)\[rs]Java\[rs]jre\[rs]lib\[rs]ext;c:\[rs]Program Files\[rs]Java\[rs]jre9\[rs]lib\[rs]ext\f[R]
.RE
.PP
The backslash character must be specified in the argument file as:
.RS
.PP
\f[V]-cp \[dq]c:\[rs]\[rs]Program Files (x86)\[rs]\[rs]Java\[rs]\[rs]jre\[rs]\[rs]lib\[rs]\[rs]ext;c:\[rs]\[rs]Program Files\[rs]\[rs]Java\[rs]\[rs]jre9\[rs]\[rs]lib\[rs]\[rs]ext\[dq]\f[R]
.RE
.SS Example of an EOL Escape Used to Force Concatenation of Lines in an Argument File
.PP
In the argument file,
.IP
.nf
\f[CB]
-cp \[dq]/lib/cool app/jars:\[rs]
    /lib/another app/jars\[dq]
\f[R]
.fi
.PP
This is interpreted as:
.RS
.PP
\f[V]-cp /lib/cool app/jars:/lib/another app/jars\f[R]
.RE
.SS Example of Line Continuation with Leading Spaces in an Argument File
.PP
In the argument file,
.IP
.nf
\f[CB]
-cp \[dq]/lib/cool\[rs]
\[rs]app/jars\[dq]
\f[R]
.fi
.PP
This is interpreted as:
.PP
\f[V]-cp /lib/cool app/jars\f[R]
.SS Examples of Using Single Argument File
.PP
You can use a single argument file, such as \f[V]myargumentfile\f[R] in
the following example, to hold all required \f[V]java\f[R] arguments:
.RS
.PP
\f[V]java \[at]myargumentfile\f[R]
.RE
.SS Examples of Using Argument Files with Paths
.PP
You can include relative paths in argument files; however, they\[aq]re
relative to the current working directory and not to the paths of the
argument files themselves.
In the following example, \f[V]path1/options\f[R] and
\f[V]path2/options\f[R] represent argument files with different paths.
Any relative paths that they contain are relative to the current working
directory and not to the argument files:
.RS
.PP
\f[V]java \[at]path1/options \[at]path2/classes\f[R]
.RE
.SH CODE HEAP STATE ANALYTICS
.SS Overview
.PP
There are occasions when having insight into the current state of the
JVM code heap would be helpful to answer questions such as:
.IP \[bu] 2
Why was the JIT turned off and then on again and again?
.IP \[bu] 2
Where has all the code heap space gone?
.IP \[bu] 2
Why is the method sweeper not working effectively?
.PP
To provide this insight, a code heap state analytics feature has been
implemented that enables on-the-fly analysis of the code heap.
The analytics process is divided into two parts.
The first part examines the entire code heap and aggregates all
information that is believed to be useful or important.
The second part consists of several independent steps that print the
collected information with an emphasis on different aspects of the data.
Data collection and printing are done on an \[dq]on request\[dq] basis.
.SS Syntax
.PP
Requests for real-time, on-the-fly analysis can be issued with the
following command:
.RS
.PP
\f[V]jcmd\f[R] \f[I]pid\f[R] \f[V]Compiler.CodeHeap_Analytics\f[R]
[\f[I]function\f[R]] [\f[I]granularity\f[R]]
.RE
.PP
If you are only interested in how the code heap looks like after running
a sample workload, you can use the command line option:
.RS
.PP
\f[V]-Xlog:codecache=Trace\f[R]
.RE
.PP
To see the code heap state when a \[dq]CodeCache full\[dq] condition
exists, start the VM with the command line option:
.RS
.PP
\f[V]-Xlog:codecache=Debug\f[R]
.RE
.PP
See \f[B]CodeHeap State Analytics (OpenJDK)\f[R]
[https://bugs.openjdk.org/secure/attachment/75649/JVM_CodeHeap_StateAnalytics_V2.pdf]
for a detailed description of the code heap state analytics feature, the
supported functions, and the granularity options.
.SH ENABLE LOGGING WITH THE JVM UNIFIED LOGGING FRAMEWORK
.PP
You use the \f[V]-Xlog\f[R] option to configure or enable logging with
the Java Virtual Machine (JVM) unified logging framework.
.SS Synopsis
.RS
.PP
\f[V]-Xlog\f[R][\f[V]:\f[R][\f[I]what\f[R]][\f[V]:\f[R][\f[I]output\f[R]][\f[V]:\f[R][\f[I]decorators\f[R]][\f[V]:\f[R]\f[I]output-options\f[R][\f[V],\f[R]...]]]]]
.PP
\f[V]-Xlog:\f[R]\f[I]directive\f[R]
.RE
.TP
\f[I]what\f[R]
Specifies a combination of tags and levels of the form
\f[I]tag1\f[R][\f[V]+\f[R]\f[I]tag2\f[R]...][\f[V]*\f[R]][\f[V]=\f[R]\f[I]level\f[R]][\f[V],\f[R]...].
Unless the wildcard (\f[V]*\f[R]) is specified, only log messages tagged
with exactly the tags specified are matched.
See \f[B]-Xlog Tags and Levels\f[R].
.TP
\f[I]output\f[R]
Sets the type of output.
Omitting the \f[I]output\f[R] type defaults to \f[V]stdout\f[R].
See \f[B]-Xlog Output\f[R].
.TP
\f[I]decorators\f[R]
Configures the output to use a custom set of decorators.
Omitting \f[I]decorators\f[R] defaults to \f[V]uptime\f[R],
\f[V]level\f[R], and \f[V]tags\f[R].
See \f[B]Decorations\f[R].
.TP
\f[I]output-options\f[R]
Sets the \f[V]-Xlog\f[R] logging output options.
.TP
\f[I]directive\f[R]
A global option or subcommand: help, disable, async
.SS Description
.PP
The Java Virtual Machine (JVM) unified logging framework provides a
common logging system for all components of the JVM.
GC logging for the JVM has been changed to use the new logging
framework.
The mapping of old GC flags to the corresponding new Xlog configuration
is described in \f[B]Convert GC Logging Flags to Xlog\f[R].
In addition, runtime logging has also been changed to use the JVM
unified logging framework.
The mapping of legacy runtime logging flags to the corresponding new
Xlog configuration is described in \f[B]Convert Runtime Logging Flags to
Xlog\f[R].
.PP
The following provides quick reference to the \f[V]-Xlog\f[R] command
and syntax for options:
.TP
\f[V]-Xlog\f[R]
Enables JVM logging on an \f[V]info\f[R] level.
.TP
\f[V]-Xlog:help\f[R]
Prints \f[V]-Xlog\f[R] usage syntax and available tags, levels, and
decorators along with example command lines with explanations.
.TP
\f[V]-Xlog:disable\f[R]
Turns off all logging and clears all configuration of the logging
framework including the default configuration for warnings and errors.
.TP
\f[V]-Xlog\f[R][\f[V]:\f[R]\f[I]option\f[R]]
Applies multiple arguments in the order that they appear on the command
line.
Multiple \f[V]-Xlog\f[R] arguments for the same output override each
other in their given order.
.RS
.PP
The \f[I]option\f[R] is set as:
.RS
.PP
[\f[I]tag-selection\f[R]][\f[V]:\f[R][\f[I]output\f[R]][\f[V]:\f[R][\f[I]decorators\f[R]][\f[V]:\f[R]\f[I]output-options\f[R]]]]
.RE
.PP
Omitting the \f[I]tag-selection\f[R] defaults to a tag-set of
\f[V]all\f[R] and a level of \f[V]info\f[R].
.RS
.PP
\f[I]tag\f[R][\f[V]+\f[R]...]
\f[V]all\f[R]
.RE
.PP
The \f[V]all\f[R] tag is a meta tag consisting of all tag-sets
available.
The asterisk \f[V]*\f[R] in a tag set definition denotes a wildcard tag
match.
Matching with a wildcard selects all tag sets that contain \f[I]at
least\f[R] the specified tags.
Without the wildcard, only exact matches of the specified tag sets are
selected.
.PP
\f[I]output-options\f[R] is
.RS
.PP
\f[V]filecount=\f[R]\f[I]file-count\f[R] \f[V]filesize=\f[R]\f[I]file
size with optional K, M or G suffix\f[R]
\f[V]foldmultilines=\f[R]\f[I]<true|false>\f[R]
.RE
.PP
When \f[V]foldmultilines\f[R] is true, a log event that consists of
multiple lines will be folded into a single line by replacing newline
characters with the sequence \f[V]\[aq]\[rs]\[aq]\f[R] and
\f[V]\[aq]n\[aq]\f[R] in the output.
Existing single backslash characters will also be replaced with a
sequence of two backslashes so that the conversion can be reversed.
This option is safe to use with UTF-8 character encodings, but other
encodings may not work.
For example, it may incorrectly convert multi-byte sequences in Shift
JIS and BIG5.
.RE
.SS Default Configuration
.PP
When the \f[V]-Xlog\f[R] option and nothing else is specified on the
command line, the default configuration is used.
The default configuration logs all messages with a level that matches
either warning or error regardless of what tags the message is
associated with.
The default configuration is equivalent to entering the following on the
command line:
.RS
.PP
\f[V]-Xlog:all=warning:stdout:uptime,level,tags\f[R]
.RE
.SS Controlling Logging at Runtime
.PP
Logging can also be controlled at run time through Diagnostic Commands
(with the \f[B]jcmd\f[R] utility).
Everything that can be specified on the command line can also be
specified dynamically with the \f[V]VM.log\f[R] command.
As the diagnostic commands are automatically exposed as MBeans, you can
use JMX to change logging configuration at run time.
.SS -Xlog Tags and Levels
.PP
Each log message has a level and a tag set associated with it.
The level of the message corresponds to its details, and the tag set
corresponds to what the message contains or which JVM component it
involves (such as, \f[V]gc\f[R], \f[V]jit\f[R], or \f[V]os\f[R]).
Mapping GC flags to the Xlog configuration is described in \f[B]Convert
GC Logging Flags to Xlog\f[R].
Mapping legacy runtime logging flags to the corresponding Xlog
configuration is described in \f[B]Convert Runtime Logging Flags to
Xlog\f[R].
.PP
\f[B]Available log levels:\f[R]
.IP \[bu] 2
\f[V]off\f[R]
.IP \[bu] 2
\f[V]trace\f[R]
.IP \[bu] 2
\f[V]debug\f[R]
.IP \[bu] 2
\f[V]info\f[R]
.IP \[bu] 2
\f[V]warning\f[R]
.IP \[bu] 2
\f[V]error\f[R]
.PP
\f[B]Available log tags:\f[R]
.PP
There are literally dozens of log tags, which in the right combinations,
will enable a range of logging output.
The full set of available log tags can be seen using
\f[V]-Xlog:help\f[R].
Specifying \f[V]all\f[R] instead of a tag combination matches all tag
combinations.
.SS -Xlog Output
.PP
The \f[V]-Xlog\f[R] option supports the following types of outputs:
.IP \[bu] 2
\f[V]stdout\f[R] --- Sends output to stdout
.IP \[bu] 2
\f[V]stderr\f[R] --- Sends output to stderr
.IP \[bu] 2
\f[V]file=\f[R]\f[I]filename\f[R] --- Sends output to text file(s).
.PP
When using \f[V]file=\f[R]\f[I]filename\f[R], specifying \f[V]%p\f[R],
\f[V]%t\f[R] and/or \f[V]%hn\f[R] in the file name expands to the
JVM\[aq]s PID, startup timestamp and host name, respectively.
You can also configure text files to handle file rotation based on file
size and a number of files to rotate.
For example, to rotate the log file every 10 MB and keep 5 files in
rotation, specify the options \f[V]filesize=10M, filecount=5\f[R].
The target size of the files isn\[aq]t guaranteed to be exact, it\[aq]s
just an approximate value.
Files are rotated by default with up to 5 rotated files of target size
20 MB, unless configured otherwise.
Specifying \f[V]filecount=0\f[R] means that the log file shouldn\[aq]t
be rotated.
There\[aq]s a possibility of the pre-existing log file getting
overwritten.
.SS -Xlog Output Mode
.PP
By default logging messages are output synchronously - each log message
is written to the designated output when the logging call is made.
But you can instead use asynchronous logging mode by specifying:
.TP
\f[V]-Xlog:async\f[R]
Write all logging asynchronously.
.PP
In asynchronous logging mode, log sites enqueue all logging messages to
an intermediate buffer and a standalone thread is responsible for
flushing them to the corresponding outputs.
The intermediate buffer is bounded and on buffer exhaustion the
enqueuing message is discarded.
Log entry write operations are guaranteed non-blocking.
.PP
The option \f[V]-XX:AsyncLogBufferSize=N\f[R] specifies the memory
budget in bytes for the intermediate buffer.
The default value should be big enough to cater for most cases.
Users can provide a custom value to trade memory overhead for log
accuracy if they need to.
.SS Decorations
.PP
Logging messages are decorated with information about the message.
You can configure each output to use a custom set of decorators.
The order of the output is always the same as listed in the table.
You can configure the decorations to be used at run time.
Decorations are prepended to the log message.
For example:
.IP
.nf
\f[CB]
[6.567s][info][gc,old] Old collection complete
\f[R]
.fi
.PP
Omitting \f[V]decorators\f[R] defaults to \f[V]uptime\f[R],
\f[V]level\f[R], and \f[V]tags\f[R].
The \f[V]none\f[R] decorator is special and is used to turn off all
decorations.
.PP
\f[V]time\f[R] (\f[V]t\f[R]), \f[V]utctime\f[R] (\f[V]utc\f[R]),
\f[V]uptime\f[R] (\f[V]u\f[R]), \f[V]timemillis\f[R] (\f[V]tm\f[R]),
\f[V]uptimemillis\f[R] (\f[V]um\f[R]), \f[V]timenanos\f[R]
(\f[V]tn\f[R]), \f[V]uptimenanos\f[R] (\f[V]un\f[R]), \f[V]hostname\f[R]
(\f[V]hn\f[R]), \f[V]pid\f[R] (\f[V]p\f[R]), \f[V]tid\f[R]
(\f[V]ti\f[R]), \f[V]level\f[R] (\f[V]l\f[R]), \f[V]tags\f[R]
(\f[V]tg\f[R]) decorators can also be specified as \f[V]none\f[R] for no
decoration.
.PP
Logging Messages Decorations
.TS
tab(@);
lw(14.9n) lw(55.1n).
T{
Decorations
T}@T{
Description
T}
_
T{
\f[V]time\f[R] or \f[V]t\f[R]
T}@T{
Current time and date in ISO-8601 format.
T}
T{
\f[V]utctime\f[R] or \f[V]utc\f[R]
T}@T{
Universal Time Coordinated or Coordinated Universal Time.
T}
T{
\f[V]uptime\f[R] or \f[V]u\f[R]
T}@T{
Time since the start of the JVM in seconds and milliseconds.
For example, 6.567s.
T}
T{
\f[V]timemillis\f[R] or \f[V]tm\f[R]
T}@T{
The same value as generated by \f[V]System.currentTimeMillis()\f[R]
T}
T{
\f[V]uptimemillis\f[R] or \f[V]um\f[R]
T}@T{
Milliseconds since the JVM started.
T}
T{
\f[V]timenanos\f[R] or \f[V]tn\f[R]
T}@T{
The same value generated by \f[V]System.nanoTime()\f[R].
T}
T{
\f[V]uptimenanos\f[R] or \f[V]un\f[R]
T}@T{
Nanoseconds since the JVM started.
T}
T{
\f[V]hostname\f[R] or \f[V]hn\f[R]
T}@T{
The host name.
T}
T{
\f[V]pid\f[R] or \f[V]p\f[R]
T}@T{
The process identifier.
T}
T{
\f[V]tid\f[R] or \f[V]ti\f[R]
T}@T{
The thread identifier.
T}
T{
\f[V]level\f[R] or \f[V]l\f[R]
T}@T{
The level associated with the log message.
T}
T{
\f[V]tags\f[R] or \f[V]tg\f[R]
T}@T{
The tag-set associated with the log message.
T}
.TE
.SS Convert GC Logging Flags to Xlog
.PP
Legacy GC Logging Flags to Xlog Configuration Mapping
.TS
tab(@);
lw(22.4n) lw(16.5n) lw(31.2n).
T{
Legacy Garbage Collection (GC) Flag
T}@T{
Xlog Configuration
T}@T{
Comment
T}
_
T{
\f[V]G1PrintHeapRegions\f[R]
T}@T{
\f[V]-Xlog:gc+region=trace\f[R]
T}@T{
Not Applicable
T}
T{
\f[V]GCLogFileSize\f[R]
T}@T{
No configuration available
T}@T{
Log rotation is handled by the framework.
T}
T{
\f[V]NumberOfGCLogFiles\f[R]
T}@T{
Not Applicable
T}@T{
Log rotation is handled by the framework.
T}
T{
\f[V]PrintAdaptiveSizePolicy\f[R]
T}@T{
\f[V]-Xlog:gc+ergo*=\f[R]\f[I]level\f[R]
T}@T{
Use a \f[I]level\f[R] of \f[V]debug\f[R] for most of the information, or
a \f[I]level\f[R] of \f[V]trace\f[R] for all of what was logged for
\f[V]PrintAdaptiveSizePolicy\f[R].
T}
T{
\f[V]PrintGC\f[R]
T}@T{
\f[V]-Xlog:gc\f[R]
T}@T{
Not Applicable
T}
T{
\f[V]PrintGCApplicationConcurrentTime\f[R]
T}@T{
\f[V]-Xlog:safepoint\f[R]
T}@T{
Note that \f[V]PrintGCApplicationConcurrentTime\f[R] and
\f[V]PrintGCApplicationStoppedTime\f[R] are logged on the same tag and
aren\[aq]t separated in the new logging.
T}
T{
\f[V]PrintGCApplicationStoppedTime\f[R]
T}@T{
\f[V]-Xlog:safepoint\f[R]
T}@T{
Note that \f[V]PrintGCApplicationConcurrentTime\f[R] and
\f[V]PrintGCApplicationStoppedTime\f[R] are logged on the same tag and
not separated in the new logging.
T}
T{
\f[V]PrintGCCause\f[R]
T}@T{
Not Applicable
T}@T{
GC cause is now always logged.
T}
T{
\f[V]PrintGCDateStamps\f[R]
T}@T{
Not Applicable
T}@T{
Date stamps are logged by the framework.
T}
T{
\f[V]PrintGCDetails\f[R]
T}@T{
\f[V]-Xlog:gc*\f[R]
T}@T{
Not Applicable
T}
T{
\f[V]PrintGCID\f[R]
T}@T{
Not Applicable
T}@T{
GC ID is now always logged.
T}
T{
\f[V]PrintGCTaskTimeStamps\f[R]
T}@T{
\f[V]-Xlog:gc+task*=debug\f[R]
T}@T{
Not Applicable
T}
T{
\f[V]PrintGCTimeStamps\f[R]
T}@T{
Not Applicable
T}@T{
Time stamps are logged by the framework.
T}
T{
\f[V]PrintHeapAtGC\f[R]
T}@T{
\f[V]-Xlog:gc+heap=trace\f[R]
T}@T{
Not Applicable
T}
T{
\f[V]PrintReferenceGC\f[R]
T}@T{
\f[V]-Xlog:gc+ref*=debug\f[R]
T}@T{
Note that in the old logging, \f[V]PrintReferenceGC\f[R] had an effect
only if \f[V]PrintGCDetails\f[R] was also enabled.
T}
T{
\f[V]PrintStringDeduplicationStatistics\f[R]
T}@T{
\[ga]-Xlog:gc+stringdedup*=debug
T}@T{
\[ga] Not Applicable
T}
T{
\f[V]PrintTenuringDistribution\f[R]
T}@T{
\f[V]-Xlog:gc+age*=\f[R]\f[I]level\f[R]
T}@T{
Use a \f[I]level\f[R] of \f[V]debug\f[R] for the most relevant
information, or a \f[I]level\f[R] of \f[V]trace\f[R] for all of what was
logged for \f[V]PrintTenuringDistribution\f[R].
T}
T{
\f[V]UseGCLogFileRotation\f[R]
T}@T{
Not Applicable
T}@T{
What was logged for \f[V]PrintTenuringDistribution\f[R].
T}
.TE
.SS Convert Runtime Logging Flags to Xlog
.PP
These legacy flags are no longer recognized and will cause an error if
used directly.
Use their unified logging equivalent instead.
.PP
Runtime Logging Flags to Xlog Configuration Mapping
.TS
tab(@);
lw(15.0n) lw(20.2n) lw(34.7n).
T{
Legacy Runtime Flag
T}@T{
Xlog Configuration
T}@T{
Comment
T}
_
T{
\f[V]TraceExceptions\f[R]
T}@T{
\f[V]-Xlog:exceptions=info\f[R]
T}@T{
Not Applicable
T}
T{
\f[V]TraceClassLoading\f[R]
T}@T{
\f[V]-Xlog:class+load=\f[R]\f[I]level\f[R]
T}@T{
Use \f[I]level\f[R]=\f[V]info\f[R] for regular information, or
\f[I]level\f[R]=\f[V]debug\f[R] for additional information.
In Unified Logging syntax, \f[V]-verbose:class\f[R] equals
\f[V]-Xlog:class+load=info,class+unload=info\f[R].
T}
T{
\f[V]TraceClassLoadingPreorder\f[R]
T}@T{
\f[V]-Xlog:class+preorder=debug\f[R]
T}@T{
Not Applicable
T}
T{
\f[V]TraceClassUnloading\f[R]
T}@T{
\f[V]-Xlog:class+unload=\f[R]\f[I]level\f[R]
T}@T{
Use \f[I]level\f[R]=\f[V]info\f[R] for regular information, or
\f[I]level\f[R]=\f[V]trace\f[R] for additional information.
In Unified Logging syntax, \f[V]-verbose:class\f[R] equals
\f[V]-Xlog:class+load=info,class+unload=info\f[R].
T}
T{
\f[V]VerboseVerification\f[R]
T}@T{
\f[V]-Xlog:verification=info\f[R]
T}@T{
Not Applicable
T}
T{
\f[V]TraceClassPaths\f[R]
T}@T{
\f[V]-Xlog:class+path=info\f[R]
T}@T{
Not Applicable
T}
T{
\f[V]TraceClassResolution\f[R]
T}@T{
\f[V]-Xlog:class+resolve=debug\f[R]
T}@T{
Not Applicable
T}
T{
\f[V]TraceClassInitialization\f[R]
T}@T{
\f[V]-Xlog:class+init=info\f[R]
T}@T{
Not Applicable
T}
T{
\f[V]TraceLoaderConstraints\f[R]
T}@T{
\f[V]-Xlog:class+loader+constraints=info\f[R]
T}@T{
Not Applicable
T}
T{
\f[V]TraceClassLoaderData\f[R]
T}@T{
\f[V]-Xlog:class+loader+data=\f[R]\f[I]level\f[R]
T}@T{
Use \f[I]level\f[R]=\f[V]debug\f[R] for regular information or
\f[I]level\f[R]=\f[V]trace\f[R] for additional information.
T}
T{
\f[V]TraceSafepointCleanupTime\f[R]
T}@T{
\f[V]-Xlog:safepoint+cleanup=info\f[R]
T}@T{
Not Applicable
T}
T{
\f[V]TraceSafepoint\f[R]
T}@T{
\f[V]-Xlog:safepoint=debug\f[R]
T}@T{
Not Applicable
T}
T{
\f[V]TraceMonitorInflation\f[R]
T}@T{
\f[V]-Xlog:monitorinflation=debug\f[R]
T}@T{
Not Applicable
T}
T{
\f[V]TraceRedefineClasses\f[R]
T}@T{
\f[V]-Xlog:redefine+class*=\f[R]\f[I]level\f[R]
T}@T{
\f[I]level\f[R]=\f[V]info\f[R], \f[V]debug\f[R], and \f[V]trace\f[R]
provide increasing amounts of information.
T}
.TE
.SS -Xlog Usage Examples
.PP
The following are \f[V]-Xlog\f[R] examples.
.TP
\f[V]-Xlog\f[R]
Logs all messages by using the \f[V]info\f[R] level to \f[V]stdout\f[R]
with \f[V]uptime\f[R], \f[V]levels\f[R], and \f[V]tags\f[R] decorations.
This is equivalent to using:
.RS
.RS
.PP
\f[V]-Xlog:all=info:stdout:uptime,levels,tags\f[R]
.RE
.RE
.TP
\f[V]-Xlog:gc\f[R]
Logs messages tagged with the \f[V]gc\f[R] tag using \f[V]info\f[R]
level to \f[V]stdout\f[R].
The default configuration for all other messages at level
\f[V]warning\f[R] is in effect.
.TP
\f[V]-Xlog:gc,safepoint\f[R]
Logs messages tagged either with the \f[V]gc\f[R] or \f[V]safepoint\f[R]
tags, both using the \f[V]info\f[R] level, to \f[V]stdout\f[R], with
default decorations.
Messages tagged with both \f[V]gc\f[R] and \f[V]safepoint\f[R] won\[aq]t
be logged.
.TP
\f[V]-Xlog:gc+ref=debug\f[R]
Logs messages tagged with both \f[V]gc\f[R] and \f[V]ref\f[R] tags,
using the \f[V]debug\f[R] level to \f[V]stdout\f[R], with default
decorations.
Messages tagged only with one of the two tags won\[aq]t be logged.
.TP
\f[V]-Xlog:gc=debug:file=gc.txt:none\f[R]
Logs messages tagged with the \f[V]gc\f[R] tag using the \f[V]debug\f[R]
level to a file called \f[V]gc.txt\f[R] with no decorations.
The default configuration for all other messages at level
\f[V]warning\f[R] is still in effect.
.TP
\f[V]-Xlog:gc=trace:file=gctrace.txt:uptimemillis,pid:filecount=5,filesize=1024\f[R]
Logs messages tagged with the \f[V]gc\f[R] tag using the \f[V]trace\f[R]
level to a rotating file set with 5 files with size 1 MB with the base
name \f[V]gctrace.txt\f[R] and uses decorations \f[V]uptimemillis\f[R]
and \f[V]pid\f[R].
.RS
.PP
The default configuration for all other messages at level
\f[V]warning\f[R] is still in effect.
.RE
.TP
\f[V]-Xlog:gc::uptime,tid\f[R]
Logs messages tagged with the \f[V]gc\f[R] tag using the default
\[aq]info\[aq] level to default the output \f[V]stdout\f[R] and uses
decorations \f[V]uptime\f[R] and \f[V]tid\f[R].
The default configuration for all other messages at level
\f[V]warning\f[R] is still in effect.
.TP
\f[V]-Xlog:gc*=info,safepoint*=off\f[R]
Logs messages tagged with at least \f[V]gc\f[R] using the \f[V]info\f[R]
level, but turns off logging of messages tagged with
\f[V]safepoint\f[R].
Messages tagged with both \f[V]gc\f[R] and \f[V]safepoint\f[R] won\[aq]t
be logged.
.TP
\f[V]-Xlog:disable -Xlog:safepoint=trace:safepointtrace.txt\f[R]
Turns off all logging, including warnings and errors, and then enables
messages tagged with \f[V]safepoint\f[R]using \f[V]trace\f[R]level to
the file \f[V]safepointtrace.txt\f[R].
The default configuration doesn\[aq]t apply, because the command line
started with \f[V]-Xlog:disable\f[R].
.SS Complex -Xlog Usage Examples
.PP
The following describes a few complex examples of using the
\f[V]-Xlog\f[R] option.
.TP
\f[V]-Xlog:gc+class*=debug\f[R]
Logs messages tagged with at least \f[V]gc\f[R] and \f[V]class\f[R] tags
using the \f[V]debug\f[R] level to \f[V]stdout\f[R].
The default configuration for all other messages at the level
\f[V]warning\f[R] is still in effect
.TP
\f[V]-Xlog:gc+meta*=trace,class*=off:file=gcmetatrace.txt\f[R]
Logs messages tagged with at least the \f[V]gc\f[R] and \f[V]meta\f[R]
tags using the \f[V]trace\f[R] level to the file \f[V]metatrace.txt\f[R]
but turns off all messages tagged with \f[V]class\f[R].
Messages tagged with \f[V]gc\f[R], \f[V]meta\f[R], and \f[V]class\f[R]
aren\[aq]t be logged as \f[V]class*\f[R] is set to off.
The default configuration for all other messages at level
\f[V]warning\f[R] is in effect except for those that include
\f[V]class\f[R].
.TP
\f[V]-Xlog:gc+meta=trace\f[R]
Logs messages tagged with exactly the \f[V]gc\f[R] and \f[V]meta\f[R]
tags using the \f[V]trace\f[R] level to \f[V]stdout\f[R].
The default configuration for all other messages at level
\f[V]warning\f[R] is still be in effect.
.TP
\f[V]-Xlog:gc+class+heap*=debug,meta*=warning,threads*=off\f[R]
Logs messages tagged with at least \f[V]gc\f[R], \f[V]class\f[R], and
\f[V]heap\f[R] tags using the \f[V]trace\f[R] level to \f[V]stdout\f[R]
but only log messages tagged with \f[V]meta\f[R] with level.
The default configuration for all other messages at the level
\f[V]warning\f[R] is in effect except for those that include
\f[V]threads\f[R].
.SH VALIDATE JAVA VIRTUAL MACHINE FLAG ARGUMENTS
.PP
You use values provided to all Java Virtual Machine (JVM) command-line
flags for validation and, if the input value is invalid or out-of-range,
then an appropriate error message is displayed.
.PP
Whether they\[aq]re set ergonomically, in a command line, by an input
tool, or through the APIs (for example, classes contained in the package
\f[V]java.lang.management\f[R]) the values provided to all Java Virtual
Machine (JVM) command-line flags are validated.
Ergonomics are described in Java Platform, Standard Edition HotSpot
Virtual Machine Garbage Collection Tuning Guide.
.PP
Range and constraints are validated either when all flags have their
values set during JVM initialization or a flag\[aq]s value is changed
during runtime (for example using the \f[V]jcmd\f[R] tool).
The JVM is terminated if a value violates either the range or constraint
check and an appropriate error message is printed on the error stream.
.PP
For example, if a flag violates a range or a constraint check, then the
JVM exits with an error:
.IP
.nf
\f[CB]
java -XX:AllocatePrefetchStyle=5 -version
intx AllocatePrefetchStyle=5 is outside the allowed range [ 0 ... 3 ]
Improperly specified VM option \[aq]AllocatePrefetchStyle=5\[aq]
Error: Could not create the Java Virtual Machine.
Error: A fatal exception has occurred. Program will exit.
\f[R]
.fi
.PP
The flag \f[V]-XX:+PrintFlagsRanges\f[R] prints the range of all the
flags.
This flag allows automatic testing of the flags by the values provided
by the ranges.
For the flags that have the ranges specified, the type, name, and the
actual range is printed in the output.
.PP
For example,
.IP
.nf
\f[CB]
intx   ThreadStackSize [ 0 ... 9007199254740987 ] {pd product}
\f[R]
.fi
.PP
For the flags that don\[aq]t have the range specified, the values
aren\[aq]t displayed in the print out.
For example:
.IP
.nf
\f[CB]
size_t NewSize         [   ...                  ] {product}
\f[R]
.fi
.PP
This helps to identify the flags that need to be implemented.
The automatic testing framework can skip those flags that don\[aq]t have
values and aren\[aq]t implemented.
.SH LARGE PAGES
.PP
You use large pages, also known as huge pages, as memory pages that are
significantly larger than the standard memory page size (which varies
depending on the processor and operating system).
Large pages optimize processor Translation-Lookaside Buffers.
.PP
A Translation-Lookaside Buffer (TLB) is a page translation cache that
holds the most-recently used virtual-to-physical address translations.
A TLB is a scarce system resource.
A TLB miss can be costly because the processor must then read from the
hierarchical page table, which may require multiple memory accesses.
By using a larger memory page size, a single TLB entry can represent a
larger memory range.
This results in less pressure on a TLB, and memory-intensive
applications may have better performance.
.PP
However, using large pages can negatively affect system performance.
For example, when a large amount of memory is pinned by an application,
it may create a shortage of regular memory and cause excessive paging in
other applications and slow down the entire system.
Also, a system that has been up for a long time could produce excessive
fragmentation, which could make it impossible to reserve enough large
page memory.
When this happens, either the OS or JVM reverts to using regular pages.
.PP
Linux and Windows support large pages.
.SS Large Pages Support for Linux
.PP
Linux supports large pages since version 2.6.
To check if your environment supports large pages, try the following:
.IP
.nf
\f[CB]
# cat /proc/meminfo | grep Huge
HugePages_Total: 0
HugePages_Free: 0
\&...
Hugepagesize: 2048 kB
\f[R]
.fi
.PP
If the output contains items prefixed with \[dq]Huge\[dq], then your
system supports large pages.
The values may vary depending on environment.
The \f[V]Hugepagesize\f[R] field shows the default large page size in
your environment, and the other fields show details for large pages of
this size.
Newer kernels have support for multiple large page sizes.
To list the supported page sizes, run this:
.IP
.nf
\f[CB]
# ls /sys/kernel/mm/hugepages/
hugepages-1048576kB  hugepages-2048kB
\f[R]
.fi
.PP
The above environment supports 2 MB and 1 GB large pages, but they need
to be configured so that the JVM can use them.
When using large pages and not enabling transparent huge pages (option
\f[V]-XX:+UseTransparentHugePages\f[R]), the number of large pages must
be pre-allocated.
For example, to enable 8 GB of memory to be backed by 2 MB large pages,
login as \f[V]root\f[R] and run:
.RS
.PP
\f[V]# echo 4096 > /sys/kernel/mm/hugepages/hugepages-2048kB/nr_hugepages\f[R]
.RE
.PP
It is always recommended to check the value of \f[V]nr_hugepages\f[R]
after the request to make sure the kernel was able to allocate the
requested number of large pages.
.RS
.PP
\f[B]Note:\f[R] The values contained in \f[V]/proc\f[R] and
\f[V]/sys\f[R] reset after you reboot your system, so may want to set
them in an initialization script (for example, \f[V]rc.local\f[R] or
\f[V]sysctl.conf\f[R]).
.RE
.PP
If you configure the OS kernel parameters to enable use of large pages,
the Java processes may allocate large pages for the Java heap as well as
other internal areas, for example:
.IP \[bu] 2
Code cache
.IP \[bu] 2
Marking bitmaps
.PP
Consequently, if you configure the \f[V]nr_hugepages\f[R] parameter to
the size of the Java heap, then the JVM can still fail to allocate the
heap using large pages because other areas such as the code cache might
already have used some of the configured large pages.
.SS Large Pages Support for Windows
.PP
To use large pages support on Windows, the administrator must first
assign additional privileges to the user who is running the application:
.IP "1." 3
Select \f[B]Control Panel\f[R], \f[B]Administrative Tools\f[R], and then
\f[B]Local Security Policy\f[R].
.IP "2." 3
Select \f[B]Local Policies\f[R] and then \f[B]User Rights
Assignment\f[R].
.IP "3." 3
Double-click \f[B]Lock pages in memory\f[R], then add users and/or
groups.
.IP "4." 3
Reboot your system.
.PP
Note that these steps are required even if it\[aq]s the administrator
who\[aq]s running the application, because administrators by default
don\[aq]t have the privilege to lock pages in memory.
.SH APPLICATION CLASS DATA SHARING
.PP
Application Class Data Sharing (AppCDS) stores classes used by your
applications in an archive file.
Since these classes are stored in a format that can be loaded very
quickly (compared to classes stored in a JAR file), AppCDS can improve
the start-up time of your applications.
In addition, AppCDS can reduce the runtime memory footprint by sharing
parts of these classes across multiple processes.
.PP
Classes in the CDS archive are stored in an optimized format that\[aq]s
about 2 to 5 times larger than classes stored in JAR files or the JDK
runtime image.
Therefore, it\[aq]s a good idea to archive only those classes that are
actually used by your application.
These usually are just a small portion of all available classes.
For example, your application may use only a few APIs provided by a
large library.
.SS Using CDS Archives
.PP
By default, in most JDK distributions, unless \f[V]-Xshare:off\f[R] is
specified, the JVM starts up with a default CDS archive, which is
usually located in \f[V]JAVA_HOME/lib/server/classes.jsa\f[R] (or
\f[V]JAVA_HOME\[rs]bin\[rs]server\[rs]classes.jsa\f[R] on Windows).
This archive contains about 1300 core library classes that are used by
most applications.
.PP
To use CDS for the exact set of classes used by your application, you
can use the \f[V]-XX:SharedArchiveFile\f[R] option, which has the
general form:
.RS
.PP
\f[V]-XX:SharedArchiveFile=<static_archive>:<dynamic_archive>\f[R]
.RE
.IP \[bu] 2
The \f[V]<static_archive>\f[R] overrides the default CDS archive.
.IP \[bu] 2
The \f[V]<dynamic_archive>\f[R] provides additional classes that can be
loaded on top of those in the \f[V]<static_archive>\f[R].
.IP \[bu] 2
On Windows, the above path delimiter \f[V]:\f[R] should be replaced with
\f[V];\f[R]
.PP
(The names \[dq]static\[dq] and \[dq]dynamic\[dq] are used for
historical reasons.
The only significance is that the \[dq]static\[dq] archive is loaded
first and the \[dq]dynamic\[dq] archive is loaded second).
.PP
The JVM can use up to two archives.
To use only a single \f[V]<static_archive>\f[R], you can omit the
\f[V]<dynamic_archive>\f[R] portion:
.RS
.PP
\f[V]-XX:SharedArchiveFile=<static_archive>\f[R]
.RE
.PP
For convenience, the \f[V]<dynamic_archive>\f[R] records the location of
the \f[V]<static_archive>\f[R].
Therefore, you can omit the \f[V]<static_archive>\f[R] by saying only:
.RS
.PP
\f[V]-XX:SharedArchiveFile=<dynamic_archive>\f[R]
.RE
.SS Manually Creating CDS Archives
.PP
CDS archives can be created manually using several methods:
.IP \[bu] 2
\f[V]-Xshare:dump\f[R]
.IP \[bu] 2
\f[V]-XX:ArchiveClassesAtExit\f[R]
.IP \[bu] 2
\f[V]jcmd VM.cds\f[R]
.PP
One common operation in all these methods is a \[dq]trial run\[dq],
where you run the application once to determine the classes that should
be stored in the archive.
.SS Creating a Static CDS Archive File with -Xshare:dump
.PP
The following steps create a static CDS archive file that contains all
the classes used by the \f[V]test.Hello\f[R] application.
.IP "1." 3
Create a list of all classes used by the \f[V]test.Hello\f[R]
application.
The following command creates a file named \f[V]hello.classlist\f[R]
that contains a list of all classes used by this application:
.RS 4
.RS
.PP
\f[V]java -Xshare:off -XX:DumpLoadedClassList=hello.classlist -cp hello.jar test.Hello\f[R]
.RE
.PP
The classpath specified by the \f[V]-cp\f[R] parameter must contain only
JAR files.
.RE
.IP "2." 3
Create a static archive, named \f[V]hello.jsa\f[R], that contains all
the classes in \f[V]hello.classlist\f[R]:
.RS 4
.RS
.PP
\f[V]java -Xshare:dump -XX:SharedArchiveFile=hello.jsa -XX:SharedClassListFile=hello.classlist -cp hello.jar\f[R]
.RE
.RE
.IP "3." 3
Run the application \f[V]test.Hello\f[R] with the archive
\f[V]hello.jsa\f[R]:
.RS 4
.RS
.PP
\f[V]java -XX:SharedArchiveFile=hello.jsa -cp hello.jar test.Hello\f[R]
.RE
.RE
.IP "4." 3
\f[B]Optional\f[R] Verify that the \f[V]test.Hello\f[R] application is
using the class contained in the \f[V]hello.jsa\f[R] shared archive:
.RS 4
.RS
.PP
\f[V]java -XX:SharedArchiveFile=hello.jsa -cp hello.jar -Xlog:class+load test.Hello\f[R]
.RE
.PP
The output of this command should contain the following text:
.RS
.PP
\f[V][info][class,load] test.Hello source: shared objects file\f[R]
.RE
.RE
.PP
By default, when the \f[V]-Xshare:dump\f[R] option is used, the JVM runs
in interpreter-only mode (as if the \f[V]-Xint\f[R] option were
specified).
This is required for generating deterministic output in the shared
archive file.
I.e., the exact same archive will be generated, bit-for-bit, every time
you dump it.
However, if deterministic output is not needed, and you have a large
classlist, you can explicitly add \f[V]-Xmixed\f[R] to the command-line
to enable the JIT compiler.
This will speed up the archive creation.
.SS Creating a Dynamic CDS Archive File with -XX:ArchiveClassesAtExit
.PP
Advantages of dynamic CDS archives are:
.IP \[bu] 2
They usually use less disk space, since they don\[aq]t need to store the
classes that are already in the static archive.
.IP \[bu] 2
They are created with one fewer step than the comparable static archive.
.PP
The following steps create a dynamic CDS archive file that contains the
classes that are used by the \f[V]test.Hello\f[R] application, excluding
those that are already in the default CDS archive.
.IP "1." 3
Create a dynamic CDS archive, named \f[V]hello.jsa\f[R], that contains
all the classes in \f[V]hello.jar\f[R] loaded by the application
\f[V]test.Hello\f[R]:
.RS 4
.RS
.PP
\f[V]java -XX:ArchiveClassesAtExit=hello.jsa -cp hello.jar Hello\f[R]
.RE
.RE
.IP "2." 3
Run the application \f[V]test.Hello\f[R] with the shared archive
\f[V]hello.jsa\f[R]:
.RS 4
.RS
.PP
\f[V]java -XX:SharedArchiveFile=hello.jsa -cp hello.jar test.Hello\f[R]
.RE
.RE
.IP "3." 3
\f[B]Optional\f[R] Repeat step 4 of the previous section to verify that
the \f[V]test.Hello\f[R] application is using the class contained in the
\f[V]hello.jsa\f[R] shared archive.
.PP
It\[aq]s also possible to create a dynamic CDS archive with a
non-default static CDS archive.
E.g.,
.RS
.PP
\f[V]java -XX:SharedArchiveFile=base.jsa -XX:ArchiveClassesAtExit=hello.jsa -cp hello.jar Hello\f[R]
.RE
.PP
To run the application using this dynamic CDS archive:
.RS
.PP
\f[V]java -XX:SharedArchiveFile=base.jsa:hello.jsa -cp hello.jar Hello\f[R]
.RE
.PP
(On Windows, the above path delimiter \f[V]:\f[R] should be replaced
with \f[V];\f[R])
.PP
As mention above, the name of the static archive can be skipped:
.RS
.PP
\f[V]java -XX:SharedArchiveFile=hello.jsa -cp hello.jar Hello\f[R]
.RE
.SS Creating CDS Archive Files with jcmd
.PP
The previous two sections require you to modify the application\[aq]s
start-up script in order to create a CDS archive.
Sometimes this could be difficult, for example, if the application\[aq]s
class path is set up by complex routines.
.PP
The \f[V]jcmd VM.cds\f[R] command provides a less intrusive way for
creating a CDS archive by connecting to a running JVM process.
You can create either a static:
.RS
.PP
\f[V]jcmd <pid> VM.cds static_dump my_static_archive.jsa\f[R]
.RE
.PP
or a dynamic archive:
.RS
.PP
\f[V]jcmd <pid> VM.cds dynamic_dump my_dynamic_archive.jsa\f[R]
.RE
.PP
To use the resulting archive file in a subsequent run of the application
without modifying the application\[aq]s start-up script, you can use the
following technique:
.RS
.PP
\f[V]env JAVA_TOOL_OPTIONS=-XX:SharedArchiveFile=my_static_archive.jsa bash app_start.sh\f[R]
.RE
.PP
Note: to use \f[V]jcmd <pid> VM.cds dynamic_dump\f[R], the JVM process
identified by \f[V]<pid>\f[R] must be started with
\f[V]-XX:+RecordDynamicDumpInfo\f[R], which can also be passed to the
application start-up script with the same technique:
.RS
.PP
\f[V]env JAVA_TOOL_OPTIONS=-XX:+RecordDynamicDumpInfo bash app_start.sh\f[R]
.RE
.SS Creating Dynamic CDS Archive File with -XX:+AutoCreateSharedArchive
.PP
\f[V]-XX:+AutoCreateSharedArchive\f[R] is a more convenient way of
creating/using CDS archives.
Unlike the methods of manual CDS archive creation described in the
previous section, with \f[V]-XX:+AutoCreateSharedArchive\f[R], it\[aq]s
no longer necessary to have a separate trial run.
Instead, you can always run the application with the same command-line
and enjoy the benefits of CDS automatically.
.RS
.PP
\f[V]java -XX:+AutoCreateSharedArchive -XX:SharedArchiveFile=hello.jsa -cp hello.jar Hello\f[R]
.RE
.PP
If the specified archive file exists and was created by the same version
of the JDK, then it will be loaded as a dynamic archive; otherwise it is
ignored at VM startup.
.PP
At VM exit, if the specified archive file does not exist, it will be
created.
If it exists but was created with a different (but post JDK 19) version
of the JDK, then it will be replaced.
In both cases the archive will be ready to be loaded the next time the
JVM is launched with the same command line.
.PP
If the specified archive file exists but was created by a JDK version
prior to JDK 19, then it will be ignored: neither loaded at startup, nor
replaced at exit.
.PP
Developers should note that the contents of the CDS archive file are
specific to each build of the JDK.
Therefore, if you switch to a different JDK build,
\f[V]-XX:+AutoCreateSharedArchive\f[R] will automatically recreate the
archive to match the JDK.
If you intend to use this feature with an existing archive, you should
make sure that the archive is created by at least version 19 of the JDK.
.SS Restrictions on Class Path and Module Path
.IP \[bu] 2
Neither the class path (\f[V]-classpath\f[R] and
\f[V]-Xbootclasspath/a\f[R]) nor the module path
(\f[V]--module-path\f[R]) can contain non-empty directories.
.IP \[bu] 2
Only modular JAR files are supported in \f[V]--module-path\f[R].
Exploded modules are not supported.
.IP \[bu] 2
The class path used at archive creation time must be the same as (or a
prefix of) the class path used at run time.
(There\[aq]s no such requirement for the module path.)
.IP \[bu] 2
The CDS archive cannot be loaded if any JAR files in the class path or
module path are modified after the archive is generated.
.SS Module related options
.PP
The following module related options are supported by CDS:
\f[V]--module-path\f[R], \f[V]--module\f[R], \f[V]--add-modules\f[R],
and \f[V]--enable-native-access\f[R].
.PP
The values for these options (if specified), should be identical when
creating and using the CDS archive.
Otherwise, if there is a mismatch of any of these options, the CDS
archive may be partially or completely disabled, leading to lower
performance.
.IP \[bu] 2
If the \f[V]AOTClassLinking\f[R] option (see below) \f[I]was\f[R]
enabled during CDS archive creation, the CDS archive cannot be used, and
the following error message is printed:
.RS 2
.PP
\f[V]CDS archive has aot-linked classes. It cannot be used when archived full module graph is not used\f[R]
.RE
.IP \[bu] 2
If the \f[V]AOTClassLinking\f[R] option \f[I]was not\f[R] enabled during
CDS archive creation, the CDS archive can be used, but the \[dq]archived
module graph\[dq] feature will be disabled.
This can lead to increased start-up time.
.PP
To diagnose problems with the above options, you can add
\f[V]-Xlog:cds\f[R] to the application\[aq]s VM arguments.
For example, if \f[V]--add-modules jdk.jconcole\f[R] was specified
during archive creation and \f[V]--add-modules jdk.incubator.vector\f[R]
is specified during runtime, the following messages will be logged:
.PP
\f[V]Mismatched values for property jdk.module.addmods\f[R]
.PP
\f[V]runtime jdk.incubator.vector dump time jdk.jconsole\f[R]
.PP
\f[V]subgraph jdk.internal.module.ArchivedBootLayer cannot be used because full module graph is disabled\f[R]
.PP
If any of the VM options \f[V]--upgrade-module-path\f[R],
\f[V]--patch-module\f[R] or \f[V]--limit-modules\f[R] are specified, CDS
is disabled.
This means that the JVM will execute without loading any CDS archives.
In addition, if you try to create a CDS archive with any of these 3
options specified, the JVM will report an error.
.SH AHEAD-OF-TIME CACHE
.PP
The JDK supports ahead-of-time (AOT) optimizations that can be performed
before an application is executed.
One example is Class Data Sharing (CDS), as described above, that parses
classes ahead of time.
AOT optimizations can improve the start-up and warm-up performance of
Java applications.
.PP
The Ahead-of-Time Cache (AOT cache) is a container introduced in JDK 24
for storing artifacts produced by AOT optimizations.
The AOT cache currently contains Java classes and heap objects.
In future JDK releases, the AOT cache may contain additional artifacts,
such as execution profiles and compiled methods.
.PP
An AOT cache is specific to a combination of the following:
.IP \[bu] 2
A particular application (as expressed by \f[V]-classpath\f[R],
\f[V]-jar\f[R], or \f[V]--module-path\f[R].)
.IP \[bu] 2
A particular JDK release.
.IP \[bu] 2
A particular OS and CPU architecture.
.PP
If any of the above changes, you must recreate the AOT cache.
.PP
The deployment of the AOT cache is divided into three phases:
.IP \[bu] 2
\f[B]Training:\f[R] We execute the application with a representative
work-load to gather statistical data that tell us what artifacts should
be included into the AOT cache.
The data are saved in an \f[I]AOT Configuration\f[R] file.
.IP \[bu] 2
\f[B]Assembly:\f[R] We use the AOT Configuration file to produce an AOT
cache.
.IP \[bu] 2
\f[B]Production:\f[R] We execute the application with the AOT cache for
better start-up and warm-up performance.
.PP
The AOT cache can be used with the following command-line options:
.TP
\f[V]-XX:AOTCache:=\f[R]\f[I]cachefile\f[R]
Specifies the location of the AOT cache.
The standard extension for \f[I]cachefile\f[R] is \f[V].aot\f[R].
If \f[V]-XX:AOTCache\f[R] is specified but \f[V]-XX:AOTMode\f[R] is not
specified, then \f[V]AOTMode\f[R] will be given the value of
\f[V]auto\f[R].
.TP
\f[V]-XX:AOTConfiguration:=\f[R]\f[I]configfile\f[R]
Specifies the AOT Configuration file for the JVM to write to or read
from.
This option can be used only with \f[V]-XX:AOTMode=record\f[R] and
\f[V]-XX:AOTMode=create\f[R].
The standard extension for \f[I]configfile\f[R] is \f[V].aotconfig\f[R].
.TP
\f[V]-XX:+AOTMode:=\f[R]\f[I]mode\f[R]
\f[I]mode\f[R] must be one of the following: \f[V]off\f[R],
\f[V]record\f[R], \f[V]create\f[R], \f[V]auto\f[R], or \f[V]on\f[R].
.IP \[bu] 2
\f[V]off\f[R]: no AOT cache is used.
.IP \[bu] 2
\f[V]record\f[R]: Execute the application in the Training phase.
\f[V]-XX:AOTConfiguration=\f[R]\f[I]configfile\f[R] must be specified.
The JVM gathers statistical data and stores them into
\f[I]configfile\f[R].
.IP \[bu] 2
\f[V]create\f[R]: Perform the Assembly phase.
\f[V]-XX:AOTConfiguration=\f[R]\f[I]configfile\f[R] and
\f[V]-XX:AOTCache=\f[R]\f[I]cachefile\f[R] must be specified.
The JVM reads the statistical data from \f[I]configfile\f[R] and writes
the optimization artifacts into \f[I]cachefile\f[R].
Note that the application itself is not executed in this phase.
.IP \[bu] 2
\f[V]auto\f[R] or \f[V]on\f[R]: These modes should be used in the
Production phase.
If \f[V]-XX:AOTCache=\f[R]\f[I]cachefile\f[R] is specified, the JVM
tries to load \f[I]cachefile\f[R] as the AOT cache.
Otherwise, the JVM tries to load a \f[I]default CDS archive\f[R] from
the JDK installation directory as the AOT cache.
.RS 2
.PP
The loading of an AOT cache can fail for a number of reasons:
.IP \[bu] 2
You are trying to use the AOT cache with an incompatible application,
JDK release, or OS/CPU.
.IP \[bu] 2
The specified \f[I]cachefile\f[R] does not exist or is not accessible.
.IP \[bu] 2
Incompatible JVM options are used (for example, certain JVMTI options).
.RS 2
.PP
Since the AOT cache is an optimization feature, there\[aq]s no guarantee
that it will be compatible with all possible JVM options.
See \f[B]JEP 483\f[R] [https://openjdk.org/jeps/483], section
\f[B]Consistency of training and subsequent runs\f[R] for a
representative list of scenarios that may be incompatible with the AOT
cache for JDK 24.
.PP
These scenarios usually involve arbitrary modification of classes for
diagnostic purposes and are typically not relevant for production
environments.
.RE
.PP
When the AOT cache fails to load:
.IP \[bu] 2
If \f[V]AOTMode\f[R] is \f[V]auto\f[R], the JVM will continue execution
without using the AOT cache.
This is the recommended mode for production environments, especially
when you may not have complete control of the command-line (e.g., your
application\[aq]s launch script may allow users to inject options to the
command-line).
This allows your application to function correctly, although sometimes
it may not benefit from the AOT cache.
.IP \[bu] 2
If \f[V]AOTMode\f[R] is \f[V]on\f[R], the JVM will print an error
message and exit immediately.
This mode should be used only as a \[dq]fail-fast\[dq] debugging aid to
check if your command-line options are compatible with the AOT cache.
An alternative is to run your application with
\f[V]-XX:AOTMode=auto -Xlog:cds\f[R] to see if the AOT cache can be used
or not.
.RE
.TP
\f[V]-XX:+AOTClassLinking\f[R]
If this option is enabled, the JVM will perform more advanced
optimizations (such as ahead-of-time resolution of invokedynamic
instructions) when creating the AOT cache.
As a result, the application will see further improvements in start-up
and warm-up performance.
However, an AOT cache created with this option cannot be used when
certain command-line parameters are specified in the Production phase.
Please see \f[B]JEP 483\f[R] [https://openjdk.org/jeps/483] for a
detailed discussion of \f[V]-XX:+AOTClassLinking\f[R] and its
restrictions.
.RS
.PP
When \f[V]-XX:AOTMode\f[R] \f[I]is used\f[R] in the command-line,
\f[V]AOTClassLinking\f[R] is automatically enabled.
To disable it, you must explicitly pass the
\f[V]-XX:-AOTClassLinking\f[R] option.
.PP
When \f[V]-XX:AOTMode\f[R] \f[I]is not used\f[R] in the command-line,
\f[V]AOTClassLinking\f[R] is disabled by default to provide full
compatibility with traditional CDS options such as \[ga]-Xshare:dump.
.RE
.SH PERFORMANCE TUNING EXAMPLES
.PP
You can use the Java advanced runtime options to optimize the
performance of your applications.
.SS Tuning for Higher Throughput
.PP
Use the following commands and advanced options to achieve higher
throughput performance for your application:
.RS
.PP
\f[V]java -server -XX:+UseParallelGC -XX:+UseLargePages -Xmn10g  -Xms26g -Xmx26g\f[R]
.RE
.SS Tuning for Lower Response Time
.PP
Use the following commands and advanced options to achieve lower
response times for your application:
.RS
.PP
\f[V]java -XX:+UseG1GC -XX:MaxGCPauseMillis=100\f[R]
.RE
.SS Keeping the Java Heap Small and Reducing the Dynamic Footprint of Embedded Applications
.PP
Use the following advanced runtime options to keep the Java heap small
and reduce the dynamic footprint of embedded applications:
.RS
.PP
\f[V]-XX:MaxHeapFreeRatio=10 -XX:MinHeapFreeRatio=5\f[R]
.RE
.RS
.PP
\f[B]Note:\f[R] The defaults for these two options are 70% and 40%
respectively.
Because performance sacrifices can occur when using these small
settings, you should optimize for a small footprint by reducing these
settings as much as possible without introducing unacceptable
performance degradation.
.RE
.SH EXIT STATUS
.PP
The following exit values are typically returned by the launcher when
the launcher is called with the wrong arguments, serious errors, or
exceptions thrown by the JVM.
However, a Java application may choose to return any value by using the
API call \f[V]System.exit(exitValue)\f[R].
The values are:
.IP \[bu] 2
\f[V]0\f[R]: Successful completion
.IP \[bu] 2
\f[V]>0\f[R]: An error occurred
