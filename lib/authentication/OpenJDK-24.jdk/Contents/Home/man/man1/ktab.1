.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "KTAB" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
ktab - manage the principal names and service keys stored in a local key
table
.SH SYNOPSIS
.PP
\f[V]ktab\f[R] [\f[I]commands\f[R]] [\f[I]options\f[R]]
.TP
[\f[I]commands\f[R]] [\f[I]options\f[R]]
Lists the keytab name and entries, adds new key entries to the keytab,
deletes existing key entries, and displays instructions.
See \f[B]Commands and Options\f[R].
.SH DESCRIPTION
.PP
The \f[V]ktab\f[R] enables the user to manage the principal names and
service keys stored in a local key table.
Principal and key pairs listed in the keytab enable services running on
a host to authenticate themselves to the Key Distribution Center (KDC).
.PP
Before configuring a server to use Kerberos, you must set up a keytab on
the host running the server.
Note that any updates made to the keytab using the \f[V]ktab\f[R] tool
don\[aq]t affect the Kerberos database.
.PP
A \f[I]keytab\f[R] is a host\[aq]s copy of its own keylist, which is
analogous to a user\[aq]s password.
An application server that needs to authenticate itself to the Key
Distribution Center (KDC) must have a keytab which contains its own
principal and key.
If you change the keys in the keytab, you must also make the
corresponding changes to the Kerberos database.
The \f[V]ktab\f[R] tool enables you to list, add, update or delete
principal names and key pairs in the key table.
None of these operations affect the Kerberos database.
.SH SECURITY ALERT
.PP
Don\[aq]t specify your password on the command line.
Doing so can be a security risk.
For example, an attacker could discover your password while running the
UNIX \f[V]ps\f[R] command.
.PP
Just as it is important for users to protect their passwords, it is
equally important for hosts to protect their keytabs.
You should always store keytab files on the local disk and make them
readable only by root.
You should never send a keytab file over a network in the clear.
.SH COMMANDS AND OPTIONS
.TP
\f[V]-l\f[R] [\f[V]-e\f[R]] [\f[V]-t\f[R]]
Lists the keytab name and entries.
When \f[V]-e\f[R] is specified, the encryption type for each entry is
displayed.
When \f[V]-t\f[R] is specified, the timestamp for each entry is
displayed.
.TP
\f[V]-a\f[R] \f[I]principal_name\f[R] [\f[I]password\f[R]] [\f[V]-n\f[R] \f[I]kvno\f[R]] [\f[V]-s\f[R] \f[I]salt\f[R] | \f[V]-f\f[R]] [\f[V]-append\f[R]]
Adds new key entries to the keytab for the given principal name with an
optional \f[I]password\f[R].
If a \f[I]kvno\f[R] is specified, new keys\[aq] Key Version Numbers
equal to the value, otherwise, automatically incrementing the Key
Version Numbers.
If \f[I]salt\f[R] is specified, it will be used instead of the default
salt.
If \f[V]-f\f[R] is specified, the KDC will be contacted to fetch the
salt.
If \f[V]-append\f[R] is specified, new keys are appended to the keytab,
otherwise, old keys for the same principal are removed.
.RS
.PP
No changes are made to the Kerberos database.
\f[B]Don\[aq]t specify the password on the command line or in a
script.\f[R] This tool will prompt for a password if it isn\[aq]t
specified.
.RE
.TP
\f[V]-d\f[R] \f[I]principal_name\f[R] [\f[V]-f\f[R]] [\f[V]-e\f[R] \f[I]etype\f[R]] [\f[I]kvno\f[R] | \f[V]all\f[R]| \f[V]old\f[R]]
Deletes key entries from the keytab for the specified principal.
No changes are made to the Kerberos database.
.RS
.IP \[bu] 2
If \f[I]kvno\f[R] is specified, the tool deletes keys whose Key Version
Numbers match kvno.
If \f[V]all\f[R] is specified, delete all keys.
.IP \[bu] 2
If \f[V]old\f[R] is specified, the tool deletes all keys except those
with the highest \f[I]kvno\f[R].
The default action is \f[V]all\f[R].
.IP \[bu] 2
If \f[I]etype\f[R] is specified, the tool only deletes keys of this
encryption type.
\f[I]etype\f[R] should be specified as the numeric value \f[I]etype\f[R]
defined in RFC 3961, section 8.
A prompt to confirm the deletion is displayed unless \f[V]-f\f[R] is
specified.
.PP
When \f[I]etype\f[R] is provided, only the entry matching this
encryption type is deleted.
Otherwise, all entries are deleted.
.RE
.TP
\f[V]-help\f[R]
Displays instructions.
.SH COMMON OPTIONS
.PP
This option can be used with the \f[V]-l\f[R], \f[V]-a\f[R] or
\f[V]-d\f[R] commands.
.TP
\f[V]-k\f[R] \f[I]keytab name\f[R]
Specifies the keytab name and path with the \f[V]FILE:\f[R] prefix.
.SH EXAMPLES
.IP \[bu] 2
Lists all the entries in the default keytable
.RS 2
.RS
.PP
\f[V]ktab -l\f[R]
.RE
.RE
.IP \[bu] 2
Adds a new principal to the key table (note that you will be prompted
for your password)
.RS 2
.RS
.PP
\f[V]ktab -a duke\[at]example.com\f[R]
.RE
.RE
.IP \[bu] 2
Deletes a principal from the key table
.RS 2
.RS
.PP
\f[V]ktab -d duke\[at]example.com\f[R]
.RE
.RE
