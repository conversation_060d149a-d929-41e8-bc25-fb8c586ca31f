.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JAVADOC" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
javadoc - generate HTML pages of API documentation from Java source
files
.SH SYNOPSIS
.PP
\f[V]javadoc\f[R] [\f[I]options\f[R]] [\f[I]packagenames\f[R]]
[\f[I]sourcefiles\f[R]] [\f[V]\[at]\f[R]\f[I]files\f[R]]
.TP
\f[I]options\f[R]
Specifies command-line options, separated by spaces.
See \f[B]Standard \f[VB]javadoc\f[B] Options\f[R], \f[B]Extra
\f[VB]javadoc\f[B] Options\f[R], \f[B]Standard Options for the Standard
Doclet\f[R], and \f[B]Extra Options for the Standard Doclet\f[R].
.TP
\f[I]packagenames\f[R]
Specifies names of packages that you want to document, separated by
spaces, for example \f[V]java.lang java.lang.reflect java.awt\f[R].
If you want to also document the subpackages, then use the
\f[V]-subpackages\f[R] option to specify the packages.
.RS
.PP
By default, \f[V]javadoc\f[R] looks for the specified packages in the
current directory and subdirectories.
Use the \f[V]-sourcepath\f[R] option to specify the list of directories
where to look for packages.
.RE
.TP
\f[I]sourcefiles\f[R]
Specifies names of Java source files that you want to document,
separated by spaces, for example
\f[V]Class.java Object.java Button.java\f[R].
By default, \f[V]javadoc\f[R] looks for the specified classes in the
current directory.
However, you can specify the full path to the class file and use
wildcard characters, for example
\f[V]/home/<USER>/java/awt/Graphics*.java\f[R].
You can also specify the path relative to the current directory.
.TP
\f[V]\[at]\f[R]\f[I]files\f[R]
Specifies names of files that contain a list of \f[V]javadoc\f[R] tool
options, package names, and source file names in any order.
.SH DESCRIPTION
.PP
The \f[V]javadoc\f[R] tool parses the declarations and documentation
comments in a set of Java source files and processes them using a
pluggable back-end called a \f[B]\f[BI]doclet\f[B]\f[R].
.PP
The \f[B]\f[BI]standard doclet\f[B]\f[R] is the one that is used by
default, and can produce corresponding HTML pages that describe the
public and protected classes, nested and implicitly declared classes
(but not anonymous inner classes), interfaces, constructors, methods,
and fields.
The standard doclet interprets the content of documentation comments
according to the \f[B]JavaDoc Documentation Comment Specification for
the Standard Doclet\f[R].
Custom tags in documentation comments are supported by means of
\f[B]taglets\f[R].
.PP
You can use the \f[V]javadoc\f[R] tool and the standard doclet to
generate the API documentation or the implementation documentation for a
set of source files.
.PP
You can run the \f[V]javadoc\f[R] tool on entire packages, individual
source files, or both.
When documenting entire packages, you can use the \f[V]-subpackages\f[R]
option either to recursively traverse a directory and its
subdirectories, or to pass in an explicit list of package names.
When you document individual source files, pass in a list of Java source
file names.
.SS Documentation Comments
.PP
The \f[V]javadoc\f[R] tool uses the documentation comment, if any, that
immediately precedes the beginning of the declaration, whether that is
an annotation, modifier, or the name being declared.
If there are multiple documentation comments before the declaration,
only the last one (closest to the declaration) will be used.
If there are any documentation comments after the beginning of the
declaration, they will be ignored.
To check for any extra or misplaced documentation comments, compile your
source code with the \f[V]javac\f[R] option \f[V]-Xlint\f[R], or more
specifically, \f[V]-Xlint:dangling-doc-comments\f[R].
Within a source file, you may suppress any warnings generated by these
options by using
\f[V]\[at]SuppressWarnings(\[dq]dangling-doc-comments\[dq])\f[R] on a
suitable enclosing declaration.
.SS Conformance
.PP
The standard doclet does not validate the content of documentation
comments for conformance, nor does it attempt to correct any errors in
documentation comments.
Anyone running javadoc is advised to be aware of the problems that may
arise when generating non-conformant output or output containing
executable content, such as JavaScript.
The standard doclet does provide the \f[B]DocLint\f[R] feature to help
developers detect common problems in documentation comments; but it is
also recommended to check the generated output with any appropriate
conformance and other checking tools.
.PP
For more details on the conformance requirements for HTML5 documents,
see \f[B]Conformance requirements for authors\f[R]
[https://html.spec.whatwg.org/multipage/introduction.html#conformance-requirements-for-authors]
in the HTML5 Specification.
For more details on security issues related to web pages, see the
\f[B]Open Web Application Security Project (OWASP)\f[R]
[https://www.owasp.org] page.
.SH OPTIONS
.PP
\f[V]javadoc\f[R] supports command-line options for both the main
\f[V]javadoc\f[R] tool and the currently selected doclet.
The standard doclet is used if no other doclet is specified.
.PP
GNU-style options (that is, those beginning with \f[V]--\f[R]) can use
an equal sign (\f[V]=\f[R]) instead of whitespace characters to separate
the name of an option from its value.
.SS Standard \f[V]javadoc\f[R] Options
.PP
The following core \f[V]javadoc\f[R] options are equivalent to
corresponding \f[V]javac\f[R] options.
See \f[I]Standard Options\f[R] in \f[B]javac\f[R] for the detailed
descriptions of using these options:
.IP \[bu] 2
\f[V]--add-modules\f[R]
.IP \[bu] 2
\f[V]-bootclasspath\f[R]
.IP \[bu] 2
\f[V]--class-path\f[R], \f[V]-classpath\f[R], or \f[V]-cp\f[R]
.IP \[bu] 2
\f[V]--disable-line-doc-comments\f[R]
.IP \[bu] 2
\f[V]--enable-preview\f[R]
.IP \[bu] 2
\f[V]-encoding\f[R]
.IP \[bu] 2
\f[V]-extdirs\f[R]
.IP \[bu] 2
\f[V]--limit-modules\f[R]
.IP \[bu] 2
\f[V]--module\f[R]
.IP \[bu] 2
\f[V]--module-path\f[R] or \f[V]-p\f[R]
.IP \[bu] 2
\f[V]--module-source-path\f[R]
.IP \[bu] 2
\f[V]--release\f[R]
.IP \[bu] 2
\f[V]--source\f[R] or \f[V]-source\f[R]
.IP \[bu] 2
\f[V]--source-path\f[R] or \f[V]-sourcepath\f[R]
.IP \[bu] 2
\f[V]--system\f[R]
.IP \[bu] 2
\f[V]--upgrade-module-path\f[R]
.PP
The following options are the core \f[V]javadoc\f[R] options that are
not equivalent to a corresponding \f[V]javac\f[R] option:
.TP
\f[V]-breakiterator\f[R]
Computes the first sentence of the description in a documentation
comment using an instance of \f[V]java.text.BreakIterator\f[R] to detect
\f[I]sentence breaks\f[R].
The rules that are used depend on the \f[B]current locale\f[R]: for
example, for English, a sentence break occurs after a period, question
mark, or exclamation point followed by a space when the next word starts
with a capital letter.
(This is meant to handle most abbreviations, such as \[dq]The serial no.
is valid\[dq], but will not handle \[dq]Mr.
Smith\[dq].)
.RS
.PP
The option is enabled by default if the language of the current locale
is not English.
If the language of the current locale is English, and the
\f[V]-breakiterator\f[R] option is not given, a simple default algorithm
is used, which just looks for a period followed by a space.
.PP
In a traditional \f[V]/**...*/\f[R] comment, the search for the end of
the first sentence is terminated by an HTML block tag, such as
\f[V]<p>\f[R], \f[V]<pre>\f[R], or the tag for a heading.
.PP
In a Markdown \f[V]///\f[R] comment, the search for the end of the first
sentence skips over any characters enclosed in code spans and links, and
is terminated by the end of the initial block, as indicated by a blank
line or the beginning of the next block, such as a list, thematic break,
or an HTML block.
.PP
The first sentence of the description in a documentation comment is used
in summary tables, index pages, and other situations where a short
summary is required.
For more explicit control in any individual documentation comment,
enclose the contents of the first sentence in a
\f[V]{\[at]summary ...}\f[R] tag, or when applicable, in a
\f[V]{\[at]return ...}\f[R] tag.
.RE
.TP
\f[V]-doclet\f[R] \f[I]class\f[R]
Generates output by using an alternate doclet.
Use the fully qualified name.
This doclet defines the content and formats the output.
If the \f[V]-doclet\f[R] option isn\[aq]t used, then the
\f[V]javadoc\f[R] tool uses the standard doclet for generating the
default HTML format.
This class must implement the \f[V]jdk.javadoc.doclet.Doclet\f[R]
interface.
The path to this class is defined by the \f[V]-docletpath\f[R] option.
.TP
\f[V]-docletpath\f[R] \f[I]path\f[R]
Specifies where to find doclet class files (specified with the
\f[V]-doclet\f[R] option) and any JAR files it depends on.
If the starting class file is in a JAR file, then this option specifies
the path to that JAR file.
You can specify an absolute path or a path relative to the current
directory.
If \f[V]path\f[R] contains multiple paths or JAR files, then they should
be separated with a colon (\f[V]:\f[R]) on Linux and macOS, and a
semicolon (\f[V];\f[R]) on Windows.
This option isn\[aq]t necessary when the \f[V]doclet\f[R] starting class
is already in the search path.
.TP
\f[V]-exclude\f[R] \f[I]pkglist\f[R]
Unconditionally, excludes the specified packages and their subpackages
from the list formed by \f[V]-subpackages\f[R].
It excludes those packages even when they would otherwise be included by
some earlier or later \f[V]-subpackages\f[R] option.
.RS
.PP
The following example would include \f[V]java.io\f[R],
\f[V]java.util\f[R], and \f[V]java.math\f[R] (among others), but would
exclude packages rooted at \f[V]java.net\f[R] and \f[V]java.lang\f[R].
Notice that these examples exclude \f[V]java.lang.ref\f[R], which is a
subpackage of \f[V]java.lang\f[R].
Arguments are separated by colons on all operating systems.
.IP \[bu] 2
\f[B]Linux and macOS:\f[R]
.RS 2
.IP
.nf
\f[CB]
javadoc -sourcepath /home/<USER>/src -subpackages java -exclude java.net:java.lang
\f[R]
.fi
.RE
.IP \[bu] 2
\f[B]Windows:\f[R]
.RS 2
.IP
.nf
\f[CB]
javadoc -sourcepath \[rs]user\[rs]src -subpackages java -exclude java.net:java.lang
\f[R]
.fi
.RE
.RE
.TP
\f[V]--expand-requires\f[R] (\f[V]transitive\f[R]|\f[V]all\f[R])
Instructs the javadoc tool to expand the set of modules to be
documented.
By default, only the modules given explicitly on the command line are
documented.
Supports the following values:
.RS
.IP \[bu] 2
\f[V]transitive\f[R]: additionally includes all the required transitive
dependencies of those modules.
.IP \[bu] 2
\f[V]all\f[R]: includes all dependencies.
.RE
.TP
\f[V]--help\f[R], \f[V]-help\f[R], \f[V]-h\f[R], or \f[V]-?\f[R]
Prints a synopsis of the standard options.
.TP
\f[V]--help-extra\f[R] or \f[V]-X\f[R]
Prints a synopsis of the set of extra options.
.TP
\f[V]-J\f[R]\f[I]flag\f[R]
Passes \f[I]flag\f[R] directly to the Java Runtime Environment (JRE)
that runs the \f[V]javadoc\f[R] tool.
For example, if you must ensure that the system sets aside 32 MB of
memory in which to process the generated documentation, then you would
call the \f[V]-Xmx\f[R] option as follows:
\f[V]javadoc -J-Xmx32m -J-Xms32m com.mypackage\f[R].
Be aware that \f[V]-Xms\f[R] is optional because it only sets the size
of initial memory, which is useful when you know the minimum amount of
memory required.
.RS
.PP
There is no space between the \f[V]J\f[R] and the \f[V]flag\f[R].
.PP
Use the \f[V]-version\f[R] option to report the version of the JRE being
used to run the \f[V]javadoc\f[R] tool.
.IP
.nf
\f[CB]
javadoc -J-version
java version \[dq]17\[dq] 2021-09-14 LTS
Java(TM) SE Runtime Environment (build 17+35-LTS-2724)
Java HotSpot(TM) 64-Bit Server VM (build 17+35-LTS-2724, mixed mode, sharing)
\f[R]
.fi
.RE
.TP
\f[V]-locale\f[R] \f[I]name\f[R]
Specifies the locale that the \f[V]javadoc\f[R] tool uses when it
generates documentation.
The argument is the name of the locale, as described in
\f[V]java.util.Locale\f[R] documentation, such as \f[V]en_US\f[R]
(English, United States) or \f[V]en_US_WIN\f[R] (Windows variant).
.RS
.PP
Specifying a locale causes the \f[V]javadoc\f[R] tool to choose the
resource files of that locale for messages such as strings in the
navigation bar, headings for lists and tables, help file contents,
comments in the \f[V]stylesheet.css\f[R] file, and so on.
It also specifies the sorting order for lists sorted alphabetically, and
the sentence separator to determine the end of the first sentence.
The \f[V]-locale\f[R] option doesn\[aq]t determine the locale of the
documentation comment text specified in the source files of the
documented classes.
.RE
.TP
\f[V]-package\f[R]
Shows only package, protected, and public classes and members.
.TP
\f[V]-private\f[R]
Shows all classes and members.
.TP
\f[V]-protected\f[R]
Shows only protected and public classes and members.
This is the default.
.TP
\f[V]-public\f[R]
Shows only the public classes and members.
.TP
\f[V]-quiet\f[R]
Shuts off messages so that only the warnings and errors appear to make
them easier to view.
It also suppresses the \f[V]version\f[R] string.
.TP
\f[V]--show-members\f[R] \f[I]value\f[R]
Specifies which members (fields, methods, or constructors) are
documented, where \f[I]value\f[R] can be any of the following:
.RS
.IP \[bu] 2
\f[V]public\f[R] --- shows only public members
.IP \[bu] 2
\f[V]protected\f[R] --- shows public and protected members; this is the
default
.IP \[bu] 2
\f[V]package\f[R] --- shows public, protected, and package members
.IP \[bu] 2
\f[V]private\f[R] --- shows all members
.RE
.TP
\f[V]--show-module-contents\f[R] \f[I]value\f[R]
Specifies the documentation granularity of module declarations, where
\f[I]value\f[R] can be \f[V]api\f[R] or \f[V]all\f[R].
.TP
\f[V]--show-packages\f[R] \f[I]value\f[R]
Specifies which module packages are documented, where \f[I]value\f[R]
can be \f[V]exported\f[R] or \f[V]all\f[R] packages.
.TP
\f[V]--show-types\f[R] \f[I]value\f[R]
Specifies which types (classes, interfaces, etc.)
are documented, where \f[I]value\f[R] can be any of the following:
.RS
.IP \[bu] 2
\f[V]public\f[R] --- shows only public types
.IP \[bu] 2
\f[V]protected\f[R] --- shows public and protected types; this is the
default
.IP \[bu] 2
\f[V]package\f[R] --- shows public, protected, and package types
.IP \[bu] 2
\f[V]private\f[R] --- shows all types
.RE
.TP
\f[V]-subpackages\f[R] \f[I]subpkglist\f[R]
Generates documentation from source files in the specified packages and
recursively in their subpackages.
This option is useful when adding new subpackages to the source code
because they are automatically included.
Each package argument is any top-level subpackage (such as
\f[V]java\f[R]) or fully qualified package (such as
\f[V]javax.swing\f[R]) that doesn\[aq]t need to contain source files.
Arguments are separated by colons on all operating systems.
Wild cards aren\[aq]t allowed.
Use \f[V]-sourcepath\f[R] to specify where to find the packages.
This option doesn\[aq]t process source files that are in the source tree
but don\[aq]t belong to the packages.
.RS
.PP
For example, the following commands generates documentation for packages
named \f[V]java\f[R] and \f[V]javax.swing\f[R] and all of their
subpackages.
.IP \[bu] 2
\f[B]Linux and macOS:\f[R]
.RS 2
.IP
.nf
\f[CB]
javadoc -d docs -sourcepath /home/<USER>/src -subpackages java:javax.swing
\f[R]
.fi
.RE
.IP \[bu] 2
\f[B]Windows:\f[R]
.RS 2
.IP
.nf
\f[CB]
javadoc -d docs -sourcepath \[rs]user\[rs]src -subpackages java:javax.swing
\f[R]
.fi
.RE
.RE
.TP
\f[V]-verbose\f[R]
Provides more detailed messages while the \f[V]javadoc\f[R] tool runs.
Without the \f[V]-verbose\f[R] option, messages appear for loading the
source files, generating the documentation (one message per source
file), and sorting.
The \f[V]-verbose\f[R] option causes the printing of additional messages
that specify the number of milliseconds to parse each Java source file.
.TP
\f[V]--version\f[R]
Prints version information.
.TP
\f[V]-Werror\f[R]
Reports an error if any warnings occur.
.PP
Note that if a Java source file contains an implicitly declared class,
then that class and its public, protected, and package members will be
documented regardless of the options such as \f[V]--show-types\f[R],
\f[V]--show-members\f[R], \f[V]-private\f[R], \f[V]-protected\f[R],
\f[V]-package\f[R], and \f[V]-public\f[R].
If \f[V]--show-members\f[R] is specified with value \f[V]private\f[R] or
if \f[V]-private\f[R] is used then all private members of an implicitly
declared class will be documented too.
.SS Extra \f[V]javadoc\f[R] Options
.PP
\f[I]Note:\f[R] The additional options for \f[V]javadoc\f[R] are subject
to change without notice.
.PP
The following additional \f[V]javadoc\f[R] options are equivalent to
corresponding \f[V]javac\f[R] options.
See \f[I]Extra Options\f[R] in \f[B]javac\f[R] for the detailed
descriptions of using these options:
.IP \[bu] 2
\f[V]--add-exports\f[R]
.IP \[bu] 2
\f[V]--add-reads\f[R]
.IP \[bu] 2
\f[V]--patch-module\f[R]
.IP \[bu] 2
\f[V]-Xmaxerrs\f[R]
.IP \[bu] 2
\f[V]-Xmaxwarns\f[R]
.SS Standard Options for the Standard Doclet
.PP
The following options are provided by the standard doclet.
.TP
\f[V]--add-script\f[R] \f[I]file\f[R]
Adds \f[I]file\f[R] as an additional JavaScript file to the generated
documentation.
This option can be used one or more times to specify additional script
files.
.RS
.PP
Command-line example:
.RS
.PP
\f[V]javadoc --add-script first_script.js --add-script second_script.js pkg_foo\f[R]
.RE
.RE
.TP
\f[V]--add-stylesheet\f[R] \f[I]file\f[R]
Adds \f[I]file\f[R] as an additional stylesheet file to the generated
documentation.
This option can be used one or more times to specify additional
stylesheets included in the documentation.
.RS
.PP
Command-line example:
.IP
.nf
\f[CB]
javadoc --add-stylesheet new_stylesheet_1.css --add-stylesheet new_stylesheet_2.css pkg_foo
\f[R]
.fi
.RE
.TP
\f[V]--allow-script-in-comments\f[R]
Allow JavaScript in documentation comments, and options whose value is
\f[I]html-code\f[R].
.TP
\f[V]-author\f[R]
Includes the text of any \f[V]author\f[R] tags in the generated
documentation.
.TP
\f[V]-bottom\f[R] \f[I]html-code\f[R]
Specifies the text to be placed at the bottom of each generated page.
The text can contain HTML tags and white space, but when it does, the
text must be enclosed in quotation marks.
Use escape characters for any internal quotation marks within text.
.TP
\f[V]-charset\f[R] \f[I]name\f[R]
Specifies the HTML character set for this document.
The name should be a preferred MIME name as specified in the \f[B]IANA
Registry, Character Sets\f[R]
[http://www.iana.org/assignments/character-sets].
.RS
.PP
For example:
.IP
.nf
\f[CB]
javadoc -charset \[dq]iso-8859-1\[dq] mypackage
\f[R]
.fi
.PP
This command inserts the following line, containing a
\f[B]\f[VB]meta\f[B] element\f[R]
[https://html.spec.whatwg.org/multipage/semantics.html#the-meta-element]
in the head of every generated page:
.IP
.nf
\f[CB]
<meta http-equiv=\[dq]Content-Type\[dq] content=\[dq]text/html; charset=ISO-8859-1\[dq]>
\f[R]
.fi
.RE
.TP
\f[V]-d\f[R] \f[I]directory\f[R]
Specifies the destination directory where the \f[V]javadoc\f[R] tool
saves the generated HTML files.
If you omit the \f[V]-d\f[R] option, then the files are saved to the
current directory.
The \f[V]directory\f[R] value can be absolute or relative to the current
working directory.
The destination directory is automatically created when the
\f[V]javadoc\f[R] tool runs.
.RS
.IP \[bu] 2
\f[B]Linux and macOS:\f[R] For example, the following command generates
the documentation for the package \f[V]com.mypackage\f[R] and saves the
results in the \f[V]/user/doc/\f[R] directory:
.RS 2
.IP
.nf
\f[CB]
javadoc -d /user/doc/ com.mypackage
\f[R]
.fi
.RE
.IP \[bu] 2
\f[B]Windows:\f[R] For example, the following command generates the
documentation for the package \f[V]com.mypackage\f[R] and saves the
results in the \f[V]\[rs]user\[rs]doc\[rs]\f[R] directory:
.RS 2
.IP
.nf
\f[CB]
javadoc -d \[rs]user\[rs]doc\[rs] com.mypackage
\f[R]
.fi
.RE
.RE
.TP
\f[V]-docencoding\f[R] \f[I]name\f[R]
Specifies the encoding of the generated HTML files.
The name should be a preferred MIME name as specified in the \f[B]IANA
Registry, Character Sets\f[R]
[http://www.iana.org/assignments/character-sets].
.RS
.PP
Three options are available for use in a \f[V]javadoc\f[R] encoding
command.
The \f[V]-encoding\f[R] option is used for encoding the files read by
the \f[V]javadoc\f[R] tool, while the \f[V]-docencoding\f[R] and
\f[V]-charset\f[R] options are used for encoding the files written by
the tool.
Of the three available options, at most, only the input and an output
encoding option are used in a single encoding command.
If you specify both input and output encoding options in a command, they
must be the same value.
If you specify neither output option, it defaults to the input encoding.
.PP
For example:
.IP
.nf
\f[CB]
javadoc -docencoding \[dq]iso-8859-1\[dq] mypackage
\f[R]
.fi
.RE
.TP
\f[V]-docfilessubdirs\f[R]
Enables deep copying of \f[V]doc-files\f[R] directories.
Subdirectories and all contents are recursively copied to the
destination.
For example, the directory \f[V]doc-files/example/images\f[R] and all of
its contents are copied.
Use the \f[B]\f[VB]-excludedocfilessubdir\f[B]\f[R] option to restrict
the subdirectories to be copied.
.TP
\f[V]-doctitle\f[R] \f[I]html-code\f[R]
Specifies the title to place near the top of the overview summary file.
The text specified in the \f[V]title\f[R] tag is placed as a centered,
level-one heading directly beneath the navigation bar.
The \f[V]title\f[R] tag can contain HTML tags and white space, but when
it does, you must enclose the title in quotation marks.
Additional quotation marks within the \f[V]title\f[R] tag must be
escaped.
For example,
\f[V]javadoc -doctitle \[dq]<b>My Library</b><br>v1.0\[dq] com.mypackage\f[R].
.TP
\f[V]-excludedocfilessubdir\f[R] \f[I]name1\f[R]\f[V],\f[R]\f[I]name2...\f[R]
Excludes any subdirectories with the given names when recursively
copying \f[V]doc-files\f[R] subdirectories.
See \f[B]\f[VB]-docfilessubdirs\f[B]\f[R].
For historical reasons, \f[V]:\f[R] can be used anywhere in the argument
as a separator instead of \f[V],\f[R].
.TP
\f[V]-footer\f[R] \f[I]html-code\f[R]
This option is no longer supported and reports a warning.
.TP
\f[V]-group\f[R] \f[I]name\f[R] \f[I]p1\f[R]\f[V],\f[R]\f[I]p2...\f[R]
Group the specified packages together in the Overview page.
For historical reasons, \f[V]:\f[R] can be used as a separator anywhere
in the argument instead of \f[V],\f[R].
.TP
\f[V]-header\f[R] \f[I]html-code\f[R]
Specifies the header text to be placed at the top of each output file.
The header is placed to the right of the navigation bar.
The \f[V]header\f[R] can contain HTML tags and white space, but when it
does, the \f[V]header\f[R] must be enclosed in quotation marks.
Use escape characters for internal quotation marks within a header.
For example,
\f[V]javadoc -header \[dq]<b>My Library</b><br>v1.0\[dq] com.mypackage\f[R].
.TP
\f[V]-helpfile\f[R] \f[I]filename\f[R]
Specifies a file containing the text that will be displayed when the
\f[B]HELP\f[R] link in the navigation bar is clicked.
If this option is not given, the \f[V]javadoc\f[R] tool creates a
default page that will be used.
.TP
\f[V]-html5\f[R]
This option is a no-op and is just retained for backwards compatibility.
.TP
\f[V]--javafx\f[R] or \f[V]-javafx\f[R]
Enables JavaFX functionality.
This option is enabled by default if the JavaFX library classes are
detected on the module path.
.TP
\f[V]-keywords\f[R]
Adds HTML keyword \f[V]<meta>\f[R] tags to the generated file for each
class.
These tags can help search engines that look for \f[V]<meta>\f[R] tags
find the pages.
Most search engines that search the entire Internet don\[aq]t look at
\f[V]<meta>\f[R] tags, because pages can misuse them.
Search engines offered by companies that confine their searches to their
own website can benefit by looking at \f[V]<meta>\f[R] tags.
The \f[V]<meta>\f[R] tags include the fully qualified name of the class
and the unqualified names of the fields and methods.
Constructors aren\[aq]t included because they are identical to the class
name.
For example, the page for the class \f[V]String\f[R] includes these
keywords:
.RS
.IP
.nf
\f[CB]
<meta name=\[dq]keywords\[dq] content=\[dq]java.lang.String class\[dq]>
<meta name=\[dq]keywords\[dq] content=\[dq]CASE_INSENSITIVE_ORDER\[dq]>
<meta name=\[dq]keywords\[dq] content=\[dq]length()\[dq]>
<meta name=\[dq]keywords\[dq] content=\[dq]isEmpty()\[dq]>
\f[R]
.fi
.RE
.TP
\f[V]-link\f[R] \f[I]url\f[R]
Creates links to existing \f[V]javadoc\f[R] generated documentation of
externally referenced classes.
The \f[I]url\f[R] argument is the absolute or relative URL of the
directory that contains the external \f[V]javadoc\f[R] generated
documentation.
You can specify multiple \f[V]-link\f[R] options in a specified
\f[V]javadoc\f[R] tool run to link to multiple documents.
.RS
.PP
Either a \f[V]package-list\f[R] or an \f[V]element-list\f[R] file must
be in this \f[I]url\f[R] directory (otherwise, use the
\f[V]-linkoffline\f[R] option).
.PP
\f[I]Note:\f[R] The \f[V]package-list\f[R] and \f[V]element-list\f[R]
files are generated by the \f[V]javadoc\f[R] tool when generating the
API documentation and should not be modified by the user.
.PP
When you use the \f[V]javadoc\f[R] tool to document packages, it uses
the \f[V]package-list\f[R] file to determine the packages declared in an
API.
When you generate API documents for modules, the \f[V]javadoc\f[R] tool
uses the \f[V]element-list\f[R] file to determine the modules and
packages declared in an API.
.PP
The \f[V]javadoc\f[R] tool reads the names from the appropriate list
file and then links to the packages or modules at that URL.
.PP
When the \f[V]javadoc\f[R] tool runs, the \f[I]url\f[R] value is copied
into the \f[V]<a href>\f[R] links that are created.
Therefore, \f[I]url\f[R] must be the URL to the directory and not to a
file.
.PP
You can use an absolute link for \f[I]url\f[R] to enable your documents
to link to a document on any web site, or you can use a relative link to
link only to a relative location.
If you use a relative link, then the value you pass in should be the
relative path from the destination directory (specified with the
\f[V]-d\f[R] option) to the directory containing the packages being
linked to.
When you specify an absolute link, you usually use an HTTP link.
However, if you want to link to a file system that has no web server,
then you can use a file link.
Use a file link only when everyone who wants to access the generated
documentation shares the same file system.
In all cases, and on all operating systems, use a slash as the
separator, whether the URL is absolute or relative, and
\f[V]https:\f[R], \f[V]http:\f[R], or \f[V]file:\f[R] as specified in
the \f[B]RFC 1738: Uniform Resource Locators (URL)\f[R]
[https://www.rfc-editor.org/info/rfc1738].
.IP
.nf
\f[CB]
-link https://<host>/<directory>/<directory>/.../<name>
-link http://<host>/<directory>/<directory>/.../<name>
-link file://<host>/<directory>/<directory>/.../<name>
-link <directory>/<directory>/.../<name>
\f[R]
.fi
.RE
.TP
\f[V]--link-modularity-mismatch\f[R] (\f[V]warn\f[R]|\f[V]info\f[R])
Specifies whether external documentation with wrong modularity (e.g.
non-modular documentation for a modular library, or the reverse case)
should be reported as a warning (\f[V]warn\f[R]) or just a message
(\f[V]info\f[R]).
The default behavior is to report a warning.
.TP
\f[V]-linkoffline\f[R] \f[I]url1\f[R] \f[I]url2\f[R]
This option is a variation of the \f[V]-link\f[R] option.
They both create links to \f[V]javadoc\f[R] generated documentation for
externally referenced classes.
You can specify multiple \f[V]-linkoffline\f[R] options in a specified
\f[V]javadoc\f[R] tool run.
.RS
.PP
Use the \f[V]-linkoffline\f[R] option when:
.IP \[bu] 2
Linking to a document on the web that the \f[V]javadoc\f[R] tool
can\[aq]t access through a web connection
.IP \[bu] 2
The \f[V]package-list\f[R] or \f[V]element-list\f[R] file of the
external document either isn\[aq]t accessible or doesn\[aq]t exist at
the URL location, but does exist at a different location and can be
specified by either the \f[V]package-list\f[R] or \f[V]element-list\f[R]
file (typically local).
.PP
\f[I]Note:\f[R] The \f[V]package-list\f[R] and \f[V]element-list\f[R]
files are generated by the \f[V]javadoc\f[R] tool when generating the
API documentation and should not be modified by the user.
.PP
If \f[I]url1\f[R] is accessible only on the World Wide Web, then the
\f[V]-linkoffline\f[R] option removes the constraint that the
\f[V]javadoc\f[R] tool must have a web connection to generate
documentation.
.PP
Another use of the \f[V]-linkoffline\f[R] option is as a work-around to
update documents.
After you have run the \f[V]javadoc\f[R] tool on a full set of packages
or modules, you can run the \f[V]javadoc\f[R] tool again on a smaller
set of changed packages or modules, so that the updated files can be
inserted back into the original set.
.PP
For example, the \f[V]-linkoffline\f[R] option takes two arguments.
The first is for the string to be embedded in the \f[V]<a href>\f[R]
links, and the second tells the \f[V]javadoc\f[R] tool where to find
either the \f[V]package-list\f[R] or \f[V]element-list\f[R] file.
.PP
The \f[I]url1\f[R] or \f[I]url2\f[R] value is the absolute or relative
URL of the directory that contains the external \f[V]javadoc\f[R]
generated documentation that you want to link to.
When relative, the value should be the relative path from the
destination directory (specified with the \f[V]-d\f[R] option) to the
root of the packages being linked to.
See \f[I]url\f[R] in the \f[V]-link\f[R] option.
.RE
.TP
\f[V]--link-platform-properties\f[R] \f[I]url\f[R]
Specifies a properties file used to configure links to platform
documentation.
.RS
.PP
The \f[I]url\f[R] argument is expected to point to a properties file
containing one or more entries with the following format, where
\f[V]<version>\f[R] is the platform version as passed to the
\f[V]--release\f[R] or \f[V]--source\f[R] option and \f[V]<url>\f[R] is
the base URL of the corresponding platform API documentation:
.IP
.nf
\f[CB]
doclet.platform.docs.<version>=<url>
\f[R]
.fi
.PP
For instance, a properties file containing URLs for releases 15 to 17
might contain the following lines:
.IP
.nf
\f[CB]
doclet.platform.docs.15=https://example.com/api/15/
doclet.platform.docs.16=https://example.com/api/16/
doclet.platform.docs.17=https://example.com/api/17/
\f[R]
.fi
.PP
If the properties file does not contain an entry for a particular
release no platform links are generated.
.RE
.TP
\f[V]-linksource\f[R]
Creates an HTML version of each source file (with line numbers) and adds
links to them from the standard HTML documentation.
Links are created for classes, interfaces, constructors, methods, and
fields whose declarations are in a source file.
Otherwise, links aren\[aq]t created, such as for default constructors
and generated classes.
.RS
.PP
This option exposes all private implementation details in the included
source files, including private classes, private fields, and the bodies
of private methods, regardless of the \f[V]-public\f[R],
\f[V]-package\f[R], \f[V]-protected\f[R], and \f[V]-private\f[R]
options.
Unless you also use the \f[V]-private\f[R] option, not all private
classes or interfaces are accessible through links.
.PP
Each link appears on the name of the identifier in its declaration.
For example, the link to the source code of the \f[V]Button\f[R] class
would be on the word \f[V]Button\f[R]:
.IP
.nf
\f[CB]
public class Button extends Component implements Accessible
\f[R]
.fi
.PP
The link to the source code of the \f[V]getLabel\f[R] method in the
\f[V]Button\f[R] class is on the word \f[V]getLabel\f[R]:
.IP
.nf
\f[CB]
public String getLabel()
\f[R]
.fi
.RE
.TP
\f[V]--main-stylesheet\f[R] \f[I]file\f[R] or \f[V]-stylesheetfile\f[R] \f[I]file\f[R]
Specifies the path of an alternate stylesheet file that contains the
definitions for the CSS styles used in the generated documentation.
This option lets you override the default.
If you do not specify the option, the \f[V]javadoc\f[R] tool will create
and use a default stylesheet.
The file name can be any name and isn\[aq]t restricted to
\f[V]stylesheet.css\f[R].
The \f[V]--main-stylesheet\f[R] option is the preferred form.
.RS
.PP
Command-line example:
.IP
.nf
\f[CB]
javadoc --main-stylesheet main_stylesheet.css pkg_foo
\f[R]
.fi
.RE
.TP
\f[V]-nocomment\f[R]
Suppresses the entire comment body, including the main description and
all tags, and generate only declarations.
This option lets you reuse source files that were originally intended
for a different purpose so that you can produce skeleton HTML
documentation during the early stages of a new project.
.TP
\f[V]-nodeprecated\f[R]
Prevents the generation of any deprecated API in the documentation.
This does what the \f[V]-nodeprecatedlist\f[R] option does, and it
doesn\[aq]t generate any deprecated API throughout the rest of the
documentation.
This is useful when writing code when you don\[aq]t want to be
distracted by the deprecated code.
.TP
\f[V]-nodeprecatedlist\f[R]
Prevents the generation of the file that contains the list of deprecated
APIs (\f[V]deprecated-list.html\f[R]) and the link in the navigation bar
to that page.
The \f[V]javadoc\f[R] tool continues to generate the deprecated API
throughout the rest of the document.
This is useful when your source code contains no deprecated APIs, and
you want to make the navigation bar cleaner.
.TP
\f[V]--no-fonts\f[R]
Prevents inclusion of font files in the generated documentation.
This can be useful if the documentation uses a custom stylesheet which
does not use the default fonts.
.TP
\f[V]-nohelp\f[R]
Omits the \f[B]HELP\f[R] link in the navigation bar at the top of each
generated page.
.TP
\f[V]-noindex\f[R]
Omits the index from the generated documents.
The index is produced by default.
.TP
\f[V]-nonavbar\f[R]
Prevents the generation of the navigation bar and header.
The \f[V]-nonavbar\f[R] option has no effect on the \f[V]-bottom\f[R]
option.
The \f[V]-nonavbar\f[R] option is useful when you are interested only in
the content and have no need for navigation, such as when you are
converting the files to PostScript or PDF for printing only.
.TP
\f[V]--no-platform-links\f[R]
Prevents the generation of links to platform documentation.
These links are generated by default.
.TP
\f[V]-noqualifier\f[R] \f[I]name1\f[R]\f[V],\f[R]\f[I]name2...\f[R]
Excludes the list of qualifiers from the output.
The package name is removed from places where class or interface names
appear.
For historical reasons, \f[V]:\f[R] can be used anywhere in the argument
as a separator instead of \f[V],\f[R].
.RS
.PP
The following example omits all package qualifiers:
\f[V]-noqualifier all\f[R].
.PP
The following example omits \f[V]java.lang\f[R] and \f[V]java.io\f[R]
package qualifiers: \f[V]-noqualifier java.lang:java.io\f[R].
.PP
The following example omits package qualifiers starting with
\f[V]java\f[R] and \f[V]com.sun\f[R] subpackages, but not
\f[V]javax: -noqualifier java.*:com.sun.*\f[R].
.PP
Where a package qualifier would appear due to the previous behavior, the
name can be suitably shortened.
This rule is in effect whether or not the \f[V]-noqualifier\f[R] option
is used.
.RE
.TP
\f[V]-nosince\f[R]
Omits from the generated documentation the \f[V]Since\f[R] sections
derived from any \f[V]since\f[R] tags.
.TP
\f[V]-notimestamp\f[R]
Suppresses the time stamp, which is hidden in an HTML comment in the
generated HTML near the top of each page.
The \f[V]-notimestamp\f[R] option is useful when you want to run the
\f[V]javadoc\f[R] tool on two source bases and compare them, because it
prevents time stamps from causing a difference (which would otherwise be
a difference on every page).
The time stamp includes the \f[V]javadoc\f[R] tool release number.
.TP
\f[V]-notree\f[R]
Omits the class and interface hierarchy pages from the generated
documents.
These are the pages you reach using the \f[B]TREE\f[R] link in the
navigation bar.
The hierarchy is produced by default.
.TP
\f[V]--override-methods\f[R] (\f[V]detail\f[R]|\f[V]summary\f[R])
Documents overridden methods in the detail or summary sections.
The default is \f[V]detail\f[R].
.TP
\f[V]-overview\f[R] \f[I]filename\f[R]
Specifies that the \f[V]javadoc\f[R] tool should retrieve the content
for the overview documentation from the file specified by
\f[I]filename\f[R] and place it on the Overview page
(\f[V]overview-summary.html\f[R]).
If the \f[I]filename\f[R] is a relative path, it is evaluated relative
to the current working directory.
.RS
.PP
The file may be an HTML file, with a filename ending in \f[V].html\f[R],
or a Markdown file, with a filename ending in \f[V].md\f[R].
If the file is an HTML file, the content for the overview documentation
is taken from the \f[V]<main>\f[R] element in the file, if one is
present, or from the \f[V]<body>\f[R] element is there is no
\f[V]<main>\f[R] element.
If the file is a Markdown file, the entire content of the file is used.
.PP
The title on the overview page is set by \f[V]-doctitle\f[R].
.PP
\f[I]Note:\f[R] older versions of the \f[V]javadoc\f[R] tool assumed
that any use of this option was for an HTML file, and allowed any
extension for the \f[I]filename\f[R].
.RE
.TP
\f[V]-serialwarn\f[R]
Reports compile-time warnings for missing \f[V]\[at]serial\f[R] tags.
By default, Javadoc reports no serial warnings.
Use this option to display the serial warnings, which helps to properly
document default serializable fields and \f[V]writeExternal\f[R]
methods.
.TP
\f[V]--since\f[R] \f[I]release\f[R](\f[V],\f[R]\f[I]release\f[R])*
Generates documentation for APIs that were added or newly deprecated in
the specified \f[I]release\f[R]s.
.RS
.PP
If the \f[V]\[at]since\f[R] tag in the \f[V]javadoc\f[R] comment of an
element in the documented source code matches a \f[I]release\f[R] passed
as the option argument, information about the element and the release it
was added in is included in a \[dq]New API\[dq] page.
.PP
If the \[dq]Deprecated API\[dq] page is generated and the
\f[V]since\f[R] element of the \f[V]java.lang.Deprecated\f[R] annotation
of a documented element matches a \f[I]release\f[R] in the option
arguments, information about the release the element was deprecated in
is added to the \[dq]Deprecated API\[dq] page.
.PP
Releases are compared using case-sensitive string comparison.
.RE
.TP
\f[V]--since-label\f[R] \f[I]text\f[R]
Specifies the \f[I]text\f[R] to use in the heading of the \[dq]New
API\[dq] page.
This may contain information about the releases covered in the page,
e.g.
\[dq]New API in release 2.0\[dq], or \[dq]New API since release 1\[dq].
.TP
\f[V]--snippet-path\f[R] \f[I]snippetpathlist\f[R]
Specifies the search paths for finding files for external snippets.
The \f[I]snippetpathlist\f[R] can contain multiple paths by separating
them with the platform path separator (\f[V];\f[R] on Windows;
\f[V]:\f[R] on other platforms.)
The standard doclet first searches the \f[V]snippet-files\f[R]
subdirectory in the package containing the snippet, and then searches
all the directories in the given list.
.TP
\f[V]-sourcetab\f[R] \f[I]tab-length\f[R]
Specifies the number of spaces each tab uses in the source.
.TP
\f[V]--spec-base-url\f[R] \f[I]url\f[R]
Specifies the base URL for relative URLs in \f[V]\[at]spec\f[R] tags, to
be used when generating links to any external specifications.
It can either be an absolute URL, or a relative URL, in which case it is
evaluated relative to the base directory of the generated output files.
The default value is equivalent to \f[V]{\[at]docRoot}/../specs\f[R].
.TP
\f[V]-splitindex\f[R]
Splits the index file into multiple files, alphabetically, one file per
letter, plus a file for any index entries that start with
non-alphabetical symbols.
.TP
\f[V]-tag\f[R] \f[I]name\f[R]:\f[I]locations\f[R]:\f[I]header\f[R]
Specifies a custom tag with a single argument.
For the \f[V]javadoc\f[R] tool to spell-check tag names, it is important
to include a \f[V]-tag\f[R] option for every custom tag that is present
in the source code, disabling (with \f[V]X\f[R]) those that aren\[aq]t
being output in the current run.
The colon (\f[V]:\f[R]) is always the separator.
To include a colon in the tag name, escape it with a backward slash
(\f[V]\[rs]\f[R]).
The \f[V]-tag\f[R] option outputs the tag heading, \f[I]header\f[R], in
bold, followed on the next line by the text from its single argument.
Similar to any block tag, the argument text can contain inline tags,
which are also interpreted.
The output is similar to standard one-argument tags, such as the
\f[V]\[at]return\f[R] and \f[V]\[at]author\f[R] tags.
Omitting a \f[I]header\f[R] value causes the \f[I]name\f[R] to be the
heading.
\f[I]locations\f[R] is a list of characters specifying the kinds of
declarations in which the tag may be used.
The following characters may be used, in either uppercase or lowercase:
.RS
.IP \[bu] 2
\f[V]A\f[R]: all declarations
.IP \[bu] 2
\f[V]C\f[R]: constructors
.IP \[bu] 2
\f[V]F\f[R]: fields
.IP \[bu] 2
\f[V]M\f[R]: methods
.IP \[bu] 2
\f[V]O\f[R]: the overview page and other documentation files in
\f[V]doc-files\f[R] subdirectories
.IP \[bu] 2
\f[V]P\f[R]: packages
.IP \[bu] 2
\f[V]S\f[R]: modules
.IP \[bu] 2
\f[V]T\f[R]: types (classes and interfaces)
.IP \[bu] 2
\f[V]X\f[R]: nowhere: the tag is disabled, and will be ignored
.PP
The order in which tags are given on the command line will be used as
the order in which the tags appear in the generated output.
You can include standard tags in the order given on the command line by
using the \f[V]-tag\f[R] option with no \f[I]locations\f[R] or
\f[I]header\f[R].
.RE
.TP
\f[V]-taglet\f[R] \f[I]class\f[R]
Specifies the fully qualified name of the taglet used in generating the
documentation for that tag.
Use the fully qualified name for the \f[I]class\f[R] value.
This taglet also defines the number of text arguments that the custom
tag has.
The taglet accepts those arguments, processes them, and generates the
output.
.RS
.PP
Taglets are useful for block or inline tags.
They can have any number of arguments and implement custom behavior,
such as making text bold, formatting bullets, writing out the text to a
file, or starting other processes.
Taglets can only determine where a tag should appear and in what form.
All other decisions are made by the doclet.
A taglet can\[aq]t do things such as remove a class name from the list
of included classes.
However, it can execute side effects, such as printing the tag\[aq]s
text to a file or triggering another process.
Use the \f[V]-tagletpath\f[R] option to specify the path to the taglet.
The following example inserts the To Do taglet after Parameters and
ahead of Throws in the generated pages.
.IP
.nf
\f[CB]
-taglet com.sun.tools.doclets.ToDoTaglet
-tagletpath /home/<USER>
-tag return
-tag param
-tag todo
-tag throws
-tag see
\f[R]
.fi
.PP
Alternately, you can use the \f[V]-taglet\f[R] option in place of its
\f[V]-tag\f[R] option, but that might be difficult to read.
.RE
.TP
\f[V]-tagletpath\f[R] \f[I]tagletpathlist\f[R]
Specifies the search paths for finding taglet class files.
The \f[I]tagletpathlist\f[R] can contain multiple paths by separating
them with the platform path separator (\f[V];\f[R] on Windows;
\f[V]:\f[R] on other platforms.)
The \f[V]javadoc\f[R] tool searches all subdirectories of the specified
paths.
.TP
\f[V]-top\f[R] \f[I]html-code\f[R]
Specifies the text to be placed at the top of each output file.
.TP
\f[V]-use\f[R]
Creates class and package usage pages.
Includes one Use page for each documented class and package.
The page describes what packages, classes, methods, constructors, and
fields use any API of the specified class or package.
Given class C, things that use class C would include subclasses of C,
fields declared as C, methods that return C, and methods and
constructors with parameters of type C.
For example, you can look at the Use page for the \f[V]String\f[R] type.
Because the \f[V]getName\f[R] method in the \f[V]java.awt.Font\f[R]
class returns type \f[V]String\f[R], the \f[V]getName\f[R] method uses
\f[V]String\f[R] and so the \f[V]getName\f[R] method appears on the Use
page for \f[V]String\f[R].
This documents only uses of the API, not the implementation.
When a method uses \f[V]String\f[R] in its implementation, but
doesn\[aq]t take a string as an argument or return a string, that
isn\[aq]t considered a use of \f[V]String\f[R].
To access the generated Use page, go to the class or package and click
the \f[B]USE\f[R] link in the navigation bar.
.TP
\f[V]-version\f[R]
Includes the text of any \f[V]version\f[R] tags in the generated
documentation.
This text is omitted by default.
Note: To find out what version of the \f[V]javadoc\f[R] tool you are
using, use the \f[V]--version\f[R] option (with two hyphens).
.TP
\f[V]-windowtitle\f[R] \f[I]title\f[R]
Specifies the title to be placed in the HTML \f[V]<title>\f[R] tag.
The text specified in the \f[V]title\f[R] tag appears in the window
title and in any browser bookmarks (favorite places) that someone
creates for this page.
This title should not contain any HTML tags because a browser will not
interpret them correctly.
Use escape characters on any internal quotation marks within the
\f[V]title\f[R] tag.
If the \f[V]-windowtitle\f[R] option is omitted, then the
\f[V]javadoc\f[R] tool uses the value of the \f[V]-doctitle\f[R] option
for the \f[V]-windowtitle\f[R] option.
For example,
\f[V]javadoc -windowtitle \[dq]My Library\[dq] com.mypackage\f[R].
.SS Extra Options for the Standard Doclet
.PP
The following are additional options provided by the standard doclet and
are subject to change without notice.
Additional options are less commonly used or are otherwise regarded as
advanced.
.TP
\f[V]--date\f[R] \f[I]date-and-time\f[R]
Specifies the value to be used to timestamp the generated pages, in
\f[B]ISO 8601\f[R]
[https://www.iso.org/iso-8601-date-and-time-format.html] format.
The specified value must be within 10 years of the current date and
time.
It is an error to specify both \f[V]-notimestamp\f[R] and
\f[V]--date\f[R].
Using a specific value means the generated documentation can be part of
a \f[B]reproducible build\f[R] [https://reproducible-builds.org/].
If the option is not given, the default value is the current date and
time.
For example:
.RS
.IP
.nf
\f[CB]
javadoc --date 2022-02-01T17:41:59-08:00 mypackage
\f[R]
.fi
.RE
.TP
\f[V]--legal-notices\f[R] (\f[V]default\f[R]|\f[V]none\f[R]|\f[I]directory\f[R])
Specifies the location from which to copy legal files to the generated
documentation.
If the option is not specified or is used with the value
\f[V]default\f[R], the files are copied from the default location.
If the argument is used with value \f[V]none\f[R], no files are copied.
Every other argument is interpreted as directory from which to copy the
legal files.
.TP
\f[V]--no-frames\f[R]
This option is no longer supported and reports a warning.
.TP
\f[V]-Xdoclint\f[R]
Enables recommended checks for problems in documentation comments.
.RS
.PP
By default, the \f[V]-Xdoclint\f[R] option is enabled.
Disable it with the option \f[V]-Xdoclint:none\f[R].
.PP
For more details, see \f[B]DocLint\f[R].
.RE
.TP
\f[V]-Xdoclint:\f[R]\f[I]flag\f[R],\f[I]flag\f[R],...
Enables or disables specific checks for different kinds of issues in
documentation comments.
.RS
.PP
Each \f[I]flag\f[R] can be one of \f[V]all\f[R], \f[V]none\f[R], or
\f[V][-]\f[R]\f[I]group\f[R] where \f[I]group\f[R] has one of the
following values: \f[V]accessibility\f[R], \f[V]html\f[R],
\f[V]missing\f[R], \f[V]reference\f[R], \f[V]syntax\f[R].
For more details on these values, see \f[B]DocLint Groups\f[R].
.PP
When specifying two or more flags, you can either use a single
\f[V]-Xdoclint:...\f[R] option, listing all the desired flags, or you
can use multiple options giving one or more flag in each option.
For example, use either of the following commands to check for the HTML,
syntax, and accessibility issues in the file \f[V]MyFile.java\f[R].
.IP
.nf
\f[CB]
javadoc -Xdoclint:html -Xdoclint:syntax -Xdoclint:accessibility MyFile.java
javadoc -Xdoclint:html,syntax,accessibility MyFile.java
\f[R]
.fi
.PP
The following examples illustrate how to change what DocLint reports:
.IP \[bu] 2
\f[V]-Xdoclint:none\f[R] --- disables all checks
.IP \[bu] 2
\f[V]-Xdoclint:\f[R]\f[I]group\f[R] --- enables \f[I]group\f[R] checks
.IP \[bu] 2
\f[V]-Xdoclint:all\f[R] --- enables all groups of checks
.IP \[bu] 2
\f[V]-Xdoclint:all,-\f[R]\f[I]group\f[R] --- enables all checks except
\f[I]group\f[R] checks
.PP
For more details, see \f[B]DocLint\f[R].
.RE
.TP
\f[V]-Xdoclint/package:\f[R][\f[V]-\f[R]]\f[I]packages\f[R]
Enables or disables checks in specific packages.
\f[I]packages\f[R] is a comma separated list of package specifiers.
A package specifier is either a qualified name of a package or a package
name prefix followed by \f[V]*\f[R], which expands to all subpackages of
the given package.
Prefix the package specifier with \f[V]-\f[R] to disable checks for the
specified packages.
.RS
.PP
For more details, see \f[B]DocLint\f[R].
.RE
.TP
\f[V]-Xdocrootparent\f[R] \f[I]url\f[R]
Replaces all \f[V]\[at]docRoot\f[R] items followed by \f[V]/..\f[R] in
documentation comments with \f[I]url\f[R].
.SH DOCLINT
.PP
DocLint provides the ability to check for possible problems in
documentation comments.
Problems may be reported as warnings or errors, depending on their
severity.
For example, a missing comment may be bad style that deserves a warning,
but a link to an unknown Java declaration is more serious and deserves
an error.
Problems are organized into \f[B]groups\f[R], and options can be used to
enable or disable messages in one or more groups.
Within the source code, messages in one or more groups can be
\f[B]suppressed\f[R] by using \f[V]\[at]SuppressWarnings\f[R]
annotations.
.PP
When invoked from \f[V]javadoc\f[R], by default DocLint checks all
comments that are used in the generated documentation.
It thus relies on other command-line options to determine which
declarations, and which corresponding documentation comments will be
included.
\f[I]Note:\f[R] this may mean that even comments on some private members
of serializable classes will also be checked, if the members need to be
documented in the generated \f[V]Serialized Forms\f[R] page.
.PP
In contrast, when DocLint is invoked from \f[V]javac\f[R], DocLint
solely relies on the various \f[V]-Xdoclint...\f[R] options to determine
which documentation comments to check.
.PP
DocLint doesn\[aq]t attempt to fix invalid input, it just reports it.
.PP
\f[I]Note:\f[R] DocLint doesn\[aq]t guarantee the completeness of these
checks.
In particular, it isn\[aq]t a full HTML compliance checker.
The goal is to just report common errors in a convenient manner.
.SS Groups
.PP
The checks performed by DocLint are organized into groups.
The warnings and errors in each group can be enabled or disabled with
command-line options, or suppressed with \f[V]\[at]SuppressWarnings\f[R]
annotations.
.PP
The groups are as follows:
.IP \[bu] 2
\f[V]accessibility\f[R] --- Checks for issues related to accessibility.
For example, no \f[V]alt\f[R] attribute specified in an \f[V]<img>\f[R]
element, or no caption or summary attributes specified in a
\f[V]<table>\f[R] element.
.RS 2
.PP
Issues are reported as errors if a downstream validation tool might be
expected to report an error in the files generated by \f[V]javadoc\f[R].
.PP
For reference, see the \f[B]Web Content Accessibility Guidelines\f[R]
[https://www.w3.org/WAI/standards-guidelines/wcag/].
.RE
.IP \[bu] 2
\f[V]html\f[R] --- Detects common high-level HTML issues.
For example, putting block elements inside inline elements, or not
closing elements that require an end tag.
.RS 2
.PP
Issues are reported as errors if a downstream validation tool might be
expected to report an error in the files generated by \f[V]javadoc\f[R].
.PP
For reference, see the \f[B]HTML Living Standard\f[R]
[https://html.spec.whatwg.org/multipage/].
.RE
.IP \[bu] 2
\f[V]missing\f[R] --- Checks for missing documentation comments or tags.
For example, a missing comment on a class declaration, or a missing
\f[V]\[at]param\f[R] or \f[V]\[at]return\f[R] tag in the comment for a
method declaration.
.RS 2
.PP
Issues related to missing items are typically reported as warnings
because they are unlikely to be reported as errors by downstream
validation tools that may be used to check the output generated by
\f[V]javadoc\f[R].
.RE
.IP \[bu] 2
\f[V]reference\f[R] --- Checks for issues relating to the references to
Java API elements from documentation comment tags.
For example, the reference in \f[V]\[at]see\f[R] or
\f[V]{\[at]link ...}\f[R] cannot be found, or a bad name is given for
\f[V]\[at]param\f[R] or \f[V]\[at]throws\f[R].
.RS 2
.PP
Issues are typically reported as errors because while the issue may not
cause problems in the generated files, the author has likely made a
mistake that will lead to incorrect or unexpected documentation.
.RE
.IP \[bu] 2
\f[V]syntax\f[R] --- Checks for low-level syntactic issues in
documentation comments.
For example, unescaped angle brackets (\f[V]<\f[R] and \f[V]>\f[R]) and
ampersands (\f[V]&\f[R]) and invalid documentation comment tags.
.RS 2
.PP
Issues are typically reported as errors because the issues may lead to
incorrect or unexpected documentation.
.RE
.SS Suppressing Messages
.PP
DocLint checks for and recognizes two strings that may be present in the
arguments for an \f[V]\[at]SuppressWarnings\f[R] annotation.
.IP \[bu] 2
\f[V]doclint\f[R]
.IP \[bu] 2
\f[V]doclint:\f[R]\f[I]LIST\f[R]
.PP
where \f[I]LIST\f[R] is a comma-separated list of one or more of
\f[V]accessibility\f[R], \f[V]html\f[R], \f[V]missing\f[R],
\f[V]reference\f[R], \f[V]syntax\f[R].
.PP
The names in \f[I]LIST\f[R] are the same \f[B]group\f[R] names supported
by the command-line \f[V]-Xdoclint\f[R] option for \f[V]javac\f[R] and
\f[V]javadoc\f[R].
(This is the same convention honored by the \f[V]javac\f[R]
\f[V]-Xlint\f[R] option and the corresponding names supported by
\f[V]\[at]SuppressWarnings\f[R].)
.PP
The names in \f[I]LIST\f[R] can equivalently be specified in separate
arguments of the annotation.
For example, the following are equivalent:
.IP \[bu] 2
\f[V]\[at]SuppressWarnings(\[dq]doclint:accessibility,missing\[dq])\f[R]
.IP \[bu] 2
\f[V]\[at]SuppressWarnings(\[dq]doclint:accessibility\[dq], \[dq]doclint:missing\[dq])\f[R]
.PP
When DocLint detects an issue in a documentation comment, it checks for
the presence of \f[V]\[at]SuppressWarnings\f[R] on the associated
declaration and on all lexically enclosing declarations.
The issue will be ignored if any such annotation is found containing the
simple string \f[V]doclint\f[R] or the longer form
\f[V]doclint:LIST\f[R] where \f[I]LIST\f[R] contains the name of the
group for the issue.
.PP
\f[I]Note:\f[R] as with other uses of \f[V]\[at]SuppressWarnings\f[R],
using the annotation on a module or package declaration only affects
that declaration; it does not affect the contents of the module or
package in other source files.
.PP
All messages related to an issue are suppressed by the presence of an
appropriate \f[V]\[at]SuppressWarnings\f[R] annotation: this includes
errors as well as warnings.
.PP
\f[I]Note:\f[R] It is only possible to \f[I]suppress\f[R] messages.
If an annotation of \f[V]\[at]SuppressWarnings(\[dq]doclint\[dq])\f[R]
is given on a top-level declaration, all DocLint messages for that
declaration and any enclosed declarations will be suppressed; it is not
possible to selectively re-enable messages for issues in enclosed
declarations.
.SS Comparison with downstream validation tools
.PP
DocLint is a utility built into \f[V]javac\f[R] and \f[V]javadoc\f[R]
that checks the content of documentation comments, as found in source
files.
In contrast, downstream validation tools can be used to validate the
output generated from those documentation comments by \f[V]javadoc\f[R]
and the standard doclet.
.PP
Although there is some overlap in functionality, the two mechanisms are
different and each has its own strengths and weaknesses.
.IP \[bu] 2
Downstream validation tools can check the end result of any generated
documentation, as it will be seen by the end user.
This includes content from all sources, including documentation
comments, the standard doclet itself, user-provided taglets, and content
supplied via command-line options.
Because such tools are analyzing complete HTML pages, they can do more
complete checks than can DocLint.
However, when a problem is found in the generated pages, it can be
harder to track down exactly where in the build pipeline the problem
needs to be fixed.
.IP \[bu] 2
DocLint checks the content of documentation comments, in source files.
This makes it very easy to identify the exact position of any issues
that may be found.
DocLint can also detect some semantic errors in documentation comments
that downstream tools cannot detect, such as missing comments, using an
\f[V]\[at]return\f[R] tag in a method returning \f[V]void\f[R], or an
\f[V]\[at]param\f[R] tag describing a non-existent parameter.
But by its nature, DocLint cannot report on problems such as missing
links, or errors in user-provided custom taglets, or problems in the
standard doclet itself.
It also cannot reliably detect errors in documentation comments at the
boundaries between content in a documentation comment and content
generated by a custom taglet.
