.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JACCESSWALKER" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jaccesswalker - navigate through the component trees in a particular
Java Virtual Machine and present the hierarchy in a tree view
.SH DESCRIPTION
.PP
You select a node in the tree, and from the \f[B]Panels\f[R] menu, you
select \f[B]Accessibility API Panel\f[R].
The \f[V]jaccesswalker\f[R] tool shows you the accessibility information
for the object in the window.
.SH RUNNING THE JACCESSWALKER TOOL
.PP
To use \f[V]jaccesswalker\f[R], launch the \f[V]jaccesswalker\f[R] tool
after launching a Java application.
For example, to launch \f[V]jaccesswalker\f[R], enter the following
command:
.PP
\f[B]Note:\f[R]
.PP
\f[V]JAVA_HOME\f[R] is an environment variable and should be set to the
path of the JDK or JRE, such as,
\f[V]c:\[rs]Program Files\[rs]Java\[rs]jdk-10\f[R].
.RS
.PP
\f[V]%JAVA_HOME%\[rs]bin\[rs]jaccesswalker.exe\f[R]
.RE
.PP
You now have two windows open: The Java application window, and the
window for the \f[V]jaccesswalker\f[R] tool.
There are two tasks that you can do with \f[V]jaccesswalker\f[R] .
You can build a tree view of the Java applications\[aq] GUI hierarchy,
and you can query the Java Accessibility API information of a particular
element in the GUI hierarchy.
.SH BUILDING THE GUI HIERARCHY
.PP
From the \f[B]File\f[R] menu, select \f[B]Refresh Tree\f[R] menu.
The \f[V]jaccesswalker\f[R] tool builds a list of the top-level windows
belonging to Java applications.
The tool then recursively queries the elements in those windows, and
builds a tree of all of the GUI components in all of the Java
applications in all of the JVMs running in the system.
.SH EXAMINING A GUI COMPONENT
.PP
After a GUI tree is built, you can view detailed accessibility
information about an individual GUI component by selecting it in the
tree, then selecting \f[B]Panels\f[R], and then \f[B]Display
Accessibility Information\f[R].
