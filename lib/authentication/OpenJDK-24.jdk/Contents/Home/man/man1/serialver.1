.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "SERIALVER" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
serialver - return the \f[V]serialVersionUID\f[R] for one or more
classes in a form suitable for copying into an evolving class
.SH SYNOPSIS
.PP
\f[V]serialver\f[R] [\f[I]options\f[R]] [\f[I]classnames\f[R]]
.TP
\f[I]options\f[R]
This represents the command-line options for the \f[V]serialver\f[R]
command.
See \f[B]Options for serialver\f[R].
.TP
\f[I]classnames\f[R]
The classes for which \f[V]serialVersionUID\f[R] is to be returned.
.SH DESCRIPTION
.PP
The \f[V]serialver\f[R] command returns the \f[V]serialVersionUID\f[R]
for one or more classes in a form suitable for copying into an evolving
class.
When called with no arguments, the \f[V]serialver\f[R] command prints a
usage line.
.SH OPTIONS FOR SERIALVER
.TP
\f[V]-classpath\f[R] \f[I]path-files\f[R]
Sets the search path for application classes and resources.
Separate classes and resources with a colon (:).
.TP
\f[V]-J\f[R]\f[I]option\f[R]
Passes the specified \f[I]option\f[R] to the Java Virtual Machine, where
\f[I]option\f[R] is one of the options described on the reference page
for the Java application launcher.
For example, \f[V]-J-Xms48m\f[R] sets the startup memory to 48 MB.
.SH WARNING
.PP
The \f[V]serialver\f[R] command loads and initializes the specified
classes in order to determine their \f[V]serialVersionUID\f[R] values.
\f[I]DO NOT RUN\f[R] \f[V]serialver\f[R] on untrusted classes.
