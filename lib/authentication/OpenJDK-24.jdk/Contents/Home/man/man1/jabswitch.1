.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JABSWITCH" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jabswitch - enable or disable the Java Access Bridge
.SH SYNOPSIS
.PP
\f[V]jabswitch\f[R] [ -enable|/enable | -disable|/disable |
-version|/version | -?|/?
]
.SH OPTIONS
.PP
\f[V]-enable\f[R] or \f[V]/enable\f[R] : Enables the Java Access Bridge
.PP
\f[V]-disable\f[R] or \f[V]/disable\f[R] : Disables the Java Access
Bridge
.PP
\f[V]-version\f[R] or \f[V]/version\f[R] : Displays version information
for the \f[V]jabswitch\f[R] command.
.PP
\f[V]-?\f[R] or \f[V]/?\f[R] : Displays usage information for the
\f[V]jabswitch\f[R] command.
.SH DESCRIPTION
.PP
The \f[V]jabswitch\f[R] command is a utility program that enables the
Java Access Bridge to be loaded by the JDK on Windows platforms.
The Java Access Bridge is used by Assistive Technologies to interact
with Java Accessibility APIs of the Java SE platform.
To have any effect, the assistive technology must support the Java
Access Bridge.
.PP
This command creates or updates a file named
\f[V].accessibility.properties\f[R], in the user\[aq]s home directory.
When selecting the \f[V]-enable\f[R] option, the file is populated with
the information needed to load the Java Access Bridge.
This file is then read and used in accordance with the specification of
the Java SE \f[B]\f[VB]java.awt.Toolkit.getDefaultToolkit()\f[B]\f[R]
API, on initialization.
.PP
Note: This command is only provided with JDK for Windows.
