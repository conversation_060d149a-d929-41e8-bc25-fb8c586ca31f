.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JPS" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jps - list the instrumented JVMs on the target system
.SH SYNOPSIS
.PP
\f[B]Note:\f[R] This command is experimental and unsupported.
.PP
\f[V]jps\f[R] [\f[V]-q\f[R]] [\f[V]-mlvV\f[R]] [\f[I]hostid\f[R]]
.PP
\f[V]jps\f[R] [\f[V]-help\f[R]]
.SH OPTIONS
.TP
\f[V]-q\f[R]
Suppresses the output of the class name, JAR file name, and arguments
passed to the \f[V]main\f[R] method, producing a list of only local JVM
identifiers.
.TP
\f[V]-mlvV\f[R]
You can specify any combination of these options.
.RS
.IP \[bu] 2
\f[V]-m\f[R] displays the arguments passed to the \f[V]main\f[R] method.
The output may be \f[V]null\f[R] for embedded JVMs.
.IP \[bu] 2
\f[V]-l\f[R] displays the full package name for the application\[aq]s
\f[V]main\f[R] class or the full path name to the application\[aq]s JAR
file.
.IP \[bu] 2
\f[V]-v\f[R] displays the arguments passed to the JVM.
.IP \[bu] 2
\f[V]-V\f[R] suppresses the output of the class name, JAR file name, and
arguments passed to the \f[V]main\f[R] method, producing a list of only
local JVM identifiers.
.RE
.TP
\f[I]hostid\f[R]
The identifier of the host for which the process report should be
generated.
The \f[V]hostid\f[R] can include optional components that indicate the
communications protocol, port number, and other implementation specific
data.
See \f[B]Host Identifier\f[R].
.TP
\f[V]-help\f[R]
Displays the help message for the \f[V]jps\f[R] command.
.SH DESCRIPTION
.PP
The \f[V]jps\f[R] command lists the instrumented Java HotSpot VMs on the
target system.
The command is limited to reporting information on JVMs for which it has
the access permissions.
.PP
If the \f[V]jps\f[R] command is run without specifying a
\f[V]hostid\f[R], then it searches for instrumented JVMs on the local
host.
If started with a \f[V]hostid\f[R], then it searches for JVMs on the
indicated host, using the specified protocol and port.
A \f[V]jstatd\f[R] process is assumed to be running on the target host.
.PP
The \f[V]jps\f[R] command reports the local JVM identifier, or
\f[V]lvmid\f[R], for each instrumented JVM found on the target system.
The \f[V]lvmid\f[R] is typically, but not necessarily, the operating
system\[aq]s process identifier for the JVM process.
With no options, the \f[V]jps\f[R] command lists each Java
application\[aq]s \f[V]lvmid\f[R] followed by the short form of the
application\[aq]s class name or jar file name.
The short form of the class name or JAR file name omits the class\[aq]s
package information or the JAR files path information.
.PP
The \f[V]jps\f[R] command uses the Java launcher to find the class name
and arguments passed to the main method.
If the target JVM is started with a custom launcher, then the class or
JAR file name, and the arguments to the \f[V]main\f[R] method aren\[aq]t
available.
In this case, the \f[V]jps\f[R] command outputs the string
\f[V]Unknown\f[R] for the class name, or JAR file name, and for the
arguments to the \f[V]main\f[R] method.
.PP
The list of JVMs produced by the \f[V]jps\f[R] command can be limited by
the permissions granted to the principal running the command.
The command lists only the JVMs for which the principal has access
rights as determined by operating system-specific access control
mechanisms.
.SH HOST IDENTIFIER
.PP
The host identifier, or \f[V]hostid\f[R], is a string that indicates the
target system.
The syntax of the \f[V]hostid\f[R] string corresponds to the syntax of a
URI:
.RS
.PP
[\f[I]protocol\f[R]\f[V]:\f[R]][[\f[V]//\f[R]]\f[I]hostname\f[R]][\f[V]:\f[R]\f[I]port\f[R]][\f[V]/\f[R]\f[I]servername\f[R]]
.RE
.TP
\f[I]protocol\f[R]
The communications protocol.
If the \f[I]protocol\f[R] is omitted and a \f[I]hostname\f[R] isn\[aq]t
specified, then the default protocol is a platform-specific, optimized,
local protocol.
If the protocol is omitted and a host name is specified, then the
default protocol is \f[V]rmi\f[R].
.TP
\f[I]hostname\f[R]
A host name or IP address that indicates the target host.
If you omit the \f[I]hostname\f[R] parameter, then the target host is
the local host.
.TP
\f[I]port\f[R]
The default port for communicating with the remote server.
If the \f[I]hostname\f[R] parameter is omitted or the \f[I]protocol\f[R]
parameter specifies an optimized, local protocol, then the
\f[I]port\f[R] parameter is ignored.
Otherwise, treatment of the \f[I]port\f[R] parameter is
implementation-specific.
For the default \f[V]rmi\f[R] protocol, the \f[I]port\f[R] parameter
indicates the port number for the \f[V]rmiregistry\f[R] on the remote
host.
If the \f[I]port\f[R] parameter is omitted, and the \f[I]protocol\f[R]
parameter indicates \f[V]rmi\f[R], then the default
\f[V]rmiregistry\f[R] port (\f[V]1099\f[R]) is used.
.TP
\f[I]servername\f[R]
The treatment of this parameter depends on the implementation.
For the optimized, local protocol, this field is ignored.
For the \f[V]rmi\f[R] protocol, this parameter is a string that
represents the name of the RMI remote object on the remote host.
See the \f[B]jstatd\f[R] command \f[V]-n\f[R] option.
.SH OUTPUT FORMAT OF THE JPS COMMAND
.PP
The output of the \f[V]jps\f[R] command has the following pattern:
.RS
.PP
\f[I]lvmid\f[R] [ [ \f[I]classname\f[R] | \f[I]JARfilename\f[R] |
\f[V]\[dq]Unknown\[dq]\f[R]] [ \f[I]arg\f[R]* ] [ \f[I]jvmarg\f[R]* ] ]
.RE
.PP
All output tokens are separated by white space.
An \f[V]arg\f[R] value that includes embedded white space introduces
ambiguity when attempting to map arguments to their actual positional
parameters.
.PP
\f[B]Note:\f[R]
.PP
It\[aq]s recommended that you don\[aq]t write scripts to parse
\f[V]jps\f[R] output because the format might change in future releases.
If you write scripts that parse \f[V]jps\f[R] output, then expect to
modify them for future releases of this tool.
.SH EXAMPLES
.PP
This section provides examples of the \f[V]jps\f[R] command.
.PP
List the instrumented JVMs on the local host:
.IP
.nf
\f[CB]
jps
18027 Java2Demo.JAR
18032 jps
18005 jstat
\f[R]
.fi
.PP
The following example lists the instrumented JVMs on a remote host.
This example assumes that the \f[V]jstat\f[R] server and either the its
internal RMI registry or a separate external \f[V]rmiregistry\f[R]
process are running on the remote host on the default port (port
\f[V]1099\f[R]).
It also assumes that the local host has appropriate permissions to
access the remote host.
This example includes the \f[V]-l\f[R] option to output the long form of
the class names or JAR file names.
.IP
.nf
\f[CB]
jps -l remote.domain
3002 /opt/jdk1.7.0/demo/jfc/Java2D/Java2Demo.JAR
2857 sun.tools.jstatd.jstatd
\f[R]
.fi
.PP
The following example lists the instrumented JVMs on a remote host with
a nondefault port for the RMI registry.
This example assumes that the \f[V]jstatd\f[R] server, with an internal
RMI registry bound to port \f[V]2002\f[R], is running on the remote
host.
This example also uses the \f[V]-m\f[R] option to include the arguments
passed to the \f[V]main\f[R] method of each of the listed Java
applications.
.IP
.nf
\f[CB]
jps -m remote.domain:2002
3002 /opt/jdk1.7.0/demo/jfc/Java2D/Java2Demo.JAR
3102 sun.tools.jstatd.jstatd -p 2002
\f[R]
.fi
