import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'safety_guidelines.dart';

class SafetyIncident {
  final String id;
  final String sessionId;
  final String counselorName;
  final String userMessage;
  final String incidentType;
  final DateTime timestamp;
  final String response;

  SafetyIncident({
    required this.id,
    required this.sessionId,
    required this.counselorN<PERSON>,
    required this.userMessage,
    required this.incidentType,
    required this.timestamp,
    required this.response,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'sessionId': sessionId,
    'counselorName': counselorName,
    'userMessage': userMessage,
    'incidentType': incidentType,
    'timestamp': timestamp.millisecondsSinceEpoch,
    'response': response,
  };

  factory SafetyIncident.fromJson(Map<String, dynamic> json) => SafetyIncident(
    id: json['id'],
    sessionId: json['sessionId'],
    counselorName: json['counselorName'],
    userMessage: json['userMessage'],
    incidentType: json['incidentType'],
    timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp']),
    response: json['response'],
  );
}

class SafetyService {
  static const String _incidentsKey = 'safety_incidents';
  static const String _dailyUsageKey = 'daily_usage';

  /// Log a safety incident for review
  static Future<void> logSafetyIncident({
    required String sessionId,
    required String counselorName,
    required String userMessage,
    required String incidentType,
    required String response,
  }) async {
    final incident = SafetyIncident(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      sessionId: sessionId,
      counselorName: counselorName,
      userMessage: userMessage,
      incidentType: incidentType,
      timestamp: DateTime.now(),
      response: response,
    );

    final prefs = await SharedPreferences.getInstance();
    final incidents = await getAllIncidents();
    incidents.add(incident);

    // Keep only last 50 incidents to manage storage
    final incidentsToKeep = incidents.length > 50 
        ? incidents.sublist(incidents.length - 50)
        : incidents;

    final incidentsJson = incidentsToKeep
        .map((incident) => json.encode(incident.toJson()))
        .toList();
    
    await prefs.setStringList(_incidentsKey, incidentsJson);
  }

  /// Get all logged safety incidents
  static Future<List<SafetyIncident>> getAllIncidents() async {
    final prefs = await SharedPreferences.getInstance();
    final incidentsJson = prefs.getStringList(_incidentsKey) ?? [];
    
    return incidentsJson
        .map((incidentStr) => SafetyIncident.fromJson(json.decode(incidentStr)))
        .toList();
  }

  /// Check if user has exceeded daily session limits
  static Future<bool> checkDailyLimits() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final dateKey = '${today.year}-${today.month}-${today.day}';
    
    final dailyUsage = prefs.getStringList('${_dailyUsageKey}_$dateKey') ?? [];
    return dailyUsage.length >= 3; // Max 3 sessions per day
  }

  /// Record a new session start
  static Future<void> recordSessionStart(String sessionId) async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final dateKey = '${today.year}-${today.month}-${today.day}';
    
    final dailyUsage = prefs.getStringList('${_dailyUsageKey}_$dateKey') ?? [];
    dailyUsage.add(sessionId);
    
    await prefs.setStringList('${_dailyUsageKey}_$dateKey', dailyUsage);
  }

  /// Get crisis resources based on message content
  static List<String> getCrisisResourcesForMessage(String message) {
    final lowerMessage = message.toLowerCase();
    
    if (lowerMessage.contains('suicide') || 
        lowerMessage.contains('kill myself') ||
        lowerMessage.contains('want to die')) {
      return SafetyGuidelines.getCrisisResources('suicide');
    }
    
    if (lowerMessage.contains('abuse') || 
        lowerMessage.contains('hitting') ||
        lowerMessage.contains('hurting me')) {
      return SafetyGuidelines.getCrisisResources('abuse');
    }
    
    if (lowerMessage.contains('drugs') || 
        lowerMessage.contains('overdose') ||
        lowerMessage.contains('substance')) {
      return SafetyGuidelines.getCrisisResources('substance');
    }
    
    return SafetyGuidelines.getCrisisResources('general');
  }

  /// Check if message requires immediate intervention
  static bool requiresImmediateIntervention(String message) {
    final urgentIndicators = [
      'going to kill myself',
      'have a plan to',
      'tonight i will',
      'pills ready',
      'rope ready',
      'gun ready',
      'going to hurt',
      'going to kill them',
      'right now i am',
      'about to take',
    ];

    final lowerMessage = message.toLowerCase();
    return urgentIndicators.any((indicator) => lowerMessage.contains(indicator));
  }

  /// Get immediate intervention response
  static String getImmediateInterventionResponse() {
    return '''🚨 IMMEDIATE SAFETY CONCERN 🚨

I'm very concerned about your immediate safety. Please:

1. Call 911 if you're in immediate danger
2. Call 988 (Suicide & Crisis Lifeline) right now
3. Go to your nearest emergency room
4. Call a trusted friend or family member to stay with you

You don't have to go through this alone. Help is available right now.

988 Suicide & Crisis Lifeline: Call or text 988
Crisis Text Line: Text HOME to 741741
National Suicide Prevention Lifeline: **************

Please reach out for help immediately. Your life matters.''';
  }

  /// Validate that AI response follows safety guidelines
  static bool validateAIResponse(String response) {
    final prohibitedContent = [
      'here\'s how to do emdr',
      'try this exposure exercise',
      'let\'s process that trauma',
      'here\'s how to hurt',
      'you should take medication',
      'i recommend this drug',
      'here\'s how to manipulate',
      'you should leave your therapist',
      'ignore what your doctor said',
    ];

    final lowerResponse = response.toLowerCase();
    return !prohibitedContent.any((content) => lowerResponse.contains(content));
  }

  /// Get safety reminder message
  static String getSafetyReminder() {
    return '''Important Reminder: I'm an AI counselor providing supportive conversation and general guidance. I'm not a replacement for professional mental health treatment, medical advice, or crisis intervention.

For serious mental health concerns, trauma processing, or crisis situations, please connect with:
• A licensed therapist or counselor
• Your healthcare provider
• Crisis hotlines (988 for immediate help)
• Emergency services (911) if in immediate danger

I'm here to support you, but professional help is essential for complex mental health needs.''';
  }

  /// Check if user is a minor based on conversation content
  static bool detectMinorUser(List<String> recentMessages) {
    final minorIndicators = [
      'i\'m 13', 'i\'m 14', 'i\'m 15', 'i\'m 16', 'i\'m 17',
      'in high school', 'my parents', 'my mom said', 'my dad said',
      'at school today', 'my teacher', 'homework', 'my grades',
    ];

    final combinedMessages = recentMessages.join(' ').toLowerCase();
    return minorIndicators.any((indicator) => combinedMessages.contains(indicator));
  }

  /// Get minor-specific safety response
  static String getMinorSafetyResponse() {
    return '''I notice you might be under 18. It's important that you know:

• If you're experiencing abuse, please tell a trusted adult like a teacher, school counselor, or family member
• For serious concerns, I encourage involving your parents/guardians when safe to do so
• School counselors are great resources for support
• Crisis resources are available 24/7: Text HOME to 741741

Your safety and wellbeing are important. Please reach out to trusted adults in your life for support with serious concerns.''';
  }

  /// Clear old safety data (for privacy)
  static Future<void> clearOldData() async {
    final prefs = await SharedPreferences.getInstance();
    final incidents = await getAllIncidents();
    
    // Keep only incidents from last 30 days
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    final recentIncidents = incidents
        .where((incident) => incident.timestamp.isAfter(thirtyDaysAgo))
        .toList();
    
    final incidentsJson = recentIncidents
        .map((incident) => json.encode(incident.toJson()))
        .toList();
    
    await prefs.setStringList(_incidentsKey, incidentsJson);
  }
}
