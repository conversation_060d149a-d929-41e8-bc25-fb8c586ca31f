.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JDB" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jdb - find and fix bugs in Java platform programs
.SH SYNOPSIS
.PP
\f[V]jdb\f[R] [\f[I]options\f[R]] [\f[I]classname\f[R]]
[\f[I]arguments\f[R]]
.TP
\f[I]options\f[R]
This represents the \f[V]jdb\f[R] command-line options.
See \f[B]Options for the jdb command\f[R].
.TP
\f[I]classname\f[R]
This represents the name of the main class to debug.
.TP
\f[I]arguments\f[R]
This represents the arguments that are passed to the \f[V]main()\f[R]
method of the class.
.SH DESCRIPTION
.PP
The Java Debugger (JDB) is a simple command-line debugger for Java
classes.
The \f[V]jdb\f[R] command and its options call the JDB.
The \f[V]jdb\f[R] command demonstrates the Java Platform Debugger
Architecture and provides inspection and debugging of a local or remote
JVM.
.SH START A JDB SESSION
.PP
There are many ways to start a JDB session.
The most frequently used way is to have the JDB launch a new JVM with
the main class of the application to be debugged.
Do this by substituting the \f[V]jdb\f[R] command for the \f[V]java\f[R]
command in the command line.
For example, if your application\[aq]s main class is \f[V]MyClass\f[R],
then use the following command to debug it under the JDB:
.RS
.PP
\f[V]jdb MyClass\f[R]
.RE
.PP
When started this way, the \f[V]jdb\f[R] command calls a second JVM with
the specified parameters, loads the specified class, and stops the JVM
before executing that class\[aq]s first instruction.
.PP
Another way to use the \f[V]jdb\f[R] command is by attaching it to a JVM
that\[aq]s already running.
Syntax for starting a JVM to which the \f[V]jdb\f[R] command attaches
when the JVM is running is as follows.
This loads in-process debugging libraries and specifies the kind of
connection to be made.
.RS
.PP
\f[V]java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n MyClass\f[R]
.RE
.PP
You can then attach the \f[V]jdb\f[R] command to the JVM with the
following command:
.RS
.PP
\f[V]jdb -attach 8000\f[R]
.RE
.PP
8000 is the address of the running JVM.
.PP
The \f[V]MyClass\f[R] argument isn\[aq]t specified in the \f[V]jdb\f[R]
command line in this case because the \f[V]jdb\f[R] command is
connecting to an existing JVM instead of launching a new JVM.
.PP
There are many other ways to connect the debugger to a JVM, and all of
them are supported by the \f[V]jdb\f[R] command.
The Java Platform Debugger Architecture has additional documentation on
these connection options.
.SH BREAKPOINTS
.PP
Breakpoints can be set in the JDB at line numbers or at the first
instruction of a method, for example:
.IP \[bu] 2
The command \f[V]stop at MyClass:22\f[R] sets a breakpoint at the first
instruction for line 22 of the source file containing \f[V]MyClass\f[R].
.IP \[bu] 2
The command \f[V]stop in java.lang.String.length\f[R] sets a breakpoint
at the beginning of the method \f[V]java.lang.String.length\f[R].
.IP \[bu] 2
The command \f[V]stop in MyClass.<clinit>\f[R] uses \f[V]<clinit>\f[R]
to identify the static initialization code for \f[V]MyClass\f[R].
.PP
When a method is overloaded, you must also specify its argument types so
that the proper method can be selected for a breakpoint.
For example, \f[V]MyClass.myMethod(int,java.lang.String)\f[R] or
\f[V]MyClass.myMethod()\f[R].
.PP
The \f[V]clear\f[R] command removes breakpoints using the following
syntax: \f[V]clear MyClass:45\f[R].
Using the \f[V]clear\f[R] or \f[V]stop\f[R] command with no argument
displays a list of all breakpoints currently set.
The \f[V]cont\f[R] command continues execution.
.SH STEPPING
.PP
The \f[V]step\f[R] command advances execution to the next line whether
it\[aq]s in the current stack frame or a called method.
The \f[V]next\f[R] command advances execution to the next line in the
current stack frame.
.SH EXCEPTIONS
.PP
When an exception occurs for which there isn\[aq]t a \f[V]catch\f[R]
statement anywhere in the throwing thread\[aq]s call stack, the JVM
typically prints an exception trace and exits.
When running under the JDB, however, control returns to the JDB at the
offending throw.
You can then use the \f[V]jdb\f[R] command to diagnose the cause of the
exception.
.PP
Use the \f[V]catch\f[R] command to cause the debugged application to
stop at other thrown exceptions, for example:
\f[V]catch java.io.FileNotFoundException\f[R] or \f[V]catch\f[R]
\f[V]mypackage.BigTroubleException\f[R].
Any exception that\[aq]s an instance of the specified class or subclass
stops the application at the point where the exception is thrown.
.PP
The \f[V]ignore\f[R] command negates the effect of an earlier
\f[V]catch\f[R] command.
The \f[V]ignore\f[R] command doesn\[aq]t cause the debugged JVM to
ignore specific exceptions, but only to ignore the debugger.
.SH OPTIONS FOR THE JDB COMMAND
.PP
When you use the \f[V]jdb\f[R] command instead of the \f[V]java\f[R]
command on the command line, the \f[V]jdb\f[R] command accepts many of
the same options as the \f[V]java\f[R] command.
.PP
The following options are accepted by the \f[V]jdb\f[R] command:
.TP
\f[V]-help\f[R]
Displays a help message.
.TP
\f[V]-sourcepath\f[R] \f[I]dir1\f[R]\f[V]:\f[R]\f[I]dir2\f[R]\f[V]:\f[R]...
Uses the specified path to search for source files in the specified
path.
If this option is not specified, then use the default path of dot
(\f[V].\f[R]).
.TP
\f[V]-attach\f[R] \f[I]address\f[R]
Attaches the debugger to a running JVM with the default connection
mechanism.
.TP
\f[V]-listen\f[R] \f[I]address\f[R]
Waits for a running JVM to connect to the specified address with a
standard connector.
.TP
\f[V]-listenany\f[R]
Waits for a running JVM to connect at any available address using a
standard connector.
.TP
\f[V]-launch\f[R]
Starts the debugged application immediately upon startup of the
\f[V]jdb\f[R] command.
The \f[V]-launch\f[R] option removes the need for the \f[V]run\f[R]
command.
The debugged application is launched and then stopped just before the
initial application class is loaded.
At that point, you can set any necessary breakpoints and use the
\f[V]cont\f[R] command to continue execution.
.TP
\f[V]-listconnectors\f[R]
Lists the connectors available in this JVM.
.TP
\f[V]-connect\f[R] \f[I]connector-name\f[R]\f[V]:\f[R]\f[I]name1\f[R]\f[V]=\f[R]\f[I]value1\f[R]....
Connects to the target JVM with the named connector and listed argument
values.
.TP
\f[V]-dbgtrace\f[R] [\f[I]flags\f[R]]
Prints information for debugging the \f[V]jdb\f[R] command.
.TP
\f[V]-tclient\f[R]
Runs the application in the Java HotSpot VM client.
.TP
\f[V]-trackallthreads\f[R]
Track all threads as they are created, including virtual threads.
See \f[B]Working With Virtual Threads\f[R] below.
.TP
\f[V]-tserver\f[R]
Runs the application in the Java HotSpot VM server.
.TP
\f[V]-J\f[R]\f[I]option\f[R]
Passes \f[I]option\f[R] to the JDB JVM, where option is one of the
options described on the reference page for the Java application
launcher.
For example, \f[V]-J-Xms48m\f[R] sets the startup memory to 48 MB.
See \f[I]Overview of Java Options\f[R] in \f[B]java\f[R].
.PP
The following options are forwarded to the debuggee process:
.TP
\f[V]-R\f[R]\f[I]option\f[R]
Passes \f[I]option\f[R] to the debuggee JVM, where option is one of the
options described on the reference page for the Java application
launcher.
For example, \f[V]-R-Xms48m\f[R] sets the startup memory to 48 MB.
See \f[I]Overview of Java Options\f[R] in \f[B]java\f[R].
.TP
\f[V]-v\f[R] or \f[V]-verbose\f[R][\f[V]:\f[R]\f[I]class\f[R]|\f[V]gc\f[R]|\f[V]jni\f[R]]
Turns on the verbose mode.
.TP
\f[V]-D\f[R]\f[I]name\f[R]\f[V]=\f[R]\f[I]value\f[R]
Sets a system property.
.TP
\f[V]-classpath\f[R] \f[I]dir\f[R]
Lists directories separated by colons in which to look for classes.
.TP
\f[V]-X\f[R] \f[I]option\f[R]
A nonstandard target JVM option.
.PP
Other options are supported to provide alternate mechanisms for
connecting the debugger to the JVM that it\[aq]s to debug.
.SH WORKING WITH VIRTUAL THREADS
.PP
Often virtual theads are created in such large numbers and frequency
that they can overwhelm a debugger.
For this reason by default JDB does not keep track of virtual threads as
they are created.
It will only keep track of virtual threads that an event has arrived on,
such as a breakpoint event.
The \f[V]-trackallthreads\f[R] option can be used to make JDB track all
virtual threads as they are created.
.PP
When JDB first connects, it requests a list of all known threads from
the Debug Agent.
By default the debug agent does not return any virtual threads in this
list, once again because the list could be so large that it overwhelms
the debugger.
The Debug Agent has an \f[V]includevirtualthreads\f[R] option that can
be enabled to change this behavior so all known virtual threads will be
included in the list.
The JDB \f[V]-trackallthreads\f[R] option will cause JDB to
automatically enable the Debug Agent\[aq]s
\f[V]includevirtualthreads\f[R] option when JDB launches an application
to debug.
However, keep in mind that the Debug Agent may not know about any
virtual threads that were created before JDB attached to the debugged
application.
