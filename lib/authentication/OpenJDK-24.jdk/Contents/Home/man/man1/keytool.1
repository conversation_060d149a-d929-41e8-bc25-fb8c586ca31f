'\" t
.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "KEYTOOL" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
keytool - a key and certificate management utility
.SH SYNOPSIS
.PP
\f[V]keytool\f[R] [\f[I]commands\f[R]]
.TP
\f[I]commands\f[R]
Commands for \f[V]keytool\f[R] include the following:
.RS
.IP \[bu] 2
\f[V]-certreq\f[R]: Generates a certificate request
.IP \[bu] 2
\f[V]-changealias\f[R]: Changes an entry\[aq]s alias
.IP \[bu] 2
\f[V]-delete\f[R]: Deletes an entry
.IP \[bu] 2
\f[V]-exportcert\f[R]: Exports certificate
.IP \[bu] 2
\f[V]-genkeypair\f[R]: Generates a key pair
.IP \[bu] 2
\f[V]-genseckey\f[R]: Generates a secret key
.IP \[bu] 2
\f[V]-gencert\f[R]: Generates a certificate from a certificate request
.IP \[bu] 2
\f[V]-importcert\f[R]: Imports a certificate or a certificate chain
.IP \[bu] 2
\f[V]-importpass\f[R]: Imports a password
.IP \[bu] 2
\f[V]-importkeystore\f[R]: Imports one or all entries from another
keystore
.IP \[bu] 2
\f[V]-keypasswd\f[R]: Changes the key password of an entry
.IP \[bu] 2
\f[V]-list\f[R]: Lists entries in a keystore
.IP \[bu] 2
\f[V]-printcert\f[R]: Prints the content of a certificate
.IP \[bu] 2
\f[V]-printcertreq\f[R]: Prints the content of a certificate request
.IP \[bu] 2
\f[V]-printcrl\f[R]: Prints the content of a Certificate Revocation List
(CRL) file
.IP \[bu] 2
\f[V]-storepasswd\f[R]: Changes the store password of a keystore
.IP \[bu] 2
\f[V]-showinfo\f[R]: Displays security-related information
.IP \[bu] 2
\f[V]-version\f[R]: Prints the program version
.PP
See \f[B]Commands and Options\f[R] for a description of these commands
with their options.
.RE
.SH DESCRIPTION
.PP
The \f[V]keytool\f[R] command is a key and certificate management
utility.
It enables users to administer their own public/private key pairs and
associated certificates for use in self-authentication (where a user
authenticates themselves to other users and services) or data integrity
and authentication services, by using digital signatures.
The \f[V]keytool\f[R] command also enables users to cache the public
keys (in the form of certificates) of their communicating peers.
.PP
A certificate is a digitally signed statement from one entity (person,
company, and so on), which says that the public key (and some other
information) of some other entity has a particular value.
When data is digitally signed, the signature can be verified to check
the data integrity and authenticity.
Integrity means that the data hasn\[aq]t been modified or tampered with,
and authenticity means that the data comes from the individual who
claims to have created and signed it.
.PP
The \f[V]keytool\f[R] command also enables users to administer secret
keys and passphrases used in symmetric encryption and decryption (Data
Encryption Standard).
It can also display other security-related information.
.PP
The \f[V]keytool\f[R] command stores the keys and certificates in a
keystore.
.PP
The \f[V]keytool\f[R] command uses the
\f[V]jdk.certpath.disabledAlgorithms\f[R] and
\f[V]jdk.security.legacyAlgorithms\f[R] security properties to determine
which algorithms are considered a security risk.
It emits warnings when disabled or legacy algorithms are being used.
The \f[V]jdk.certpath.disabledAlgorithms\f[R] and
\f[V]jdk.security.legacyAlgorithms\f[R] security properties are defined
in the \f[V]java.security\f[R] file (located in the JDK\[aq]s
\f[V]$JAVA_HOME/conf/security\f[R] directory).
.SH COMMAND AND OPTION NOTES
.PP
The following notes apply to the descriptions in \f[B]Commands and
Options\f[R]:
.IP \[bu] 2
All command and option names are preceded by a hyphen sign
(\f[V]-\f[R]).
.IP \[bu] 2
Only one command can be provided.
.IP \[bu] 2
Options for each command can be provided in any order.
.IP \[bu] 2
There are two kinds of options, one is single-valued which should be
only provided once.
If a single-valued option is provided multiple times, the value of the
last one is used.
The other type is multi-valued, which can be provided multiple times and
all values are used.
The only multi-valued option currently supported is the \f[V]-ext\f[R]
option used to generate X.509v3 certificate extensions.
.IP \[bu] 2
All items not italicized or in braces ({ }) or brackets ([ ]) are
required to appear as is.
.IP \[bu] 2
Braces surrounding an option signify that a default value is used when
the option isn\[aq]t specified on the command line.
Braces are also used around the \f[V]-v\f[R], \f[V]-rfc\f[R], and
\f[V]-J\f[R] options, which have meaning only when they appear on the
command line.
They don\[aq]t have any default values.
.IP \[bu] 2
Brackets surrounding an option signify that the user is prompted for the
values when the option isn\[aq]t specified on the command line.
For the \f[V]-keypass\f[R] option, if you don\[aq]t specify the option
on the command line, then the \f[V]keytool\f[R] command first attempts
to use the keystore password to recover the private/secret key.
If this attempt fails, then the \f[V]keytool\f[R] command prompts you
for the private/secret key password.
.IP \[bu] 2
Items in italics (option values) represent the actual values that must
be supplied.
For example, here is the format of the \f[V]-printcert\f[R] command:
.RS 2
.RS
.PP
\f[V]keytool -printcert\f[R] {\f[V]-file\f[R] \f[I]cert_file\f[R]}
{\f[V]-v\f[R]}
.RE
.PP
When you specify a \f[V]-printcert\f[R] command, replace
\f[I]cert_file\f[R] with the actual file name, as follows:
\f[V]keytool -printcert -file VScert.cer\f[R]
.RE
.IP \[bu] 2
Option values must be enclosed in quotation marks when they contain a
blank (space).
.SH COMMANDS AND OPTIONS
.PP
The keytool commands and their options can be grouped by the tasks that
they perform.
.PP
\f[B]Commands for Creating or Adding Data to the Keystore\f[R]:
.IP \[bu] 2
\f[V]-gencert\f[R]
.IP \[bu] 2
\f[V]-genkeypair\f[R]
.IP \[bu] 2
\f[V]-genseckey\f[R]
.IP \[bu] 2
\f[V]-importcert\f[R]
.IP \[bu] 2
\f[V]-importpass\f[R]
.PP
\f[B]Commands for Importing Contents from Another Keystore\f[R]:
.IP \[bu] 2
\f[V]-importkeystore\f[R]
.PP
\f[B]Commands for Generating a Certificate Request\f[R]:
.IP \[bu] 2
\f[V]-certreq\f[R]
.PP
\f[B]Commands for Exporting Data\f[R]:
.IP \[bu] 2
\f[V]-exportcert\f[R]
.PP
\f[B]Commands for Displaying Data\f[R]:
.IP \[bu] 2
\f[V]-list\f[R]
.IP \[bu] 2
\f[V]-printcert\f[R]
.IP \[bu] 2
\f[V]-printcertreq\f[R]
.IP \[bu] 2
\f[V]-printcrl\f[R]
.PP
\f[B]Commands for Managing the Keystore\f[R]:
.IP \[bu] 2
\f[V]-storepasswd\f[R]
.IP \[bu] 2
\f[V]-keypasswd\f[R]
.IP \[bu] 2
\f[V]-delete\f[R]
.IP \[bu] 2
\f[V]-changealias\f[R]
.PP
\f[B]Commands for Displaying Security-related Information\f[R]:
.IP \[bu] 2
\f[V]-showinfo\f[R]
.PP
\f[B]Commands for Displaying Program Version\f[R]:
.IP \[bu] 2
\f[V]-version\f[R]
.SH COMMANDS FOR CREATING OR ADDING DATA TO THE KEYSTORE
.TP
\f[V]-gencert\f[R]
The following are the available options for the \f[V]-gencert\f[R]
command:
.RS
.IP \[bu] 2
{\f[V]-rfc\f[R]}: Output in RFC (Request For Comment) style
.IP \[bu] 2
{\f[V]-infile\f[R] \f[I]infile\f[R]}: Input file name
.IP \[bu] 2
{\f[V]-outfile\f[R] \f[I]outfile\f[R]}: Output file name
.IP \[bu] 2
{\f[V]-alias\f[R] \f[I]alias\f[R]}: Alias name of the entry to process
.IP \[bu] 2
{\f[V]-sigalg\f[R] \f[I]sigalg\f[R]}: Signature algorithm name
.IP \[bu] 2
{\f[V]-dname\f[R] \f[I]dname\f[R]}: Distinguished name
.IP \[bu] 2
{\f[V]-startdate\f[R] \f[I]startdate\f[R]}: Certificate validity start
date and time
.IP \[bu] 2
{\f[V]-ext\f[R] \f[I]ext\f[R]}*: X.509 extension
.IP \[bu] 2
{\f[V]-validity\f[R] \f[I]days\f[R]}: Validity number of days
.IP \[bu] 2
[\f[V]-keypass\f[R] \f[I]arg\f[R]]: Key password
.IP \[bu] 2
{\f[V]-keystore\f[R] \f[I]keystore\f[R]}: Keystore name
.IP \[bu] 2
[\f[V]-storepass\f[R] \f[I]arg\f[R]]: Keystore password
.IP \[bu] 2
{\f[V]-storetype\f[R] \f[I]type\f[R]}: Keystore type
.IP \[bu] 2
{\f[V]-providername\f[R] \f[I]name\f[R]}: Provider name
.IP \[bu] 2
{\f[V]-addprovider\f[R] \f[I]name\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Adds a security provider by name (such as SunPKCS11)
with an optional configure argument.
The value of the security provider is the name of a security provider
that is defined in a module.
.RS 2
.PP
For example,
.RS
.PP
\f[V]keytool -addprovider SunPKCS11 -providerarg some.cfg ...\f[R]
.RE
.PP
\f[B]Note:\f[R]
.PP
For compatibility reasons, the SunPKCS11 provider can still be loaded
with \f[V]-providerclass sun.security.pkcs11.SunPKCS11\f[R] even if it
is now defined in a module.
This is the only module included in the JDK that needs a configuration,
and therefore the most widely used with the \f[V]-providerclass\f[R]
option.
For legacy security providers located on classpath and loaded by
reflection, \f[V]-providerclass\f[R] should still be used.
.RE
.IP \[bu] 2
{\f[V]-providerclass\f[R] \f[I]class\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by fully qualified class name
with an optional configure argument.
.RS 2
.PP
For example, if \f[V]MyProvider\f[R] is a legacy provider loaded via
reflection,
.RS
.PP
\f[V]keytool -providerclass com.example.MyProvider ...\f[R]
.RE
.RE
.IP \[bu] 2
{\f[V]-providerpath\f[R] \f[I]list\f[R]}: Provider classpath
.IP \[bu] 2
{\f[V]-v\f[R]}: Verbose output
.IP \[bu] 2
{\f[V]-protected\f[R]}: Password provided through a protected mechanism
.PP
Use the \f[V]-gencert\f[R] command to generate a certificate as a
response to a certificate request file (which can be created by the
\f[V]keytool -certreq\f[R] command).
The command reads the request either from \f[I]infile\f[R] or, if
omitted, from the standard input, signs it by using the alias\[aq]s
private key, and outputs the X.509 certificate into either
\f[I]outfile\f[R] or, if omitted, to the standard output.
When \f[V]-rfc\f[R] is specified, the output format is Base64-encoded
PEM; otherwise, a binary DER is created.
.PP
The \f[V]-sigalg\f[R] value specifies the algorithm that should be used
to sign the certificate.
The \f[I]startdate\f[R] argument is the start time and date that the
certificate is valid.
The \f[I]days\f[R] argument tells the number of days for which the
certificate should be considered valid.
.PP
When \f[I]dname\f[R] is provided, it is used as the subject of the
generated certificate.
Otherwise, the one from the certificate request is used.
.PP
The \f[V]-ext\f[R] value shows what X.509 extensions will be embedded in
the certificate.
Read \f[B]Common Command Options\f[R] for the grammar of \f[V]-ext\f[R].
.PP
The \f[V]-gencert\f[R] option enables you to create certificate chains.
The following example creates a certificate, \f[V]e1\f[R], that contains
three certificates in its certificate chain.
.PP
The following commands creates four key pairs named \f[V]ca\f[R],
\f[V]ca1\f[R], \f[V]ca2\f[R], and \f[V]e1\f[R]:
.IP
.nf
\f[CB]
keytool -alias ca -dname CN=CA -genkeypair -keyalg rsa
keytool -alias ca1 -dname CN=CA -genkeypair -keyalg rsa
keytool -alias ca2 -dname CN=CA -genkeypair -keyalg rsa
keytool -alias e1 -dname CN=E1 -genkeypair -keyalg rsa
\f[R]
.fi
.PP
The following two commands create a chain of signed certificates;
\f[V]ca\f[R] signs \f[V]ca1\f[R] and \f[V]ca1\f[R] signs \f[V]ca2\f[R],
all of which are self-issued:
.IP
.nf
\f[CB]
keytool -alias ca1 -certreq |
    keytool -alias ca -gencert -ext san=dns:ca1 |
    keytool -alias ca1 -importcert

keytool -alias ca2 -certreq |
    keytool -alias ca1 -gencert -ext san=dns:ca2 |
    keytool -alias ca2 -importcert
\f[R]
.fi
.PP
The following command creates the certificate \f[V]e1\f[R] and stores it
in the \f[V]e1.cert\f[R] file, which is signed by \f[V]ca2\f[R].
As a result, \f[V]e1\f[R] should contain \f[V]ca\f[R], \f[V]ca1\f[R],
and \f[V]ca2\f[R] in its certificate chain:
.RS
.PP
\f[V]keytool -alias e1 -certreq | keytool -alias ca2 -gencert > e1.cert\f[R]
.RE
.RE
.TP
\f[V]-genkeypair\f[R]
The following are the available options for the \f[V]-genkeypair\f[R]
command:
.RS
.IP \[bu] 2
{\f[V]-alias\f[R] \f[I]alias\f[R]}: Alias name of the entry to process
.IP \[bu] 2
\f[V]-keyalg\f[R] \f[I]alg\f[R]: Key algorithm name
.IP \[bu] 2
{\f[V]-keysize\f[R] \f[I]size\f[R]}: Key bit size
.IP \[bu] 2
{\f[V]-groupname\f[R] \f[I]name\f[R]}: Group name.
For example, an Elliptic Curve name.
.IP \[bu] 2
{\f[V]-sigalg\f[R] \f[I]alg\f[R]}: Signature algorithm name
.IP \[bu] 2
{\f[V]-signer\f[R] \f[I]alias\f[R]}: Signer alias
.IP \[bu] 2
[\f[V]-signerkeypass\f[R] \f[I]arg\f[R]]: Signer key password
.IP \[bu] 2
[\f[V]-dname\f[R] \f[I]name\f[R]]: Distinguished name
.IP \[bu] 2
{\f[V]-startdate\f[R] \f[I]date\f[R]}: Certificate validity start date
and time
.IP \[bu] 2
{\f[V]-ext\f[R] \f[I]value\f[R]}*: X.509 extension
.IP \[bu] 2
{\f[V]-validity\f[R] \f[I]days\f[R]}: Validity number of days
.IP \[bu] 2
[\f[V]-keypass\f[R] \f[I]arg\f[R]]: Key password
.IP \[bu] 2
{\f[V]-keystore\f[R] \f[I]keystore\f[R]}: Keystore name
.IP \[bu] 2
[\f[V]-storepass\f[R] \f[I]arg\f[R]]: Keystore password
.IP \[bu] 2
{\f[V]-storetype\f[R] \f[I]type\f[R]}: Keystore type
.IP \[bu] 2
{\f[V]-providername\f[R] \f[I]name\f[R]}: Provider name
.IP \[bu] 2
{\f[V]-addprovider\f[R] \f[I]name\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by name (such as SunPKCS11) with
an optional configure argument.
.IP \[bu] 2
{\f[V]-providerclass\f[R] \f[I]class\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]] }: Add security provider by fully qualified class name
with an optional configure argument.
.IP \[bu] 2
{\f[V]-providerpath\f[R] \f[I]list\f[R]}: Provider classpath
.IP \[bu] 2
{\f[V]-v\f[R]}: Verbose output
.IP \[bu] 2
{\f[V]-protected\f[R]}: Password provided through a protected mechanism
.PP
Use the \f[V]-genkeypair\f[R] command to generate a key pair (a public
key and associated private key).
When the \f[V]-signer\f[R] option is not specified, the public key is
wrapped in an X.509 v3 self-signed certificate and stored as a
single-element certificate chain.
When the \f[V]-signer\f[R] option is specified, a new certificate is
generated and signed by the designated signer and stored as a
multiple-element certificate chain (containing the generated certificate
itself, and the signer\[aq]s certificate chain).
The certificate chain and private key are stored in a new keystore entry
that is identified by its alias.
.PP
The \f[V]-keyalg\f[R] value specifies the algorithm to be used to
generate the key pair.
The \f[V]-keysize\f[R] value specifies the size of each key to be
generated.
The \f[V]-groupname\f[R] value specifies the named group (for example,
the standard or predefined name of an Elliptic Curve) of the key to be
generated.
.PP
When a \f[V]-keysize\f[R] value is provided, it will be used to
initialize a \f[V]KeyPairGenerator\f[R] object using the
\f[V]initialize(int keysize)\f[R] method.
When a \f[V]-groupname\f[R] value is provided, it will be used to
initialize a \f[V]KeyPairGenerator\f[R] object using the
\f[V]initialize(AlgorithmParameterSpec params)\f[R] method where
\f[V]params\f[R] is \f[V]new NamedParameterSpec(groupname)\f[R].
.PP
Only one of \f[V]-groupname\f[R] and \f[V]-keysize\f[R] can be
specified.
If an algorithm has multiple named groups that have the same key size,
the \f[V]-groupname\f[R] option should usually be used.
In this case, if \f[V]-keysize\f[R] is specified, it\[aq]s up to the
security provider to determine which named group is chosen when
generating a key pair.
.PP
The \f[V]-sigalg\f[R] value specifies the algorithm that should be used
to sign the certificate.
This algorithm must be compatible with the \f[V]-keyalg\f[R] value.
.PP
The \f[V]-signer\f[R] value specifies the alias of a
\f[V]PrivateKeyEntry\f[R] for the signer that already exists in the
keystore.
This option is used to sign the certificate with the signer\[aq]s
private key.
This is especially useful for key agreement algorithms (i.e.
the \f[V]-keyalg\f[R] value is \f[V]XDH\f[R], \f[V]X25519\f[R],
\f[V]X448\f[R], or \f[V]DH\f[R]) as these keys cannot be used for
digital signatures, and therefore a self-signed certificate cannot be
created.
.PP
The \f[V]-signerkeypass\f[R] value specifies the password of the
signer\[aq]s private key.
It can be specified if the private key of the signer entry is protected
by a password different from the store password.
.PP
The \f[V]-dname\f[R] value specifies the X.500 Distinguished Name to be
associated with the value of \f[V]-alias\f[R].
If the \f[V]-signer\f[R] option is not specified, the issuer and subject
fields of the self-signed certificate are populated with the specified
distinguished name.
If the \f[V]-signer\f[R] option is specified, the subject field of the
certificate is populated with the specified distinguished name and the
issuer field is populated with the subject field of the signer\[aq]s
certificate.
If a distinguished name is not provided at the command line, then the
user is prompted for one.
.PP
The value of \f[V]-keypass\f[R] is a password used to protect the
private key of the generated key pair.
If a password is not provided, then the user is prompted for it.
If you press the \f[B]Return\f[R] key at the prompt, then the key
password is set to the same password as the keystore password.
The \f[V]-keypass\f[R] value must have at least six characters.
.PP
The value of \f[V]-startdate\f[R] specifies the issue time of the
certificate, also known as the \[dq]Not Before\[dq] value of the X.509
certificate\[aq]s Validity field.
.PP
The option value can be set in one of these two forms:
.PP
([\f[V]+-\f[R]]\f[I]nnn\f[R][\f[V]ymdHMS\f[R]])+
.PP
[\f[I]yyyy\f[R]\f[V]/\f[R]\f[I]mm\f[R]\f[V]/\f[R]\f[I]dd\f[R]]
[\f[I]HH\f[R]\f[V]:\f[R]\f[I]MM\f[R]\f[V]:\f[R]\f[I]SS\f[R]]
.PP
With the first form, the issue time is shifted by the specified value
from the current time.
The value is a concatenation of a sequence of subvalues.
Inside each subvalue, the plus sign (+) means shift forward, and the
minus sign (-) means shift backward.
The time to be shifted is \f[I]nnn\f[R] units of years, months, days,
hours, minutes, or seconds (denoted by a single character of
\f[V]y\f[R], \f[V]m\f[R], \f[V]d\f[R], \f[V]H\f[R], \f[V]M\f[R], or
\f[V]S\f[R] respectively).
The exact value of the issue time is calculated by using the
\f[V]java.util.GregorianCalendar.add(int field, int amount)\f[R] method
on each subvalue, from left to right.
For example, the issue time can be specified by:
.IP
.nf
\f[CB]
Calendar c = new GregorianCalendar();
c.add(Calendar.YEAR, -1);
c.add(Calendar.MONTH, 1);
c.add(Calendar.DATE, -1);
return c.getTime()
\f[R]
.fi
.PP
With the second form, the user sets the exact issue time in two parts,
year/month/day and hour:minute:second (using the local time zone).
The user can provide only one part, which means the other part is the
same as the current date (or time).
The user must provide the exact number of digits shown in the format
definition (padding with 0 when shorter).
When both date and time are provided, there is one (and only one) space
character between the two parts.
The hour should always be provided in 24-hour format.
.PP
When the option isn\[aq]t provided, the start date is the current time.
The option can only be provided one time.
.PP
The value of \f[I]date\f[R] specifies the number of days (starting at
the date specified by \f[V]-startdate\f[R], or the current date when
\f[V]-startdate\f[R] isn\[aq]t specified) for which the certificate
should be considered valid.
.RE
.TP
\f[V]-genseckey\f[R]
The following are the available options for the \f[V]-genseckey\f[R]
command:
.RS
.IP \[bu] 2
{\f[V]-alias\f[R] \f[I]alias\f[R]}: Alias name of the entry to process
.IP \[bu] 2
[\f[V]-keypass\f[R] \f[I]arg\f[R]]: Key password
.IP \[bu] 2
\f[V]-keyalg\f[R] \f[I]alg\f[R]: Key algorithm name
.IP \[bu] 2
{\f[V]-keysize\f[R] \f[I]size\f[R]}: Key bit size
.IP \[bu] 2
{\f[V]-keystore\f[R] \f[I]keystore\f[R]}: Keystore name
.IP \[bu] 2
[\f[V]-storepass\f[R] \f[I]arg\f[R]]: Keystore password
.IP \[bu] 2
{\f[V]-storetype\f[R] \f[I]type\f[R]}: Keystore type
.IP \[bu] 2
{\f[V]-providername\f[R] \f[I]name\f[R]}: Provider name
.IP \[bu] 2
{\f[V]-addprovider\f[R] \f[I]name\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by name (such as SunPKCS11) with
an optional configure argument.
.IP \[bu] 2
{\f[V]-providerclass\f[R] \f[I]class\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by fully qualified class name
with an optional configure argument.
.IP \[bu] 2
{\f[V]-providerpath\f[R] \f[I]list\f[R]}: Provider classpath
.IP \[bu] 2
{\f[V]-v\f[R]}: Verbose output
.IP \[bu] 2
{\f[V]-protected\f[R]}: Password provided through a protected mechanism
.PP
Use the \f[V]-genseckey\f[R] command to generate a secret key and store
it in a new \f[V]KeyStore.SecretKeyEntry\f[R] identified by
\f[V]alias\f[R].
.PP
The value of \f[V]-keyalg\f[R] specifies the algorithm to be used to
generate the secret key, and the value of \f[V]-keysize\f[R] specifies
the size of the key that is generated.
The \f[V]-keypass\f[R] value is a password that protects the secret key.
If a password is not provided, then the user is prompted for it.
If you press the \f[B]Return\f[R] key at the prompt, then the key
password is set to the same password that is used for the
\f[V]-keystore\f[R].
The \f[V]-keypass\f[R] value must contain at least six characters.
.RE
.TP
\f[V]-importcert\f[R]
The following are the available options for the \f[V]-importcert\f[R]
command:
.RS
.IP \[bu] 2
{\f[V]-noprompt\f[R]}: Do not prompt
.IP \[bu] 2
{\f[V]-trustcacerts\f[R]}: Trust certificates from cacerts
.IP \[bu] 2
{\f[V]-protected\f[R]}: Password is provided through protected mechanism
.IP \[bu] 2
{\f[V]-alias\f[R] \f[I]alias\f[R]}: Alias name of the entry to process
.IP \[bu] 2
{\f[V]-file\f[R] \f[I]file\f[R]}: Input file name
.IP \[bu] 2
[\f[V]-keypass\f[R] \f[I]arg\f[R]]: Key password
.IP \[bu] 2
{\f[V]-keystore\f[R] \f[I]keystore\f[R]}: Keystore name
.IP \[bu] 2
{\f[V]-cacerts\f[R]}: Access the cacerts keystore
.IP \[bu] 2
[\f[V]-storepass\f[R] \f[I]arg\f[R]]: Keystore password
.IP \[bu] 2
{\f[V]-storetype\f[R] \f[I]type\f[R]}: Keystore type
.IP \[bu] 2
{\f[V]-providername\f[R] \f[I]name\f[R]}: Provider name
.IP \[bu] 2
{\f[V]-addprovider\f[R] \f[I]name\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by name (such as SunPKCS11) with
an optional configure argument.
.IP \[bu] 2
{\f[V]-providerclass\f[R] \f[I]class\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by fully qualified class name
with an optional configure argument.
.IP \[bu] 2
{\f[V]-providerpath\f[R] \f[I]list\f[R]}: Provider classpath
.IP \[bu] 2
{\f[V]-v\f[R]}: Verbose output
.PP
Use the \f[V]-importcert\f[R] command to read the certificate or
certificate chain (where the latter is supplied in a PKCS#7 formatted
reply or in a sequence of X.509 certificates) from \f[V]-file\f[R]
\f[I]file\f[R], and store it in the \f[V]keystore\f[R] entry identified
by \f[V]-alias\f[R].
If \f[V]-file\f[R] \f[I]file\f[R] is not specified, then the certificate
or certificate chain is read from \f[V]stdin\f[R].
.PP
The \f[V]keytool\f[R] command can import X.509 v1, v2, and v3
certificates, and PKCS#7 formatted certificate chains consisting of
certificates of that type.
The data to be imported must be provided either in binary encoding
format or in printable encoding format (also known as Base64 encoding)
as defined by the Internet RFC 1421 standard.
In the latter case, the encoding must be bounded at the beginning by a
string that starts with \f[V]-----BEGIN\f[R], and bounded at the end by
a string that starts with \f[V]-----END\f[R].
.PP
You import a certificate for two reasons: To add it to the list of
trusted certificates, and to import a certificate reply received from a
certificate authority (CA) as the result of submitting a Certificate
Signing Request (CSR) to that CA.
See the \f[V]-certreq\f[R] command in \f[B]Commands for Generating a
Certificate Request\f[R].
.PP
The type of import is indicated by the value of the \f[V]-alias\f[R]
option.
If the alias doesn\[aq]t point to a key entry, then the
\f[V]keytool\f[R] command assumes you are adding a trusted certificate
entry.
In this case, the alias shouldn\[aq]t already exist in the keystore.
If the alias does exist, then the \f[V]keytool\f[R] command outputs an
error because a trusted certificate already exists for that alias, and
doesn\[aq]t import the certificate.
If \f[V]-alias\f[R] points to a key entry, then the \f[V]keytool\f[R]
command assumes that you\[aq]re importing a certificate reply.
.RE
.TP
\f[V]-importpass\f[R]
The following are the available options for the \f[V]-importpass\f[R]
command:
.RS
.IP \[bu] 2
{\f[V]-alias\f[R] \f[I]alias\f[R]}: Alias name of the entry to process
.IP \[bu] 2
[\f[V]-keypass\f[R] \f[I]arg\f[R]]: Key password
.IP \[bu] 2
{\f[V]-keyalg\f[R] \f[I]alg\f[R]}: Key algorithm name
.IP \[bu] 2
{\f[V]-keysize\f[R] \f[I]size\f[R]}: Key bit size
.IP \[bu] 2
{\f[V]-keystore\f[R] \f[I]keystore\f[R]}: Keystore name
.IP \[bu] 2
[\f[V]-storepass\f[R] \f[I]arg\f[R]]: Keystore password
.IP \[bu] 2
{\f[V]-storetype\f[R] \f[I]type\f[R]}: Keystore type
.IP \[bu] 2
{\f[V]-providername\f[R] \f[I]name\f[R]}: Provider name
.IP \[bu] 2
{\f[V]-addprovider\f[R] \f[I]name\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by name (such as SunPKCS11) with
an optional configure argument.
.IP \[bu] 2
{\f[V]-providerclass\f[R] \f[I]class\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by fully qualified class name
with an optional configure argument.
.IP \[bu] 2
{\f[V]-providerpath\f[R] \f[I]list\f[R]}: Provider classpath
.IP \[bu] 2
{\f[V]-v\f[R]}: Verbose output
.IP \[bu] 2
{\f[V]-protected\f[R]}: Password provided through a protected mechanism
.PP
Use the \f[V]-importpass\f[R] command to imports a passphrase and store
it in a new \f[V]KeyStore.SecretKeyEntry\f[R] identified by
\f[V]-alias\f[R].
The passphrase may be supplied via the standard input stream; otherwise
the user is prompted for it.
The \f[V]-keypass\f[R] option provides a password to protect the
imported passphrase.
If a password is not provided, then the user is prompted for it.
If you press the \f[B]Return\f[R] key at the prompt, then the key
password is set to the same password as that used for the
\f[V]keystore\f[R].
The \f[V]-keypass\f[R] value must contain at least six characters.
.RE
.SH COMMANDS FOR IMPORTING CONTENTS FROM ANOTHER KEYSTORE
.TP
\f[V]-importkeystore\f[R]
The following are the available options for the
\f[V]-importkeystore\f[R] command:
.RS
.IP \[bu] 2
\f[V]-srckeystore\f[R] \f[I]keystore\f[R]: Source keystore name
.IP \[bu] 2
{\f[V]-destkeystore\f[R] \f[I]keystore\f[R]}: Destination keystore name
.IP \[bu] 2
{\f[V]-srcstoretype\f[R] \f[I]type\f[R]}: Source keystore type
.IP \[bu] 2
{\f[V]-deststoretype\f[R] \f[I]type\f[R]}: Destination keystore type
.IP \[bu] 2
[\f[V]-srcstorepass\f[R] \f[I]arg\f[R]]: Source keystore password
.IP \[bu] 2
[\f[V]-deststorepass\f[R] \f[I]arg\f[R]]: Destination keystore password
.IP \[bu] 2
{\f[V]-srcprotected\f[R]}: Source keystore password protected
.IP \[bu] 2
{\f[V]-destprotected\f[R]}: Destination keystore password protected
.IP \[bu] 2
{\f[V]-srcprovidername\f[R] \f[I]name\f[R]}: Source keystore provider
name
.IP \[bu] 2
{\f[V]-destprovidername\f[R] \f[I]name\f[R]}: Destination keystore
provider name
.IP \[bu] 2
{\f[V]-srcalias\f[R] \f[I]alias\f[R]}: Source alias
.IP \[bu] 2
{\f[V]-destalias\f[R] \f[I]alias\f[R]}: Destination alias
.IP \[bu] 2
[\f[V]-srckeypass\f[R] \f[I]arg\f[R]]: Source key password
.IP \[bu] 2
[\f[V]-destkeypass\f[R] \f[I]arg\f[R]]: Destination key password
.IP \[bu] 2
{\f[V]-noprompt\f[R]}: Do not prompt
.IP \[bu] 2
{\f[V]-addprovider\f[R] \f[I]name\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]: Add security provider by name (such as SunPKCS11) with
an optional configure argument.
.IP \[bu] 2
{\f[V]-providerclass\f[R] \f[I]class\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by fully qualified class name
with an optional configure argument
.IP \[bu] 2
{\f[V]-providerpath\f[R] \f[I]list\f[R]}: Provider classpath
.IP \[bu] 2
{\f[V]-v\f[R]}: Verbose output
.PP
\f[B]Note:\f[R]
.PP
This is the first line of all options:
.RS
.PP
\f[V]-srckeystore\f[R] \f[I]keystore\f[R] \f[V]-destkeystore\f[R]
\f[I]keystore\f[R]
.RE
.PP
Use the \f[V]-importkeystore\f[R] command to import a single entry or
all entries from a source keystore to a destination keystore.
.PP
\f[B]Note:\f[R]
.PP
If you do not specify \f[V]-destkeystore\f[R] when using the
\f[V]keytool -importkeystore\f[R] command, then the default keystore
used is \f[V]$HOME/.keystore\f[R].
.PP
When the \f[V]-srcalias\f[R] option is provided, the command imports the
single entry identified by the alias to the destination keystore.
If a destination alias isn\[aq]t provided with \f[V]-destalias\f[R],
then \f[V]-srcalias\f[R] is used as the destination alias.
If the source entry is protected by a password, then
\f[V]-srckeypass\f[R] is used to recover the entry.
If \f[V]-srckeypass\f[R] isn\[aq]t provided, then the \f[V]keytool\f[R]
command attempts to use \f[V]-srcstorepass\f[R] to recover the entry.
If \f[V]-srcstorepass\f[R] is not provided or is incorrect, then the
user is prompted for a password.
The destination entry is protected with \f[V]-destkeypass\f[R].
If \f[V]-destkeypass\f[R] isn\[aq]t provided, then the destination entry
is protected with the source entry password.
For example, most third-party tools require \f[V]storepass\f[R] and
\f[V]keypass\f[R] in a PKCS #12 keystore to be the same.
To create a PKCS#12 keystore for these tools, always specify a
\f[V]-destkeypass\f[R] that is the same as \f[V]-deststorepass\f[R].
.PP
If the \f[V]-srcalias\f[R] option isn\[aq]t provided, then all entries
in the source keystore are imported into the destination keystore.
Each destination entry is stored under the alias from the source entry.
If the source entry is protected by a password, then
\f[V]-srcstorepass\f[R] is used to recover the entry.
If \f[V]-srcstorepass\f[R] is not provided or is incorrect, then the
user is prompted for a password.
If a source keystore entry type isn\[aq]t supported in the destination
keystore, or if an error occurs while storing an entry into the
destination keystore, then the user is prompted either to skip the entry
and continue or to quit.
The destination entry is protected with the source entry password.
.PP
If the destination alias already exists in the destination keystore,
then the user is prompted either to overwrite the entry or to create a
new entry under a different alias name.
.PP
If the \f[V]-noprompt\f[R] option is provided, then the user isn\[aq]t
prompted for a new destination alias.
Existing entries are overwritten with the destination alias name.
Entries that can\[aq]t be imported are skipped and a warning is
displayed.
.RE
.SH COMMANDS FOR GENERATING A CERTIFICATE REQUEST
.TP
\f[V]-certreq\f[R]
The following are the available options for the \f[V]-certreq\f[R]
command:
.RS
.IP \[bu] 2
{\f[V]-alias\f[R] \f[I]alias\f[R]}: Alias name of the entry to process
.IP \[bu] 2
{\f[V]-sigalg\f[R] \f[I]alg\f[R]}: Signature algorithm name
.IP \[bu] 2
{\f[V]-file\f[R] \f[I]file\f[R]}: Output file name
.IP \[bu] 2
[ \f[V]-keypass\f[R] \f[I]arg\f[R]]: Key password
.IP \[bu] 2
{\f[V]-keystore\f[R] \f[I]keystore\f[R]}: Keystore name
.IP \[bu] 2
{\f[V]-dname\f[R] \f[I]name\f[R]}: Distinguished name
.IP \[bu] 2
{\f[V]-ext\f[R] \f[I]value\f[R]}: X.509 extension
.IP \[bu] 2
[\f[V]-storepass\f[R] \f[I]arg\f[R]]: Keystore password
.IP \[bu] 2
{\f[V]-storetype\f[R] \f[I]type\f[R]}: Keystore type
.IP \[bu] 2
{\f[V]-providername\f[R] \f[I]name\f[R]}: Provider name
.IP \[bu] 2
{\f[V]-addprovider\f[R] \f[I]name\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by name (such as SunPKCS11) with
an optional configure argument.
.IP \[bu] 2
{\f[V]-providerclass\f[R] \f[I]class\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by fully qualified class name
with an optional configure argument.
.IP \[bu] 2
{\f[V]-providerpath\f[R] \f[I]list\f[R]}: Provider classpath
.IP \[bu] 2
{\f[V]-v\f[R]}: Verbose output
.IP \[bu] 2
{\f[V]-protected\f[R]}: Password provided through a protected mechanism
.PP
Use the \f[V]-certreq\f[R] command to generate a Certificate Signing
Request (CSR) using the PKCS #10 format.
.PP
A CSR is intended to be sent to a CA.
The CA authenticates the certificate requestor (usually offline) and
returns a certificate or certificate chain to replace the existing
certificate chain (initially a self-signed certificate) in the keystore.
.PP
The private key associated with \f[I]alias\f[R] is used to create the
PKCS #10 certificate request.
To access the private key, the correct password must be provided.
If \f[V]-keypass\f[R] isn\[aq]t provided at the command line and is
different from the password used to protect the integrity of the
keystore, then the user is prompted for it.
If \f[V]-dname\f[R] is provided, then it is used as the subject in the
CSR.
Otherwise, the X.500 Distinguished Name associated with alias is used.
.PP
The \f[V]-sigalg\f[R] value specifies the algorithm that should be used
to sign the CSR.
.PP
The CSR is stored in the \f[V]-file\f[R] \f[I]file\f[R].
If a file is not specified, then the CSR is output to \f[V]-stdout\f[R].
.PP
Use the \f[V]-importcert\f[R] command to import the response from the
CA.
.RE
.SH COMMANDS FOR EXPORTING DATA
.TP
\f[V]-exportcert\f[R]
The following are the available options for the \f[V]-exportcert\f[R]
command:
.RS
.IP \[bu] 2
{\f[V]-rfc\f[R]}: Output in RFC style
.IP \[bu] 2
{\f[V]-alias\f[R] \f[I]alias\f[R]}: Alias name of the entry to process
.IP \[bu] 2
{\f[V]-file\f[R] \f[I]file\f[R]}: Output file name
.IP \[bu] 2
{\f[V]-keystore\f[R] \f[I]keystore\f[R]}: Keystore name
.IP \[bu] 2
{\f[V]-cacerts\f[R]}: Access the cacerts keystore
.IP \[bu] 2
[\f[V]-storepass\f[R] \f[I]arg\f[R]]: Keystore password
.IP \[bu] 2
{\f[V]-storetype\f[R] \f[I]type\f[R]}: Keystore type
.IP \[bu] 2
{\f[V]-providername\f[R] \f[I]name\f[R]}: Provider name
.IP \[bu] 2
{\f[V]-addprovider\f[R] \f[I]name\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by name (such as SunPKCS11) with
an optional configure argument.
.IP \[bu] 2
{\f[V]-providerclass\f[R] \f[I]class\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]] }: Add security provider by fully qualified class name
with an optional configure argument.
.IP \[bu] 2
{\f[V]-providerpath\f[R] \f[I]list\f[R]}: Provider classpath
.IP \[bu] 2
{\f[V]-v\f[R]}: Verbose output
.IP \[bu] 2
{\f[V]-protected\f[R]}: Password provided through a protected mechanism
.PP
Use the \f[V]-exportcert\f[R] command to read a certificate from the
keystore that is associated with \f[V]-alias\f[R] \f[I]alias\f[R] and
store it in the \f[V]-file\f[R] \f[I]file\f[R].
When a file is not specified, the certificate is output to
\f[V]stdout\f[R].
.PP
By default, the certificate is output in binary encoding.
If the \f[V]-rfc\f[R] option is specified, then the output in the
printable encoding format defined by the Internet RFC 1421 Certificate
Encoding Standard.
.PP
If \f[V]-alias\f[R] refers to a trusted certificate, then that
certificate is output.
Otherwise, \f[V]-alias\f[R] refers to a key entry with an associated
certificate chain.
In that case, the first certificate in the chain is returned.
This certificate authenticates the public key of the entity addressed by
\f[V]-alias\f[R].
.RE
.SH COMMANDS FOR DISPLAYING DATA
.TP
\f[V]-list\f[R]
The following are the available options for the \f[V]-list\f[R] command:
.RS
.IP \[bu] 2
{\f[V]-rfc\f[R]}: Output in RFC style
.IP \[bu] 2
{\f[V]-alias\f[R] \f[I]alias\f[R]}: Alias name of the entry to process
.IP \[bu] 2
{\f[V]-keystore\f[R] \f[I]keystore\f[R]}: Keystore name
.IP \[bu] 2
{\f[V]-cacerts\f[R]}: Access the cacerts keystore
.IP \[bu] 2
[\f[V]-storepass\f[R] \f[I]arg\f[R]]: Keystore password
.IP \[bu] 2
{\f[V]-storetype\f[R] \f[I]type\f[R]}: Keystore type
.IP \[bu] 2
{\f[V]-providername\f[R] \f[I]name\f[R]}: Provider name
.IP \[bu] 2
{\f[V]-addprovider\f[R] \f[I]name\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by name (such as SunPKCS11) with
an optional configure argument.
.IP \[bu] 2
{\f[V]-providerclass\f[R] \f[I]class\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]] }: Add security provider by fully qualified class name
with an optional configure argument.
.IP \[bu] 2
{\f[V]-providerpath\f[R] \f[I]list\f[R]}: Provider classpath
.IP \[bu] 2
{\f[V]-v\f[R]}: Verbose output
.IP \[bu] 2
{\f[V]-protected\f[R]}: Password provided through a protected mechanism
.PP
Use the \f[V]-list\f[R] command to print the contents of the keystore
entry identified by \f[V]-alias\f[R] to \f[V]stdout\f[R].
If \f[V]-alias\f[R] \f[I]alias\f[R] is not specified, then the contents
of the entire keystore are printed.
.PP
By default, this command prints the SHA-256 fingerprint of a
certificate.
If the \f[V]-v\f[R] option is specified, then the certificate is printed
in human-readable format, with additional information such as the owner,
issuer, serial number, and any extensions.
If the \f[V]-rfc\f[R] option is specified, then the certificate contents
are printed by using the printable encoding format, as defined by the
Internet RFC 1421 Certificate Encoding Standard.
.PP
\f[B]Note:\f[R]
.PP
You can\[aq]t specify both \f[V]-v\f[R] and \f[V]-rfc\f[R] in the same
command.
Otherwise, an error is reported.
.RE
.TP
\f[V]-printcert\f[R]
The following are the available options for the \f[V]-printcert\f[R]
command:
.RS
.IP \[bu] 2
{\f[V]-rfc\f[R]}: Output in RFC style
.IP \[bu] 2
{\f[V]-file\f[R] \f[I]cert_file\f[R]}: Input file name
.IP \[bu] 2
{\f[V]-sslserver\f[R] \f[I]server\f[R][\f[V]:\f[R]\f[I]port\f[R]]}::
Secure Sockets Layer (SSL) server host and port
.IP \[bu] 2
{\f[V]-jarfile\f[R] \f[I]JAR_file\f[R]}: Signed \f[V].jar\f[R] file
.IP \[bu] 2
{\f[V]-keystore\f[R] \f[I]keystore\f[R]}: Keystore name
.IP \[bu] 2
{\f[V]-trustcacerts\f[R]}: Trust certificates from cacerts
.IP \[bu] 2
[\f[V]-storepass\f[R] \f[I]arg\f[R]]: Keystore password
.IP \[bu] 2
{\f[V]-storetype\f[R] \f[I]type\f[R]}: Keystore type
.IP \[bu] 2
{\f[V]-providername\f[R] \f[I]name\f[R]}: Provider name
.IP \[bu] 2
{\f[V]-addprovider\f[R] \f[I]name\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by name (such as SunPKCS11) with
an optional configure argument.
.IP \[bu] 2
{\f[V]-providerclass\f[R] \f[I]class\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by fully qualified class name
with an optional configure argument.
.IP \[bu] 2
{\f[V]-providerpath\f[R] \f[I]list\f[R]}: Provider classpath
.IP \[bu] 2
{\f[V]-protected\f[R]}: Password is provided through protected mechanism
.IP \[bu] 2
{\f[V]-v\f[R]}: Verbose output
.PP
Use the \f[V]-printcert\f[R] command to read and print the certificate
from \f[V]-file\f[R] \f[I]cert_file\f[R], the SSL server located at
\f[V]-sslserver\f[R] \f[I]server\f[R][\f[V]:\f[R]\f[I]port\f[R]], or the
signed JAR file specified by \f[V]-jarfile\f[R] \f[I]JAR_file\f[R].
It prints its contents in a human-readable format.
When a port is not specified, the standard HTTPS port 443 is assumed.
.PP
\f[B]Note:\f[R]
.PP
The \f[V]-sslserver\f[R] and \f[V]-file\f[R] options can\[aq]t be
provided in the same command.
Otherwise, an error is reported.
If you don\[aq]t specify either option, then the certificate is read
from \f[V]stdin\f[R].
.PP
When\f[V]-rfc\f[R] is specified, the \f[V]keytool\f[R] command prints
the certificate in PEM mode as defined by the Internet RFC 1421
Certificate Encoding standard.
.PP
If the certificate is read from a file or \f[V]stdin\f[R], then it might
be either binary encoded or in printable encoding format, as defined by
the RFC 1421 Certificate Encoding standard.
.PP
If the SSL server is behind a firewall, then the
\f[V]-J-Dhttps.proxyHost=proxyhost\f[R] and
\f[V]-J-Dhttps.proxyPort=proxyport\f[R] options can be specified on the
command line for proxy tunneling.
.PP
\f[B]Note:\f[R]
.PP
This command can be used independently of a keystore.
This command does not check for the weakness of a certificate\[aq]s
signature algorithm if it is a trusted certificate in the user keystore
(specified by \f[V]-keystore\f[R]) or in the \f[V]cacerts\f[R] keystore
(if \f[V]-trustcacerts\f[R] is specified).
.RE
.TP
\f[V]-printcertreq\f[R]
The following are the available options for the \f[V]-printcertreq\f[R]
command:
.RS
.IP \[bu] 2
{\f[V]-file\f[R] \f[I]file\f[R]}: Input file name
.IP \[bu] 2
{\f[V]-v\f[R]}: Verbose output
.PP
Use the \f[V]-printcertreq\f[R] command to print the contents of a PKCS
#10 format certificate request, which can be generated by the
\f[V]keytool -certreq\f[R] command.
The command reads the request from file.
If there is no file, then the request is read from the standard input.
.RE
.TP
\f[V]-printcrl\f[R]
The following are the available options for the \f[V]-printcrl\f[R]
command:
.RS
.IP \[bu] 2
{\f[V]-file crl\f[R]}: Input file name
.IP \[bu] 2
{\f[V]-keystore\f[R] \f[I]keystore\f[R]}: Keystore name
.IP \[bu] 2
{\f[V]-trustcacerts\f[R]}: Trust certificates from cacerts
.IP \[bu] 2
[\f[V]-storepass\f[R] \f[I]arg\f[R]]: Keystore password
.IP \[bu] 2
{\f[V]-storetype\f[R] \f[I]type\f[R]}: Keystore type
.IP \[bu] 2
{\f[V]-providername\f[R] \f[I]name\f[R]}: Provider name
.IP \[bu] 2
{\f[V]-addprovider\f[R] \f[I]name\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by name (such as SunPKCS11) with
an optional configure argument.
.IP \[bu] 2
{\f[V]-providerclass\f[R] \f[I]class\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by fully qualified class name
with an optional configure argument.
.IP \[bu] 2
{\f[V]-providerpath\f[R] \f[I]list\f[R]}: Provider classpath
.IP \[bu] 2
{\f[V]-protected\f[R]}: Password is provided through protected mechanism
.IP \[bu] 2
{\f[V]-v\f[R]}: Verbose output
.PP
Use the \f[V]-printcrl\f[R] command to read the Certificate Revocation
List (CRL) from \f[V]-file crl\f[R] .
A CRL is a list of the digital certificates that were revoked by the CA
that issued them.
The CA generates the \f[V]crl\f[R] file.
.PP
\f[B]Note:\f[R]
.PP
This command can be used independently of a keystore.
This command attempts to verify the CRL using a certificate from the
user keystore (specified by \f[V]-keystore\f[R]) or the
\f[V]cacerts\f[R] keystore (if \f[V]-trustcacerts\f[R] is specified),
and will print out a warning if it cannot be verified.
.RE
.SH COMMANDS FOR MANAGING THE KEYSTORE
.TP
\f[V]-storepasswd\f[R]
The following are the available options for the \f[V]-storepasswd\f[R]
command:
.RS
.IP \[bu] 2
[\f[V]-new\f[R] \f[I]arg\f[R]]: New password
.IP \[bu] 2
{\f[V]-keystore\f[R] \f[I]keystore\f[R]}: Keystore name
.IP \[bu] 2
{\f[V]-cacerts\f[R]}: Access the cacerts keystore
.IP \[bu] 2
[\f[V]-storepass\f[R] \f[I]arg\f[R]]: Keystore password
.IP \[bu] 2
{\f[V]-storetype\f[R] \f[I]type\f[R]}: Keystore type
.IP \[bu] 2
{\f[V]-providername\f[R] \f[I]name\f[R]}: Provider name
.IP \[bu] 2
{\f[V]-addprovider\f[R] \f[I]name\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by name (such as SunPKCS11) with
an optional configure argument.
.IP \[bu] 2
{\f[V]-providerclass\f[R] \f[I]class\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by fully qualified class name
with an optional configure argument.
.IP \[bu] 2
{\f[V]-providerpath\f[R] \f[I]list\f[R]}: Provider classpath
.IP \[bu] 2
{\f[V]-v\f[R]}: Verbose output
.PP
Use the \f[V]-storepasswd\f[R] command to change the password used to
protect the integrity of the keystore contents.
The new password is set by \f[V]-new\f[R] \f[I]arg\f[R] and must contain
at least six characters.
.RE
.TP
\f[V]-keypasswd\f[R]
The following are the available options for the \f[V]-keypasswd\f[R]
command:
.RS
.IP \[bu] 2
{\f[V]-alias\f[R] \f[I]alias\f[R]}: Alias name of the entry to process
.IP \[bu] 2
[\f[V]-keypass\f[R] \f[I]old_keypass\f[R]]: Key password
.IP \[bu] 2
[\f[V]-new\f[R] \f[I]new_keypass\f[R]]: New password
.IP \[bu] 2
{\f[V]-keystore\f[R] \f[I]keystore\f[R]}: Keystore name
.IP \[bu] 2
{\f[V]-storepass\f[R] \f[I]arg\f[R]}: Keystore password
.IP \[bu] 2
{\f[V]-storetype\f[R] \f[I]type\f[R]}: Keystore type
.IP \[bu] 2
{\f[V]-providername\f[R] \f[I]name\f[R]}: Provider name
.IP \[bu] 2
{\f[V]-addprovider\f[R] \f[I]name\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by name (such as SunPKCS11) with
an optional configure argument.
.IP \[bu] 2
{\f[V]-providerclass\f[R] \f[I]class\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by fully qualified class name
with an optional configure argument.
.IP \[bu] 2
{\f[V]-providerpath\f[R] \f[I]list\f[R]}: Provider classpath
.IP \[bu] 2
{\f[V]-v\f[R]}: Verbose output
.PP
Use the \f[V]-keypasswd\f[R] command to change the password (under which
private/secret keys identified by \f[V]-alias\f[R] are protected) from
\f[V]-keypass\f[R] \f[I]old_keypass\f[R] to \f[V]-new\f[R]
\f[I]new_keypass\f[R].
The password value must contain at least six characters.
.PP
If the \f[V]-keypass\f[R] option isn\[aq]t provided at the command line
and the \f[V]-keypass\f[R] password is different from the keystore
password (\f[V]-storepass\f[R] \f[I]arg\f[R]), then the user is prompted
for it.
.PP
If the \f[V]-new\f[R] option isn\[aq]t provided at the command line,
then the user is prompted for it.
.RE
.TP
\f[V]-delete\f[R]
The following are the available options for the \f[V]-delete\f[R]
command:
.RS
.IP \[bu] 2
[\f[V]-alias\f[R] \f[I]alias\f[R]]: Alias name of the entry to process
.IP \[bu] 2
{\f[V]-keystore\f[R] \f[I]keystore\f[R]}: Keystore name
.IP \[bu] 2
{\f[V]-cacerts\f[R]}: Access the cacerts keystore
.IP \[bu] 2
[\f[V]-storepass\f[R] \f[I]arg\f[R]]: Keystore password
.IP \[bu] 2
{\f[V]-storetype\f[R] \f[I]type\f[R]}: Keystore type
.IP \[bu] 2
{\f[V]-providername\f[R] \f[I]name\f[R]}: Provider name
.IP \[bu] 2
{\f[V]-addprovider\f[R] \f[I]name\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by name (such as SunPKCS11) with
an optional configure argument.
.IP \[bu] 2
{\f[V]-providerclass\f[R] \f[I]class\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by fully qualified class name
with an optional configure argument.
.IP \[bu] 2
{\f[V]-providerpath\f[R] \f[I]list\f[R]}: Provider classpath
.IP \[bu] 2
{\f[V]-v\f[R]}: Verbose output
.IP \[bu] 2
{\f[V]-protected\f[R]}: Password provided through a protected mechanism
.PP
Use the \f[V]-delete\f[R] command to delete the \f[V]-alias\f[R]
\f[I]alias\f[R] entry from the keystore.
When not provided at the command line, the user is prompted for the
\f[V]alias\f[R].
.RE
.TP
\f[V]-changealias\f[R]
The following are the available options for the \f[V]-changealias\f[R]
command:
.RS
.IP \[bu] 2
{\f[V]-alias\f[R] \f[I]alias\f[R]}: Alias name of the entry to process
.IP \[bu] 2
[\f[V]-destalias\f[R] \f[I]alias\f[R]]: Destination alias
.IP \[bu] 2
[\f[V]-keypass\f[R] \f[I]arg\f[R]]: Key password
.IP \[bu] 2
{\f[V]-keystore\f[R] \f[I]keystore\f[R]}: Keystore name
.IP \[bu] 2
{\f[V]-cacerts\f[R]}: Access the cacerts keystore
.IP \[bu] 2
[\f[V]-storepass\f[R] \f[I]arg\f[R]]: Keystore password
.IP \[bu] 2
{\f[V]-storetype\f[R] \f[I]type\f[R]}: Keystore type
.IP \[bu] 2
{\f[V]-providername\f[R] \f[I]name\f[R]}: Provider name
.IP \[bu] 2
{\f[V]-addprovider\f[R] \f[I]name\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by name (such as SunPKCS11) with
an optional configure argument.
.IP \[bu] 2
{\f[V]-providerclass\f[R] \f[I]class\f[R] [\f[V]-providerarg\f[R]
\f[I]arg\f[R]]}: Add security provider by fully qualified class name
with an optional configure argument.
.IP \[bu] 2
{\f[V]-providerpath\f[R] \f[I]list\f[R]}: Provider classpath
.IP \[bu] 2
{\f[V]-v\f[R]}: Verbose output
.IP \[bu] 2
{\f[V]-protected\f[R]}: Password provided through a protected mechanism
.PP
Use the \f[V]-changealias\f[R] command to move an existing keystore
entry from \f[V]-alias\f[R] \f[I]alias\f[R] to a new
\f[V]-destalias\f[R] \f[I]alias\f[R].
If a destination alias is not provided, then the command prompts you for
one.
If the original entry is protected with an entry password, then the
password can be supplied with the \f[V]-keypass\f[R] option.
If a key password is not provided, then the \f[V]-storepass\f[R] (if
provided) is attempted first.
If the attempt fails, then the user is prompted for a password.
.RE
.SH COMMANDS FOR DISPLAYING SECURITY-RELATED INFORMATION
.TP
\f[V]-showinfo\f[R]
The following are the available options for the \f[V]-showinfo\f[R]
command:
.RS
.IP \[bu] 2
{\f[V]-tls\f[R]}: Displays TLS configuration information
.IP \[bu] 2
{\f[V]-v\f[R]}: Verbose output
.PP
Use the \f[V]-showinfo\f[R] command to display various security-related
information.
The \f[V]-tls\f[R] option displays TLS configurations, such as the list
of enabled protocols and cipher suites.
.RE
.SH COMMANDS FOR DISPLAYING PROGRAM VERSION
.PP
You can use \f[V]-version\f[R] to print the program version of
\f[V]keytool\f[R].
.SH COMMANDS FOR DISPLAYING HELP INFORMATION
.PP
You can use \f[V]--help\f[R] to display a list of \f[V]keytool\f[R]
commands or to display help information about a specific
\f[V]keytool\f[R] command.
.IP \[bu] 2
To display a list of \f[V]keytool\f[R] commands, enter:
.RS 2
.RS
.PP
\f[V]keytool --help\f[R]
.RE
.RE
.IP \[bu] 2
To display help information about a specific \f[V]keytool\f[R] command,
enter:
.RS 2
.RS
.PP
\f[V]keytool -<command> --help\f[R]
.RE
.RE
.SH COMMON COMMAND OPTIONS
.PP
The \f[V]-v\f[R] option can appear for all commands except
\f[V]--help\f[R].
When the \f[V]-v\f[R] option appears, it signifies verbose mode, which
means that more information is provided in the output.
.PP
The \f[V]-J\f[R]\f[I]option\f[R] argument can appear for any command.
When the \f[V]-J\f[R]\f[I]option\f[R] is used, the specified
\f[I]option\f[R] string is passed directly to the Java interpreter.
This option doesn\[aq]t contain any spaces.
It\[aq]s useful for adjusting the execution environment or memory usage.
For a list of possible interpreter options, enter \f[V]java -h\f[R] or
\f[V]java -X\f[R] at the command line.
.PP
These options can appear for all commands operating on a keystore:
.TP
\f[V]-storetype\f[R] \f[I]storetype\f[R]
This qualifier specifies the type of keystore to be instantiated.
.TP
\f[V]-keystore\f[R] \f[I]keystore\f[R]
The keystore location.
.RS
.PP
If the JKS \f[V]storetype\f[R] is used and a keystore file doesn\[aq]t
yet exist, then certain \f[V]keytool\f[R] commands can result in a new
keystore file being created.
For example, if \f[V]keytool -genkeypair\f[R] is called and the
\f[V]-keystore\f[R] option isn\[aq]t specified, the default keystore
file named \f[V].keystore\f[R] is created in the user\[aq]s home
directory if it doesn\[aq]t already exist.
Similarly, if the \f[V]-keystore ks_file\f[R] option is specified but
\f[V]ks_file\f[R] doesn\[aq]t exist, then it is created.
For more information on the JKS \f[V]storetype\f[R], see the
\f[B]KeyStore Implementation\f[R] section in \f[B]KeyStore aliases\f[R].
.PP
Note that the input stream from the \f[V]-keystore\f[R] option is passed
to the \f[V]KeyStore.load\f[R] method.
If \f[V]NONE\f[R] is specified as the URL, then a null stream is passed
to the \f[V]KeyStore.load\f[R] method.
\f[V]NONE\f[R] should be specified if the keystore isn\[aq]t file-based.
For example, when the keystore resides on a hardware token device.
.RE
.TP
\f[V]-cacerts\f[R] \f[I]cacerts\f[R]
Operates on the \f[I]cacerts\f[R] keystore .
This option is equivalent to \f[V]-keystore\f[R]
\f[I]path_to_cacerts\f[R] \f[V]-storetype\f[R]
\f[I]type_of_cacerts\f[R].
An error is reported if the \f[V]-keystore\f[R] or \f[V]-storetype\f[R]
option is used with the \f[V]-cacerts\f[R] option.
.TP
\f[V]-storepass\f[R] [\f[V]:env\f[R] | \f[V]:file\f[R] ] \f[I]argument\f[R]
The password that is used to protect the integrity of the keystore.
.RS
.PP
If the modifier \f[V]env\f[R] or \f[V]file\f[R] isn\[aq]t specified,
then the password has the value \f[I]argument\f[R], which must contain
at least six characters.
Otherwise, the password is retrieved as follows:
.IP \[bu] 2
\f[V]env\f[R]: Retrieve the password from the environment variable named
\f[I]argument\f[R].
.IP \[bu] 2
\f[V]file\f[R]: Retrieve the password from the file named
\f[I]argument\f[R].
.PP
\f[B]Note:\f[R] All other options that require passwords, such as
\f[V]-keypass\f[R], \f[V]-srckeypass\f[R], \f[V]-destkeypass\f[R],
\f[V]-srcstorepass\f[R], and \f[V]-deststorepass\f[R], accept the
\f[V]env\f[R] and \f[V]file\f[R] modifiers.
Remember to separate the password option and the modifier with a colon
(:).
.PP
The password must be provided to all commands that access the keystore
contents.
For such commands, when the \f[V]-storepass\f[R] option isn\[aq]t
provided at the command line, the user is prompted for it.
.PP
When retrieving information from the keystore, the password is optional.
If a password is not specified, then the integrity of the retrieved
information can\[aq]t be verified and a warning is displayed.
.RE
.TP
\f[V]-providername\f[R] \f[I]name\f[R]
Used to identify a cryptographic service provider\[aq]s name when listed
in the security properties file.
.TP
\f[V]-addprovider\f[R] \f[I]name\f[R]
Used to add a security provider by name (such as SunPKCS11) .
.TP
\f[V]-providerclass\f[R] \f[I]class\f[R]
Used to specify the name of a cryptographic service provider\[aq]s
master class file when the service provider isn\[aq]t listed in the
security properties file.
.TP
\f[V]-providerpath\f[R] \f[I]list\f[R]
Used to specify the provider classpath.
.TP
\f[V]-providerarg\f[R] \f[I]arg\f[R]
Used with the \f[V]-addprovider\f[R] or \f[V]-providerclass\f[R] option
to represent an optional string input argument for the constructor of
\f[I]class\f[R] name.
.TP
\f[V]-protected=true\f[R]|\f[V]false\f[R]
Specify this value as \f[V]true\f[R] when a password must be specified
by way of a protected authentication path, such as a dedicated PIN
reader.
Because there are two keystores involved in the
\f[V]-importkeystore\f[R] command, the following two options,
\f[V]-srcprotected\f[R] and \f[V]-destprotected\f[R], are provided for
the source keystore and the destination keystore respectively.
.TP
\f[V]-ext\f[R] {\f[I]name\f[R]{\f[V]:critical\f[R]} {\f[V]=\f[R]\f[I]value\f[R]}}
Denotes an X.509 certificate extension.
The option can be used in \f[V]-genkeypair\f[R] and \f[V]-gencert\f[R]
to embed extensions into the generated certificate, or in
\f[V]-certreq\f[R] to show what extensions are requested in the
certificate request.
The option can appear multiple times.
The \f[I]name\f[R] argument can be a supported extension name (see
\f[B]Supported Named Extensions\f[R]) or an arbitrary OID number.
The \f[I]value\f[R] argument, when provided, denotes the argument for
the extension.
When \f[I]value\f[R] is omitted, the default value of the extension or
the extension itself requires no argument.
The \f[V]:critical\f[R] modifier, when provided, means the
extension\[aq]s \f[V]isCritical\f[R] attribute is \f[V]true\f[R];
otherwise, it is \f[V]false\f[R].
You can use \f[V]:c\f[R] in place of \f[V]:critical\f[R].
.TP
\f[V]-conf\f[R] \f[I]file\f[R]
Specifies a pre-configured options file.
.SH PRE-CONFIGURED OPTIONS FILE
.PP
A pre-configured options file is a Java properties file that can be
specified with the \f[V]-conf\f[R] option.
Each property represents the default option(s) for a keytool command
using \[dq]keytool.\f[I]command_name\f[R]\[dq] as the property name.
A special property named \[dq]keytool.all\[dq] represents the default
option(s) applied to all commands.
A property value can include \f[V]${prop}\f[R] which will be expanded to
the system property associated with it.
If an option value includes white spaces inside, it should be surrounded
by quotation marks (\[dq] or \[aq]).
All property names must be in lower case.
.PP
When \f[V]keytool\f[R] is launched with a pre-configured options file,
the value for \[dq]keytool.all\[dq] (if it exists) is prepended to the
\f[V]keytool\f[R] command line first, with the value for the command
name (if it exists) comes next, and the existing options on the command
line at last.
For a single-valued option, this allows the property for a specific
command to override the \[dq]keytool.all\[dq] value, and the value
specified on the command line to override both.
For multiple-valued options, all of them will be used by
\f[V]keytool\f[R].
.PP
For example, given the following file named \f[V]preconfig\f[R]:
.IP
.nf
\f[CB]
    # A tiny pre-configured options file
    keytool.all = -keystore ${user.home}/ks
    keytool.list = -v
    keytool.genkeypair = -keyalg rsa
\f[R]
.fi
.PP
\f[V]keytool -conf preconfig -list\f[R] is identical to
.RS
.PP
\f[V]keytool -keystore \[ti]/ks -v -list\f[R]
.RE
.PP
\f[V]keytool -conf preconfig -genkeypair -alias me\f[R] is identical to
.RS
.PP
\f[V]keytool -keystore \[ti]/ks -keyalg rsa -genkeypair -alias me\f[R]
.RE
.PP
\f[V]keytool -conf preconfig -genkeypair -alias you -keyalg ec\f[R] is
identical to
.RS
.PP
\f[V]keytool -keystore \[ti]/ks -keyalg rsa -genkeypair -alias you -keyalg ec\f[R]
.RE
.PP
which is equivalent to
.RS
.PP
\f[V]keytool -keystore \[ti]/ks -genkeypair -alias you -keyalg ec\f[R]
.RE
.PP
because \f[V]-keyalg\f[R] is a single-valued option and the \f[V]ec\f[R]
value specified on the command line overrides the preconfigured options
file.
.SH EXAMPLES OF OPTION VALUES
.PP
The following examples show the defaults for various option values:
.IP
.nf
\f[CB]
-alias \[dq]mykey\[dq]

-keysize
    2048 (when using -genkeypair and -keyalg is \[dq]DSA\[dq])
    3072 (when using -genkeypair and -keyalg is \[dq]RSA\[dq], \[dq]RSASSA-PSS\[dq], or \[dq]DH\[dq])
    384 (when using -genkeypair and -keyalg is \[dq]EC\[dq])
    56 (when using -genseckey and -keyalg is \[dq]DES\[dq])
    168 (when using -genseckey and -keyalg is \[dq]DESede\[dq])

-groupname
    ed25519 (when using -genkeypair and -keyalg is \[dq]EdDSA\[dq], key size is 255)
    x25519 (when using -genkeypair and -keyalg is \[dq]XDH\[dq], key size is 255)

-validity 90

-keystore <the file named .keystore in the user\[aq]s home directory>

-destkeystore <the file named .keystore in the user\[aq]s home directory>

-storetype <the value of the \[dq]keystore.type\[dq] property in the
    security properties file, which is returned by the static
    getDefaultType method in java.security.KeyStore>

-file
    stdin (if reading)
    stdout (if writing)

-protected false
\f[R]
.fi
.PP
When generating a certificate or a certificate request, the default
signature algorithm (\f[V]-sigalg\f[R] option) is derived from the
algorithm of the underlying private key to provide an appropriate level
of security strength as follows:
.PP
Default Signature Algorithms
.TS
tab(@);
l l l.
T{
keyalg
T}@T{
key size
T}@T{
default sigalg
T}
_
T{
DSA
T}@T{
any size
T}@T{
SHA256withDSA
T}
T{
RSA
T}@T{
< 624
T}@T{
SHA256withRSA (key size is too small for using SHA-384)
T}
T{
T}@T{
<= 7680
T}@T{
SHA384withRSA
T}
T{
T}@T{
> 7680
T}@T{
SHA512withRSA
T}
T{
EC
T}@T{
< 512
T}@T{
SHA384withECDSA
T}
T{
T}@T{
>= 512
T}@T{
SHA512withECDSA
T}
T{
RSASSA-PSS
T}@T{
< 624
T}@T{
RSASSA-PSS (with SHA-256, key size is too small for
T}
T{
T}@T{
T}@T{
using SHA-384)
T}
T{
T}@T{
<= 7680
T}@T{
RSASSA-PSS (with SHA-384)
T}
T{
T}@T{
> 7680
T}@T{
RSASSA-PSS (with SHA-512)
T}
T{
EdDSA
T}@T{
255
T}@T{
Ed25519
T}
T{
T}@T{
448
T}@T{
Ed448
T}
T{
Ed25519
T}@T{
255
T}@T{
Ed25519
T}
T{
Ed448
T}@T{
448
T}@T{
Ed448
T}
.TE
.IP \[bu] 2
The key size, measured in bits, corresponds to the size of the private
key.
This size is determined by the value of the \f[V]-keysize\f[R] or
\f[V]-groupname\f[R] options or the value derived from a default
setting.
.IP \[bu] 2
An RSASSA-PSS signature algorithm uses a \f[V]MessageDigest\f[R]
algorithm as its hash and MGF1 algorithms.
.IP \[bu] 2
If neither a default \f[V]-keysize\f[R] or \f[V]-groupname\f[R] is
defined for an algorithm, the security provider will choose a default
setting.
.PP
\f[B]Note:\f[R]
.PP
To improve out of the box security, default keysize, groupname, and
signature algorithm names are periodically updated to stronger values
with each release of the JDK.
If interoperability with older releases of the JDK is important, make
sure that the defaults are supported by those releases.
Alternatively, you can use the \f[V]-keysize\f[R], \f[V]-groupname\f[R],
or \f[V]-sigalg\f[R] options to override the default values at your own
risk.
.SH SUPPORTED NAMED EXTENSIONS
.PP
The \f[V]keytool\f[R] command supports these named extensions.
The names aren\[aq]t case-sensitive.
.TP
\f[V]BC\f[R] or \f[V]BasicConstraints\f[R]
Values:
.RS
.PP
The full form is
\f[V]ca:\f[R]{\f[V]true\f[R]|\f[V]false\f[R]}[\f[V],pathlen:\f[R]\f[I]len\f[R]]
or \f[I]len\f[R], which is short for
\f[V]ca:true,pathlen:\f[R]\f[I]len\f[R].
.PP
When \f[I]len\f[R] is omitted, the resulting value is \f[V]ca:true\f[R].
.RE
.TP
\f[V]KU\f[R] or \f[V]KeyUsage\f[R]
Values:
.RS
.PP
\f[I]usage\f[R](\f[V],\f[R] \f[I]usage\f[R])*
.PP
\f[I]usage\f[R] can be one of the following:
.IP \[bu] 2
\f[V]digitalSignature\f[R]
.IP \[bu] 2
\f[V]nonRepudiation\f[R] (\f[V]contentCommitment\f[R])
.IP \[bu] 2
\f[V]keyEncipherment\f[R]
.IP \[bu] 2
\f[V]dataEncipherment\f[R]
.IP \[bu] 2
\f[V]keyAgreement\f[R]
.IP \[bu] 2
\f[V]keyCertSign\f[R]
.IP \[bu] 2
\f[V]cRLSign\f[R]
.IP \[bu] 2
\f[V]encipherOnly\f[R]
.IP \[bu] 2
\f[V]decipherOnly\f[R]
.PP
Provided there is no ambiguity, the \f[I]usage\f[R] argument can be
abbreviated with the first few letters (such as \f[V]dig\f[R] for
\f[V]digitalSignature\f[R]) or in camel-case style (such as \f[V]dS\f[R]
for \f[V]digitalSignature\f[R] or \f[V]cRLS\f[R] for \f[V]cRLSign\f[R]).
The \f[I]usage\f[R] values are case-sensitive.
.RE
.TP
\f[V]EKU\f[R] or \f[V]ExtendedKeyUsage\f[R]
Values:
.RS
.PP
\f[I]usage\f[R](\f[V],\f[R] \f[I]usage\f[R])*
.PP
\f[I]usage\f[R] can be one of the following:
.IP \[bu] 2
\f[V]anyExtendedKeyUsage\f[R]
.IP \[bu] 2
\f[V]serverAuth\f[R]
.IP \[bu] 2
\f[V]clientAuth\f[R]
.IP \[bu] 2
\f[V]codeSigning\f[R]
.IP \[bu] 2
\f[V]emailProtection\f[R]
.IP \[bu] 2
\f[V]timeStamping\f[R]
.IP \[bu] 2
\f[V]OCSPSigning\f[R]
.IP \[bu] 2
Any OID string
.PP
Provided there is no ambiguity, the \f[I]usage\f[R] argument can be
abbreviated with the first few letters or in camel-case style.
The \f[I]usage\f[R] values are case-sensitive.
.RE
.TP
\f[V]SAN\f[R] or \f[V]SubjectAlternativeName\f[R]
Values:
.RS
.PP
\f[I]type\f[R]\f[V]:\f[R]\f[I]value\f[R](\f[V],\f[R]
\f[I]type\f[R]\f[V]:\f[R]\f[I]value\f[R])*
.PP
\f[I]type\f[R] can be one of the following:
.IP \[bu] 2
\f[V]EMAIL\f[R]
.IP \[bu] 2
\f[V]URI\f[R]
.IP \[bu] 2
\f[V]DNS\f[R]
.IP \[bu] 2
\f[V]IP\f[R]
.IP \[bu] 2
\f[V]OID\f[R]
.PP
The \f[I]value\f[R] argument is the string format value for the
\f[I]type\f[R].
.RE
.TP
\f[V]IAN\f[R] or \f[V]IssuerAlternativeName\f[R]
Values:
.RS
.PP
Same as \f[V]SAN\f[R] or \f[V]SubjectAlternativeName\f[R].
.RE
.TP
\f[V]SIA\f[R] or \f[V]SubjectInfoAccess\f[R]
Values:
.RS
.PP
\f[I]method\f[R]\f[V]:\f[R]\f[I]location-type\f[R]\f[V]:\f[R]\f[I]location-value\f[R](\f[V],\f[R]
\f[I]method\f[R]\f[V]:\f[R]\f[I]location-type\f[R]\f[V]:\f[R]\f[I]location-value\f[R])*
.PP
\f[I]method\f[R] can be one of the following:
.IP \[bu] 2
\f[V]timeStamping\f[R]
.IP \[bu] 2
\f[V]caRepository\f[R]
.IP \[bu] 2
Any OID
.PP
The \f[I]location-type\f[R] and \f[I]location-value\f[R] arguments can
be any \f[I]type\f[R]\f[V]:\f[R]\f[I]value\f[R] supported by the
\f[V]SubjectAlternativeName\f[R] extension.
.RE
.TP
\f[V]AIA\f[R] or \f[V]AuthorityInfoAccess\f[R]
Values:
.RS
.PP
Same as \f[V]SIA\f[R] or \f[V]SubjectInfoAccess\f[R].
.PP
The \f[I]method\f[R] argument can be one of the following:
.IP \[bu] 2
\f[V]ocsp\f[R]
.IP \[bu] 2
\f[V]caIssuers\f[R]
.IP \[bu] 2
Any OID
.RE
.PP
When \f[I]name\f[R] is OID, the value is the hexadecimal dumped Definite
Encoding Rules (DER) encoding of the \f[V]extnValue\f[R] for the
extension excluding the OCTET STRING type and length bytes.
Other than standard hexadecimal numbers (0-9, a-f, A-F), any extra
characters are ignored in the HEX string.
Therefore, both 01:02:03:04 and 01020304 are accepted as identical
values.
When there is no value, the extension has an empty value field.
.PP
A special name \f[V]honored\f[R], used only in \f[V]-gencert\f[R],
denotes how the extensions included in the certificate request should be
honored.
The value for this name is a comma-separated list of \f[V]all\f[R] (all
requested extensions are honored),
\f[I]name\f[R]{\f[V]:\f[R][\f[V]critical\f[R]|\f[V]non-critical\f[R]]}
(the named extension is honored, but it uses a different
\f[V]isCritical\f[R] attribute), and \f[V]-name\f[R] (used with
\f[V]all\f[R], denotes an exception).
Requested extensions aren\[aq]t honored by default.
.PP
If, besides the\f[V]-ext honored\f[R] option, another named or OID
\f[V]-ext\f[R] option is provided, this extension is added to those
already honored.
However, if this name (or OID) also appears in the honored value, then
its value and criticality override that in the request.
If an extension of the same type is provided multiple times through
either a name or an OID, only the last extension is used.
.PP
The \f[V]subjectKeyIdentifier\f[R] extension is always created.
For non-self-signed certificates, the \f[V]authorityKeyIdentifier\f[R]
is created.
.PP
\f[B]CAUTION:\f[R]
.PP
Users should be aware that some combinations of extensions (and other
certificate fields) may not conform to the Internet standard.
See \f[B]Certificate Conformance Warning\f[R].
.SH EXAMPLES OF TASKS IN CREATING A KEYSTORE
.PP
The following examples describe the sequence actions in creating a
keystore for managing public/private key pairs and certificates from
trusted entities.
.IP \[bu] 2
\f[B]Generating the Key Pair\f[R]
.IP \[bu] 2
\f[B]Requesting a Signed Certificate from a CA\f[R]
.IP \[bu] 2
\f[B]Importing a Certificate for the CA\f[R]
.IP \[bu] 2
\f[B]Importing the Certificate Reply from the CA\f[R]
.IP \[bu] 2
\f[B]Exporting a Certificate That Authenticates the Public Key\f[R]
.IP \[bu] 2
\f[B]Importing the Keystore\f[R]
.IP \[bu] 2
\f[B]Generating Certificates for an SSL Server\f[R]
.SH GENERATING THE KEY PAIR
.PP
Create a keystore and then generate the key pair.
.PP
You can enter the command as a single line such as the following:
.RS
.PP
\f[V]keytool -genkeypair -dname \[dq]cn=myname, ou=mygroup, o=mycompany, c=mycountry\[dq] -alias business -keyalg rsa -keypass\f[R]
\f[I]password\f[R]
\f[V]-keystore /working/mykeystore -storepass password -validity 180\f[R]
.RE
.PP
The command creates the keystore named \f[V]mykeystore\f[R] in the
working directory (provided it doesn\[aq]t already exist), and assigns
it the password specified by \f[V]-keypass\f[R].
It generates a public/private key pair for the entity whose
distinguished name is \f[V]myname\f[R], \f[V]mygroup\f[R],
\f[V]mycompany\f[R], and a two-letter country code of
\f[V]mycountry\f[R].
It uses the RSA key generation algorithm to create the keys; both are
3072 bits.
.PP
The command uses the default SHA384withRSA signature algorithm to create
a self-signed certificate that includes the public key and the
distinguished name information.
The certificate is valid for 180 days, and is associated with the
private key in a keystore entry referred to by
\f[V]-alias business\f[R].
The private key is assigned the password specified by
\f[V]-keypass\f[R].
.PP
The command is significantly shorter when the option defaults are
accepted.
In this case, only \f[V]-keyalg\f[R] is required, and the defaults are
used for unspecified options that have default values.
You are prompted for any required values.
You could have the following:
.RS
.PP
\f[V]keytool -genkeypair -keyalg rsa\f[R]
.RE
.PP
In this case, a keystore entry with the alias \f[V]mykey\f[R] is
created, with a newly generated key pair and a certificate that is valid
for 90 days.
This entry is placed in your home directory in a keystore named
\f[V].keystore\f[R] .
\f[V].keystore\f[R] is created if it doesn\[aq]t already exist.
You are prompted for the distinguished name information, the keystore
password, and the private key password.
.PP
\f[B]Note:\f[R]
.PP
The rest of the examples assume that you responded to the prompts with
values equal to those specified in the first \f[V]-genkeypair\f[R]
command.
For example, a distinguished name of
\f[V]cn=\f[R]\f[I]myname\f[R]\f[V], ou=\f[R]\f[I]mygroup\f[R]\f[V], o=\f[R]\f[I]mycompany\f[R]\f[V], c=\f[R]\f[I]mycountry\f[R]).
.SH REQUESTING A SIGNED CERTIFICATE FROM A CA
.PP
\f[B]Note:\f[R]
.PP
Generating the key pair created a self-signed certificate; however, a
certificate is more likely to be trusted by others when it is signed by
a CA.
.PP
To get a CA signature, complete the following process:
.IP "1." 3
Generate a CSR:
.RS 4
.RS
.PP
\f[V]keytool -certreq -file myname.csr\f[R]
.RE
.PP
This creates a CSR for the entity identified by the default alias
\f[V]mykey\f[R] and puts the request in the file named
\f[V]myname.csr\f[R].
.RE
.IP "2." 3
Submit \f[V]myname.csr\f[R] to a CA, such as DigiCert.
.PP
The CA authenticates you, the requestor (usually offline), and returns a
certificate, signed by them, authenticating your public key.
In some cases, the CA returns a chain of certificates, each one
authenticating the public key of the signer of the previous certificate
in the chain.
.SH IMPORTING A CERTIFICATE FOR THE CA
.PP
To import a certificate for the CA, complete the following process:
.IP "1." 3
Before you import the certificate reply from a CA, you need one or more
trusted certificates either in your keystore or in the \f[V]cacerts\f[R]
keystore file.
See \f[V]-importcert\f[R] in \f[B]Commands\f[R].
.RS 4
.IP \[bu] 2
If the certificate reply is a certificate chain, then you need the top
certificate of the chain.
The root CA certificate that authenticates the public key of the CA.
.IP \[bu] 2
If the certificate reply is a single certificate, then you need a
certificate for the issuing CA (the one that signed it).
If that certificate isn\[aq]t self-signed, then you need a certificate
for its signer, and so on, up to a self-signed root CA certificate.
.PP
The \f[V]cacerts\f[R] keystore ships with a set of root certificates
issued by the CAs of \f[B]the Oracle Java Root Certificate program\f[R]
[http://www.oracle.com/technetwork/java/javase/javasecarootcertsprogram-1876540.html].
If you request a signed certificate from a CA, and a certificate
authenticating that CA\[aq]s public key hasn\[aq]t been added to
\f[V]cacerts\f[R], then you must import a certificate from that CA as a
trusted certificate.
.PP
A certificate from a CA is usually self-signed or signed by another CA.
If it is signed by another CA, you need a certificate that authenticates
that CA\[aq]s public key.
.PP
For example, you have obtained a \f[I]X\f[R]\f[V].cer\f[R] file from a
company that is a CA and the file is supposed to be a self-signed
certificate that authenticates that CA\[aq]s public key.
Before you import it as a trusted certificate, you should ensure that
the certificate is valid by:
.IP "1." 3
Viewing it with the \f[V]keytool -printcert\f[R] command or the
\f[V]keytool -importcert\f[R] command without using the
\f[V]-noprompt\f[R] option.
Make sure that the displayed certificate fingerprints match the expected
fingerprints.
.IP "2." 3
Calling the person who sent the certificate, and comparing the
fingerprints that you see with the ones that they show or that a secure
public key repository shows.
.PP
Only when the fingerprints are equal is it assured that the certificate
wasn\[aq]t replaced in transit with somebody else\[aq]s certificate
(such as an attacker\[aq]s certificate).
If such an attack takes place, and you didn\[aq]t check the certificate
before you imported it, then you would be trusting anything that the
attacker signed.
.RE
.IP "2." 3
Replace the self-signed certificate with a certificate chain, where each
certificate in the chain authenticates the public key of the signer of
the previous certificate in the chain, up to a root CA.
.RS 4
.PP
If you trust that the certificate is valid, then you can add it to your
keystore by entering the following command:
.RS
.PP
\f[V]keytool -importcert -alias\f[R] \f[I]alias\f[R]
\f[V]-file *X*\f[R].cer\[ga]
.RE
.PP
This command creates a trusted certificate entry in the keystore from
the data in the CA certificate file and assigns the values of the
\f[I]alias\f[R] to the entry.
.RE
.SH IMPORTING THE CERTIFICATE REPLY FROM THE CA
.PP
After you import a certificate that authenticates the public key of the
CA that you submitted your certificate signing request to (or there is
already such a certificate in the \f[V]cacerts\f[R] file), you can
import the certificate reply and replace your self-signed certificate
with a certificate chain.
.PP
The certificate chain is one of the following:
.IP \[bu] 2
Returned by the CA when the CA reply is a chain.
.IP \[bu] 2
Constructed when the CA reply is a single certificate.
This certificate chain is constructed by using the certificate reply and
trusted certificates available either in the keystore where you import
the reply or in the \f[V]cacerts\f[R] keystore file.
.PP
For example, if you sent your certificate signing request to DigiCert,
then you can import their reply by entering the following command:
.PP
\f[B]Note:\f[R]
.PP
In this example, the returned certificate is named
\f[V]DCmyname.cer\f[R].
.RS
.PP
\f[V]keytool -importcert -trustcacerts -file DCmyname.cer\f[R]
.RE
.SH EXPORTING A CERTIFICATE THAT AUTHENTICATES THE PUBLIC KEY
.PP
\f[B]Note:\f[R]
.PP
If you used the \f[V]jarsigner\f[R] command to sign a Java Archive (JAR)
file, then clients that use the file will want to authenticate your
signature.
.PP
One way that clients can authenticate you is by importing your public
key certificate into their keystore as a trusted entry.
You can then export the certificate and supply it to your clients.
.PP
For example:
.IP "1." 3
Copy your certificate to a file named \f[V]myname.cer\f[R] by entering
the following command:
.RS 4
.PP
\f[B]Note:\f[R]
.PP
In this example, the entry has an alias of \f[V]mykey\f[R].
.RS
.PP
\f[V]keytool -exportcert -alias mykey -file myname.cer\f[R]
.RE
.RE
.IP "2." 3
With the certificate and the signed JAR file, a client can use the
\f[V]jarsigner\f[R] command to authenticate your signature.
.SH IMPORTING THE KEYSTORE
.PP
Use the \f[V]importkeystore\f[R] command to import an entire keystore
into another keystore.
This imports all entries from the source keystore, including keys and
certificates, to the destination keystore with a single command.
You can use this command to import entries from a different type of
keystore.
During the import, all new entries in the destination keystore will have
the same alias names and protection passwords (for secret keys and
private keys).
If the \f[V]keytool\f[R] command can\[aq]t recover the private keys or
secret keys from the source keystore, then it prompts you for a
password.
If it detects alias duplication, then it asks you for a new alias, and
you can specify a new alias or simply allow the \f[V]keytool\f[R]
command to overwrite the existing one.
.PP
For example, import entries from a typical JKS type keystore
\f[V]key.jks\f[R] into a PKCS #11 type hardware-based keystore, by
entering the following command:
.RS
.PP
\f[V]keytool -importkeystore -srckeystore key.jks -destkeystore NONE -srcstoretype JKS -deststoretype PKCS11 -srcstorepass\f[R]
\f[I]password\f[R] \f[V]-deststorepass\f[R] \f[I]password\f[R]
.RE
.PP
The \f[V]importkeystore\f[R] command can also be used to import a single
entry from a source keystore to a destination keystore.
In this case, besides the options you used in the previous example, you
need to specify the alias you want to import.
With the \f[V]-srcalias\f[R] option specified, you can also specify the
destination alias name, protection password for a secret or private key,
and the destination protection password you want as follows:
.RS
.PP
\f[V]keytool -importkeystore -srckeystore key.jks -destkeystore NONE -srcstoretype JKS -deststoretype PKCS11 -srcstorepass\f[R]
\f[I]password\f[R] \f[V]-deststorepass\f[R] \f[I]password\f[R]
\f[V]-srcalias myprivatekey -destalias myoldprivatekey -srckeypass\f[R]
\f[I]password\f[R] \f[V]-destkeypass\f[R] \f[I]password\f[R]
\f[V]-noprompt\f[R]
.RE
.SH GENERATING CERTIFICATES FOR AN SSL SERVER
.PP
The following are \f[V]keytool\f[R] commands used to generate key pairs
and certificates for three entities:
.IP \[bu] 2
Root CA (\f[V]root\f[R])
.IP \[bu] 2
Intermediate CA (\f[V]ca\f[R])
.IP \[bu] 2
SSL server (\f[V]server\f[R])
.PP
Ensure that you store all the certificates in the same keystore.
.IP
.nf
\f[CB]
keytool -genkeypair -keystore root.jks -alias root -ext bc:c -keyalg rsa
keytool -genkeypair -keystore ca.jks -alias ca -ext bc:c -keyalg rsa
keytool -genkeypair -keystore server.jks -alias server -keyalg rsa

keytool -keystore root.jks -alias root -exportcert -rfc > root.pem

keytool -storepass password -keystore ca.jks -certreq -alias ca |
    keytool -storepass password -keystore root.jks
    -gencert -alias root -ext BC=0 -rfc > ca.pem
keytool -keystore ca.jks -importcert -alias ca -file ca.pem

keytool -storepass password -keystore server.jks -certreq -alias server |
    keytool -storepass password -keystore ca.jks -gencert -alias ca
    -ext ku:c=dig,kE -rfc > server.pem
cat root.pem ca.pem server.pem |
    keytool -keystore server.jks -importcert -alias server
\f[R]
.fi
.SH TERMS
.TP
Keystore
A keystore is a storage facility for cryptographic keys and
certificates.
.TP
Keystore entries
Keystores can have different types of entries.
The two most applicable entry types for the \f[V]keytool\f[R] command
include the following:
.RS
.PP
Key entries: Each entry holds very sensitive cryptographic key
information, which is stored in a protected format to prevent
unauthorized access.
Typically, a key stored in this type of entry is a secret key, or a
private key accompanied by the certificate chain for the corresponding
public key.
See \f[B]Certificate Chains\f[R].
The \f[V]keytool\f[R] command can handle both types of entries, while
the \f[V]jarsigner\f[R] tool only handles the latter type of entry, that
is private keys and their associated certificate chains.
.PP
Trusted certificate entries: Each entry contains a single public key
certificate that belongs to another party.
The entry is called a trusted certificate because the keystore owner
trusts that the public key in the certificate belongs to the identity
identified by the subject (owner) of the certificate.
The issuer of the certificate vouches for this, by signing the
certificate.
.RE
.TP
Keystore aliases
All keystore entries (key and trusted certificate entries) are accessed
by way of unique aliases.
.RS
.PP
An alias is specified when you add an entity to the keystore with the
\f[V]-genseckey\f[R] command to generate a secret key, the
\f[V]-genkeypair\f[R] command to generate a key pair (public and private
key), or the \f[V]-importcert\f[R] command to add a certificate or
certificate chain to the list of trusted certificates.
Subsequent \f[V]keytool\f[R] commands must use this same alias to refer
to the entity.
.PP
For example, you can use the alias \f[V]duke\f[R] to generate a new
public/private key pair and wrap the public key into a self-signed
certificate with the following command.
See \f[B]Certificate Chains\f[R].
.RS
.PP
\f[V]keytool -genkeypair -alias duke -keyalg rsa -keypass\f[R]
\f[I]passwd\f[R]
.RE
.PP
This example specifies an initial \f[I]passwd\f[R] required by
subsequent commands to access the private key associated with the alias
\f[V]duke\f[R].
If you later want to change Duke\[aq]s private key password, use a
command such as the following:
.RS
.PP
\f[V]keytool -keypasswd -alias duke -keypass\f[R] \f[I]passwd\f[R]
\f[V]-new\f[R] \f[I]newpasswd\f[R]
.RE
.PP
This changes the initial \f[I]passwd\f[R] to \f[I]newpasswd\f[R].
A password shouldn\[aq]t be specified on a command line or in a script
unless it is for testing purposes, or you are on a secure system.
If you don\[aq]t specify a required password option on a command line,
then you are prompted for it.
.RE
.TP
Keystore implementation
The \f[V]KeyStore\f[R] class provided in the \f[V]java.security\f[R]
package supplies well-defined interfaces to access and modify the
information in a keystore.
It is possible for there to be multiple different concrete
implementations, where each implementation is that for a particular type
of keystore.
.RS
.PP
Currently, two command-line tools (\f[V]keytool\f[R] and
\f[V]jarsigner\f[R]) make use of keystore implementations.
Because the \f[V]KeyStore\f[R] class is \f[V]public\f[R], users can
write additional security applications that use it.
.PP
In JDK 9 and later, the default keystore implementation is
\f[V]PKCS12\f[R].
This is a cross platform keystore based on the RSA PKCS12 Personal
Information Exchange Syntax Standard.
This standard is primarily meant for storing or transporting a
user\[aq]s private keys, certificates, and miscellaneous secrets.
There is another built-in implementation, provided by Oracle.
It implements the keystore as a file with a proprietary keystore type
(format) named \f[V]JKS\f[R].
It protects each private key with its individual password, and also
protects the integrity of the entire keystore with a (possibly
different) password.
.PP
Keystore implementations are provider-based.
More specifically, the application interfaces supplied by
\f[V]KeyStore\f[R] are implemented in terms of a Service Provider
Interface (SPI).
That is, there is a corresponding abstract \f[V]KeystoreSpi\f[R] class,
also in the \f[V]java.security package\f[R], which defines the Service
Provider Interface methods that providers must implement.
The term \f[I]provider\f[R] refers to a package or a set of packages
that supply a concrete implementation of a subset of services that can
be accessed by the Java Security API.
To provide a keystore implementation, clients must implement a provider
and supply a \f[V]KeystoreSpi\f[R] subclass implementation, as described
in Steps to Implement and Integrate a Provider.
.PP
Applications can choose different types of keystore implementations from
different providers, using the \f[V]getInstance\f[R] factory method
supplied in the \f[V]KeyStore\f[R] class.
A keystore type defines the storage and data format of the keystore
information, and the algorithms used to protect private/secret keys in
the keystore and the integrity of the keystore.
Keystore implementations of different types aren\[aq]t compatible.
.PP
The \f[V]keytool\f[R] command works on any file-based keystore
implementation.
It treats the keystore location that is passed to it at the command line
as a file name and converts it to a \f[V]FileInputStream\f[R], from
which it loads the keystore information.)The \f[V]jarsigner\f[R]
commands can read a keystore from any location that can be specified
with a URL.
.PP
For \f[V]keytool\f[R] and \f[V]jarsigner\f[R], you can specify a
keystore type at the command line, with the \f[V]-storetype\f[R] option.
.PP
If you don\[aq]t explicitly specify a keystore type, then the tools
choose a keystore implementation based on the value of the
\f[V]keystore.type\f[R] property specified in the security properties
file.
The security properties file is called \f[V]java.security\f[R], and
resides in the security properties directory:
.IP \[bu] 2
\f[B]Linux and macOS:\f[R] \f[V]java.home/lib/security\f[R]
.IP \[bu] 2
\f[B]Windows:\f[R] \f[V]java.home\[rs]lib\[rs]security\f[R]
.PP
Each tool gets the \f[V]keystore.type\f[R] value and then examines all
the currently installed providers until it finds one that implements a
keystores of that type.
It then uses the keystore implementation from that provider.The
\f[V]KeyStore\f[R] class defines a static method named
\f[V]getDefaultType\f[R] that lets applications retrieve the value of
the \f[V]keystore.type\f[R] property.
The following line of code creates an instance of the default keystore
type as specified in the \f[V]keystore.type\f[R] property:
.RS
.PP
\f[V]KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());\f[R]
.RE
.PP
The default keystore type is \f[V]pkcs12\f[R], which is a cross-platform
keystore based on the RSA PKCS12 Personal Information Exchange Syntax
Standard.
This is specified by the following line in the security properties file:
.RS
.PP
\f[V]keystore.type=pkcs12\f[R]
.RE
.PP
To have the tools utilize a keystore implementation other than the
default, you can change that line to specify a different keystore type.
For example, if you want to use the Oracle\[aq]s \f[V]jks\f[R] keystore
implementation, then change the line to the following:
.RS
.PP
\f[V]keystore.type=jks\f[R]
.RE
.PP
\f[B]Note:\f[R]
.PP
Case doesn\[aq]t matter in keystore type designations.
For example, \f[V]JKS\f[R] would be considered the same as
\f[V]jks\f[R].
.RE
.TP
Certificate
A certificate (or public-key certificate) is a digitally signed
statement from one entity (the issuer), saying that the public key and
some other information of another entity (the subject) has some specific
value.
The following terms are related to certificates:
.RS
.IP \[bu] 2
Public Keys: These are numbers associated with a particular entity, and
are intended to be known to everyone who needs to have trusted
interactions with that entity.
Public keys are used to verify signatures.
.IP \[bu] 2
Digitally Signed: If some data is digitally signed, then it is stored
with the identity of an entity and a signature that proves that entity
knows about the data.
The data is rendered unforgeable by signing with the entity\[aq]s
private key.
.IP \[bu] 2
Identity: A known way of addressing an entity.
In some systems, the identity is the public key, and in others it can be
anything from an Oracle Solaris UID to an email address to an X.509
distinguished name.
.IP \[bu] 2
Signature: A signature is computed over some data using the private key
of an entity.
The signer, which in the case of a certificate is also known as the
issuer.
.IP \[bu] 2
Private Keys: These are numbers, each of which is supposed to be known
only to the particular entity whose private key it is (that is, it is
supposed to be kept secret).
Private and public keys exist in pairs in all public key cryptography
systems (also referred to as public key crypto systems).
In a typical public key crypto system, such as DSA, a private key
corresponds to exactly one public key.
Private keys are used to compute signatures.
.IP \[bu] 2
Entity: An entity is a person, organization, program, computer,
business, bank, or something else you are trusting to some degree.
.PP
Public key cryptography requires access to users\[aq] public keys.
In a large-scale networked environment, it is impossible to guarantee
that prior relationships between communicating entities were established
or that a trusted repository exists with all used public keys.
Certificates were invented as a solution to this public key distribution
problem.
Now a Certification Authority (CA) can act as a trusted third party.
CAs are entities such as businesses that are trusted to sign (issue)
certificates for other entities.
It is assumed that CAs only create valid and reliable certificates
because they are bound by legal agreements.
There are many public Certification Authorities, such as DigiCert,
Comodo, Entrust, and so on.
.PP
You can also run your own Certification Authority using products such as
Microsoft Certificate Server or the Entrust CA product for your
organization.
With the \f[V]keytool\f[R] command, it is possible to display, import,
and export certificates.
It is also possible to generate self-signed certificates.
.PP
The \f[V]keytool\f[R] command currently handles X.509 certificates.
.RE
.TP
X.509 Certificates
The X.509 standard defines what information can go into a certificate
and describes how to write it down (the data format).
All the data in a certificate is encoded with two related standards
called ASN.1/DER.
Abstract Syntax Notation 1 describes data.
The Definite Encoding Rules describe a single way to store and transfer
that data.
.RS
.PP
All X.509 certificates have the following data, in addition to the
signature:
.IP \[bu] 2
Version: This identifies which version of the X.509 standard applies to
this certificate, which affects what information can be specified in it.
Thus far, three versions are defined.
The \f[V]keytool\f[R] command can import and export v1, v2, and v3
certificates.
It generates v3 certificates.
.RS 2
.IP \[bu] 2
X.509 Version 1 has been available since 1988, is widely deployed, and
is the most generic.
.IP \[bu] 2
X.509 Version 2 introduced the concept of subject and issuer unique
identifiers to handle the possibility of reuse of subject or issuer
names over time.
Most certificate profile documents strongly recommend that names not be
reused and that certificates shouldn\[aq]t make use of unique
identifiers.
Version 2 certificates aren\[aq]t widely used.
.IP \[bu] 2
X.509 Version 3 is the most recent (1996) and supports the notion of
extensions where anyone can define an extension and include it in the
certificate.
Some common extensions are: KeyUsage (limits the use of the keys to
particular purposes such as \f[V]signing-only\f[R]) and AlternativeNames
(allows other identities to also be associated with this public key, for
example.
DNS names, email addresses, IP addresses).
Extensions can be marked critical to indicate that the extension should
be checked and enforced or used.
For example, if a certificate has the KeyUsage extension marked critical
and set to \f[V]keyCertSign\f[R], then when this certificate is
presented during SSL communication, it should be rejected because the
certificate extension indicates that the associated private key should
only be used for signing certificates and not for SSL use.
.RE
.IP \[bu] 2
Serial number: The entity that created the certificate is responsible
for assigning it a serial number to distinguish it from other
certificates it issues.
This information is used in numerous ways.
For example, when a certificate is revoked its serial number is placed
in a Certificate Revocation List (CRL).
.IP \[bu] 2
Signature algorithm identifier: This identifies the algorithm used by
the CA to sign the certificate.
.IP \[bu] 2
Issuer name: The X.500 Distinguished Name of the entity that signed the
certificate.
This is typically a CA.
Using this certificate implies trusting the entity that signed this
certificate.
In some cases, such as root or top-level CA certificates, the issuer
signs its own certificate.
.IP \[bu] 2
Validity period: Each certificate is valid only for a limited amount of
time.
This period is described by a start date and time and an end date and
time, and can be as short as a few seconds or almost as long as a
century.
The validity period chosen depends on a number of factors, such as the
strength of the private key used to sign the certificate, or the amount
one is willing to pay for a certificate.
This is the expected period that entities can rely on the public value,
when the associated private key has not been compromised.
.IP \[bu] 2
Subject name: The name of the entity whose public key the certificate
identifies.
This name uses the X.500 standard, so it is intended to be unique across
the Internet.
This is the X.500 Distinguished Name (DN) of the entity.
For example,
.RS 2
.RS
.PP
\f[V]CN=Java Duke, OU=Java Software Division, O=Oracle Corporation, C=US\f[R]
.RE
.PP
These refer to the subject\[aq]s common name (CN), organizational unit
(OU), organization (O), and country (C).
.RE
.IP \[bu] 2
Subject public key information: This is the public key of the entity
being named with an algorithm identifier that specifies which public key
crypto system this key belongs to and any associated key parameters.
.RE
.TP
Certificate Chains
The \f[V]keytool\f[R] command can create and manage keystore key entries
that each contain a private key and an associated certificate chain.
The first certificate in the chain contains the public key that
corresponds to the private key.
.RS
.PP
When keys are first generated, the chain usually starts off containing a
single element, a self-signed certificate.
See -genkeypair in \f[B]Commands\f[R].
A self-signed certificate is one for which the issuer (signer) is the
same as the subject.
The subject is the entity whose public key is being authenticated by the
certificate.
When the \f[V]-genkeypair\f[R] command is called to generate a new
public/private key pair, it also wraps the public key into a self-signed
certificate (unless the \f[V]-signer\f[R] option is specified).
.PP
Later, after a Certificate Signing Request (CSR) was generated with the
\f[V]-certreq\f[R] command and sent to a Certification Authority (CA),
the response from the CA is imported with \f[V]-importcert\f[R], and the
self-signed certificate is replaced by a chain of certificates.
At the bottom of the chain is the certificate (reply) issued by the CA
authenticating the subject\[aq]s public key.
The next certificate in the chain is one that authenticates the CA\[aq]s
public key.
.PP
In many cases, this is a self-signed certificate, which is a certificate
from the CA authenticating its own public key, and the last certificate
in the chain.
In other cases, the CA might return a chain of certificates.
In this case, the bottom certificate in the chain is the same (a
certificate signed by the CA, authenticating the public key of the key
entry), but the second certificate in the chain is a certificate signed
by a different CA that authenticates the public key of the CA you sent
the CSR to.
The next certificate in the chain is a certificate that authenticates
the second CA\[aq]s key, and so on, until a self-signed root certificate
is reached.
Each certificate in the chain (after the first) authenticates the public
key of the signer of the previous certificate in the chain.
.PP
Many CAs only return the issued certificate, with no supporting chain,
especially when there is a flat hierarchy (no intermediates CAs).
In this case, the certificate chain must be established from trusted
certificate information already stored in the keystore.
.PP
A different reply format (defined by the PKCS #7 standard) includes the
supporting certificate chain in addition to the issued certificate.
Both reply formats can be handled by the \f[V]keytool\f[R] command.
.PP
The top-level (root) CA certificate is self-signed.
However, the trust into the root\[aq]s public key doesn\[aq]t come from
the root certificate itself, but from other sources such as a newspaper.
This is because anybody could generate a self-signed certificate with
the distinguished name of, for example, the DigiCert root CA.
The root CA public key is widely known.
The only reason it is stored in a certificate is because this is the
format understood by most tools, so the certificate in this case is only
used as a vehicle to transport the root CA\[aq]s public key.
Before you add the root CA certificate to your keystore, you should view
it with the \f[V]-printcert\f[R] option and compare the displayed
fingerprint with the well-known fingerprint obtained from a newspaper,
the root CA\[aq]s Web page, and so on.
.RE
.TP
cacerts Certificates File
A certificates file named \f[V]cacerts\f[R] resides in the security
properties directory:
.RS
.IP \[bu] 2
\f[B]Linux and macOS:\f[R] \f[I]JAVA_HOME\f[R]\f[V]/lib/security\f[R]
.IP \[bu] 2
\f[B]Windows:\f[R] \f[I]JAVA_HOME\f[R]\f[V]\[rs]lib\[rs]security\f[R]
.PP
The \f[V]cacerts\f[R] file represents a system-wide keystore with CA
certificates.
System administrators can configure and manage that file with the
\f[V]keytool\f[R] command by specifying \f[V]jks\f[R] as the keystore
type.
The \f[V]cacerts\f[R] keystore file ships with a default set of root CA
certificates.
For Linux, macOS, and Windows, you can list the default certificates
with the following command:
.RS
.PP
\f[V]keytool -list -cacerts\f[R]
.RE
.PP
The initial password of the \f[V]cacerts\f[R] keystore file is
\f[V]changeit\f[R].
System administrators should change that password and the default access
permission of that file upon installing the SDK.
.PP
\f[B]Note:\f[R]
.PP
It is important to verify your \f[V]cacerts\f[R] file.
Because you trust the CAs in the \f[V]cacerts\f[R] file as entities for
signing and issuing certificates to other entities, you must manage the
\f[V]cacerts\f[R] file carefully.
The \f[V]cacerts\f[R] file should contain only certificates of the CAs
you trust.
It is your responsibility to verify the trusted root CA certificates
bundled in the \f[V]cacerts\f[R] file and make your own trust decisions.
.PP
To remove an untrusted CA certificate from the \f[V]cacerts\f[R] file,
use the \f[V]-delete\f[R] option of the \f[V]keytool\f[R] command.
You can find the \f[V]cacerts\f[R] file in the JDK\[aq]s
\f[V]$JAVA_HOME/lib/security\f[R] directory.
Contact your system administrator if you don\[aq]t have permission to
edit this file.
.RE
.TP
Internet RFC 1421 Certificate Encoding Standard
Certificates are often stored using the printable encoding format
defined by the Internet RFC 1421 standard, instead of their binary
encoding.
This certificate format, also known as Base64 encoding, makes it easy to
export certificates to other applications by email or through some other
mechanism.
.RS
.PP
Certificates read by the \f[V]-importcert\f[R] and \f[V]-printcert\f[R]
commands can be in either this format or binary encoded.
The \f[V]-exportcert\f[R] command by default outputs a certificate in
binary encoding, but will instead output a certificate in the printable
encoding format, when the \f[V]-rfc\f[R] option is specified.
.PP
The \f[V]-list\f[R] command by default prints the SHA-256 fingerprint of
a certificate.
If the \f[V]-v\f[R] option is specified, then the certificate is printed
in human-readable format.
If the \f[V]-rfc\f[R] option is specified, then the certificate is
output in the printable encoding format.
.PP
In its printable encoding format, the encoded certificate is bounded at
the beginning and end by the following text:
.IP
.nf
\f[CB]
-----BEGIN CERTIFICATE-----

encoded certificate goes here.

-----END CERTIFICATE-----
\f[R]
.fi
.RE
.TP
X.500 Distinguished Names
X.500 Distinguished Names are used to identify entities, such as those
that are named by the \f[V]subject\f[R] and \f[V]issuer\f[R] (signer)
fields of X.509 certificates.
The \f[V]keytool\f[R] command supports the following subparts:
.RS
.IP \[bu] 2
commonName: The common name of a person such as Susan Jones.
.IP \[bu] 2
organizationUnit: The small organization (such as department or
division) name.
For example, Purchasing.
.IP \[bu] 2
localityName: The locality (city) name, for example, Palo Alto.
.IP \[bu] 2
stateName: State or province name, for example, California.
.IP \[bu] 2
country: Two-letter country code, for example, CH.
.PP
When you supply a distinguished name string as the value of a
\f[V]-dname\f[R] option, such as for the \f[V]-genkeypair\f[R] command,
the string must be in the following format:
.RS
.PP
\f[V]CN=cName, OU=orgUnit, O=org, L=city, S=state, C=countryCode\f[R]
.RE
.PP
All the following items represent actual values and the previous
keywords are abbreviations for the following:
.IP
.nf
\f[CB]
CN=commonName
OU=organizationUnit
O=organizationName
L=localityName
S=stateName
C=country
\f[R]
.fi
.PP
A sample distinguished name string is:
.RS
.PP
\f[V]CN=Mark Smith, OU=Java, O=Oracle, L=Cupertino, S=California, C=US\f[R]
.RE
.PP
A sample command using such a string is:
.RS
.PP
\f[V]keytool -genkeypair -dname \[dq]CN=Mark Smith, OU=Java, O=Oracle, L=Cupertino, S=California, C=US\[dq] -alias mark -keyalg rsa\f[R]
.RE
.PP
Case doesn\[aq]t matter for the keyword abbreviations.
For example, CN, cn, and Cn are all treated the same.
.PP
Order matters; each subcomponent must appear in the designated order.
However, it isn\[aq]t necessary to have all the subcomponents.
You can use a subset, for example:
.RS
.PP
\f[V]CN=Smith, OU=Java, O=Oracle, C=US\f[R]
.RE
.PP
If a distinguished name string value contains a comma, then the comma
must be escaped by a backslash (\[rs]) character when you specify the
string on a command line, as in:
.RS
.PP
\f[V]cn=Jack, ou=Java\[rs], Product Development, o=Oracle, c=US\f[R]
.RE
.PP
It is never necessary to specify a distinguished name string on a
command line.
When the distinguished name is needed for a command, but not supplied on
the command line, the user is prompted for each of the subcomponents.
In this case, a comma doesn\[aq]t need to be escaped by a backslash
(\[rs]).
.RE
.SH WARNINGS
.SH IMPORTING TRUSTED CERTIFICATES WARNING
.PP
\f[B]Important\f[R]: Be sure to check a certificate very carefully
before importing it as a trusted certificate.
.PP
\f[B]Windows Example:\f[R]
.PP
View the certificate first with the \f[V]-printcert\f[R] command or the
\f[V]-importcert\f[R] command without the \f[V]-noprompt\f[R] option.
Ensure that the displayed certificate fingerprints match the expected
ones.
For example, suppose someone sends or emails you a certificate that you
put it in a file named \f[V]\[rs]tmp\[rs]cert\f[R].
Before you consider adding the certificate to your list of trusted
certificates, you can execute a \f[V]-printcert\f[R] command to view its
fingerprints, as follows:
.IP
.nf
\f[CB]
  keytool -printcert -file \[rs]tmp\[rs]cert
    Owner: CN=ll, OU=ll, O=ll, L=ll, S=ll, C=ll
    Issuer: CN=ll, OU=ll, O=ll, L=ll, S=ll, C=ll
    Serial Number: 59092b34
    Valid from: Thu Jun 24 18:01:13 PDT 2016 until: Wed Jun 23 17:01:13 PST 2016
    Certificate Fingerprints:

                   SHA-1: 20:B6:17:FA:EF:E5:55:8A:D0:71:1F:E8:D6:9D:C0:37:13:0E:5E:FE
                 SHA-256: 90:7B:70:0A:EA:DC:16:79:92:99:41:FF:8A:FE:EB:90:
                          17:75:E0:90:B2:24:4D:3A:2A:16:A6:E4:11:0F:67:A4
\f[R]
.fi
.PP
\f[B]Linux Example:\f[R]
.PP
View the certificate first with the \f[V]-printcert\f[R] command or the
\f[V]-importcert\f[R] command without the \f[V]-noprompt\f[R] option.
Ensure that the displayed certificate fingerprints match the expected
ones.
For example, suppose someone sends or emails you a certificate that you
put it in a file named \f[V]/tmp/cert\f[R].
Before you consider adding the certificate to your list of trusted
certificates, you can execute a \f[V]-printcert\f[R] command to view its
fingerprints, as follows:
.IP
.nf
\f[CB]
  keytool -printcert -file /tmp/cert
    Owner: CN=ll, OU=ll, O=ll, L=ll, S=ll, C=ll
    Issuer: CN=ll, OU=ll, O=ll, L=ll, S=ll, C=ll
    Serial Number: 59092b34
    Valid from: Thu Jun 24 18:01:13 PDT 2016 until: Wed Jun 23 17:01:13 PST 2016
    Certificate Fingerprints:

                   SHA-1: 20:B6:17:FA:EF:E5:55:8A:D0:71:1F:E8:D6:9D:C0:37:13:0E:5E:FE
                   SHA-256: 90:7B:70:0A:EA:DC:16:79:92:99:41:FF:8A:FE:EB:90:
                           17:75:E0:90:B2:24:4D:3A:2A:16:A6:E4:11:0F:67:A4
\f[R]
.fi
.PP
Then call or otherwise contact the person who sent the certificate and
compare the fingerprints that you see with the ones that they show.
Only when the fingerprints are equal is it guaranteed that the
certificate wasn\[aq]t replaced in transit with somebody else\[aq]s
certificate such as an attacker\[aq]s certificate.
If such an attack took place, and you didn\[aq]t check the certificate
before you imported it, then you would be trusting anything the attacker
signed, for example, a JAR file with malicious class files inside.
.PP
\f[B]Note:\f[R]
.PP
It isn\[aq]t required that you execute a \f[V]-printcert\f[R] command
before importing a certificate.
This is because before you add a certificate to the list of trusted
certificates in the keystore, the \f[V]-importcert\f[R] command prints
out the certificate information and prompts you to verify it.
You can then stop the import operation.
However, you can do this only when you call the \f[V]-importcert\f[R]
command without the \f[V]-noprompt\f[R] option.
If the \f[V]-noprompt\f[R] option is specified, then there is no
interaction with the user.
.SH PASSWORDS WARNING
.PP
Most commands that operate on a keystore require the store password.
Some commands require a private/secret key password.
Passwords can be specified on the command line in the
\f[V]-storepass\f[R] and \f[V]-keypass\f[R] options.
However, a password shouldn\[aq]t be specified on a command line or in a
script unless it is for testing, or you are on a secure system.
When you don\[aq]t specify a required password option on a command line,
you are prompted for it.
.SH CERTIFICATE CONFORMANCE WARNING
.PP
\f[B]Internet X.509 Public Key Infrastructure Certificate and
Certificate Revocation List (CRL) Profile\f[R]
[https://tools.ietf.org/rfc/rfc5280.txt] defined a profile on conforming
X.509 certificates, which includes what values and value combinations
are valid for certificate fields and extensions.
.PP
The \f[V]keytool\f[R] command doesn\[aq]t enforce all of these rules so
it can generate certificates that don\[aq]t conform to the standard,
such as self-signed certificates that would be used for internal testing
purposes.
Certificates that don\[aq]t conform to the standard might be rejected by
the JDK or other applications.
Users should ensure that they provide the correct options for
\f[V]-dname\f[R], \f[V]-ext\f[R], and so on.
.SH IMPORT A NEW TRUSTED CERTIFICATE
.PP
Before you add the certificate to the keystore, the \f[V]keytool\f[R]
command verifies it by attempting to construct a chain of trust from
that certificate to a self-signed certificate (belonging to a root CA),
using trusted certificates that are already available in the keystore.
.PP
If the \f[V]-trustcacerts\f[R] option was specified, then additional
certificates are considered for the chain of trust, namely the
certificates in a file named \f[V]cacerts\f[R].
.PP
If the \f[V]keytool\f[R] command fails to establish a trust path from
the certificate to be imported up to a self-signed certificate (either
from the keystore or the \f[V]cacerts\f[R] file), then the certificate
information is printed, and the user is prompted to verify it by
comparing the displayed certificate fingerprints with the fingerprints
obtained from some other (trusted) source of information, which might be
the certificate owner.
Be very careful to ensure the certificate is valid before importing it
as a trusted certificate.
The user then has the option of stopping the import operation.
If the \f[V]-noprompt\f[R] option is specified, then there is no
interaction with the user.
.SH IMPORT A CERTIFICATE REPLY
.PP
When you import a certificate reply, the certificate reply is validated
with trusted certificates from the keystore, and optionally, the
certificates configured in the \f[V]cacerts\f[R] keystore file when the
\f[V]-trustcacerts\f[R] option is specified.
.PP
The methods of determining whether the certificate reply is trusted are
as follows:
.IP \[bu] 2
If the reply is a single X.509 certificate, then the \f[V]keytool\f[R]
command attempts to establish a trust chain, starting at the certificate
reply and ending at a self-signed certificate (belonging to a root CA).
The certificate reply and the hierarchy of certificates is used to
authenticate the certificate reply from the new certificate chain of
aliases.
If a trust chain can\[aq]t be established, then the certificate reply
isn\[aq]t imported.
In this case, the \f[V]keytool\f[R] command doesn\[aq]t print the
certificate and prompt the user to verify it, because it is very
difficult for a user to determine the authenticity of the certificate
reply.
.IP \[bu] 2
If the reply is a PKCS #7 formatted certificate chain or a sequence of
X.509 certificates, then the chain is ordered with the user certificate
first followed by zero or more CA certificates.
If the chain ends with a self-signed root CA certificate and
the\f[V]-trustcacerts\f[R] option was specified, the \f[V]keytool\f[R]
command attempts to match it with any of the trusted certificates in the
keystore or the \f[V]cacerts\f[R] keystore file.
If the chain doesn\[aq]t end with a self-signed root CA certificate and
the \f[V]-trustcacerts\f[R] option was specified, the \f[V]keytool\f[R]
command tries to find one from the trusted certificates in the keystore
or the \f[V]cacerts\f[R] keystore file and add it to the end of the
chain.
If the certificate isn\[aq]t found and the \f[V]-noprompt\f[R] option
isn\[aq]t specified, the information of the last certificate in the
chain is printed, and the user is prompted to verify it.
.PP
If the public key in the certificate reply matches the user\[aq]s public
key already stored with \f[V]alias\f[R], then the old certificate chain
is replaced with the new certificate chain in the reply.
The old chain can only be replaced with a valid \f[V]keypass\f[R], and
so the password used to protect the private key of the entry is
supplied.
If no password is provided, and the private key password is different
from the keystore password, the user is prompted for it.
.PP
This command was named \f[V]-import\f[R] in earlier releases.
This old name is still supported in this release.
The new name, \f[V]-importcert\f[R], is preferred.
