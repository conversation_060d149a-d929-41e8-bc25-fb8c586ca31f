import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

class JournalEntry {
  final String id;
  final String title;
  final String content;
  final DateTime createdAt;
  final String? sessionId;
  final String? counselorName;
  final JournalEntryType type;
  final List<String>? prompts;

  JournalEntry({
    required this.id,
    required this.title,
    required this.content,
    required this.createdAt,
    this.sessionId,
    this.counselorName,
    required this.type,
    this.prompts,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'content': content,
    'createdAt': createdAt.millisecondsSinceEpoch,
    'sessionId': sessionId,
    'counselorName': counselorName,
    'type': type.toString(),
    'prompts': prompts,
  };

  factory JournalEntry.fromJson(Map<String, dynamic> json) => JournalEntry(
    id: json['id'],
    title: json['title'],
    content: json['content'],
    createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
    sessionId: json['sessionId'],
    counselorName: json['counselorName'],
    type: JournalEntryType.values.firstWhere(
      (e) => e.toString() == json['type'],
      orElse: () => JournalEntryType.personal,
    ),
    prompts:
        json['prompts'] != null ? List<String>.from(json['prompts']) : null,
  );

  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }
}

enum JournalEntryType { personal, sessionPrompts, reflection }

class JournalService {
  static const String _journalKey = 'journal_entries_v1';
  static const Uuid _uuid = Uuid();

  /// Create a new journal entry
  static Future<JournalEntry> createEntry({
    required String title,
    required String content,
    String? sessionId,
    String? counselorName,
    required JournalEntryType type,
    List<String>? prompts,
  }) async {
    final entry = JournalEntry(
      id: _uuid.v4(),
      title: title,
      content: content,
      createdAt: DateTime.now(),
      sessionId: sessionId,
      counselorName: counselorName,
      type: type,
      prompts: prompts,
    );

    await _saveEntry(entry);
    return entry;
  }

  /// Create journal entry from session prompts
  static Future<JournalEntry> createFromSessionPrompts({
    required String sessionId,
    required String counselorName,
    required String sessionTitle,
    required List<String> prompts,
  }) async {
    final content = prompts
        .asMap()
        .entries
        .map(
          (entry) =>
              '${entry.key + 1}. ${entry.value}\n\n[Your reflection here]\n',
        )
        .join('\n');

    return await createEntry(
      title: 'Session with $counselorName: $sessionTitle',
      content: content,
      sessionId: sessionId,
      counselorName: counselorName,
      type: JournalEntryType.sessionPrompts,
      prompts: prompts,
    );
  }

  /// Save journal entry to local storage
  static Future<void> _saveEntry(JournalEntry entry) async {
    final prefs = await SharedPreferences.getInstance();
    final entries = await getAllEntries();

    // Update existing entry or add new one
    final index = entries.indexWhere((e) => e.id == entry.id);
    if (index >= 0) {
      entries[index] = entry;
    } else {
      entries.add(entry);
    }

    // Sort by creation date (newest first)
    entries.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    final entriesJson = entries.map((e) => json.encode(e.toJson())).toList();
    await prefs.setStringList(_journalKey, entriesJson);
  }

  /// Get all journal entries
  static Future<List<JournalEntry>> getAllEntries() async {
    final prefs = await SharedPreferences.getInstance();
    final entriesJson = prefs.getStringList(_journalKey) ?? [];

    return entriesJson
        .map((entryStr) => JournalEntry.fromJson(json.decode(entryStr)))
        .toList();
  }

  /// Get entries by type
  static Future<List<JournalEntry>> getEntriesByType(
    JournalEntryType type,
  ) async {
    final entries = await getAllEntries();
    return entries.where((entry) => entry.type == type).toList();
  }

  /// Get entries for a specific session
  static Future<List<JournalEntry>> getEntriesForSession(
    String sessionId,
  ) async {
    final entries = await getAllEntries();
    return entries.where((entry) => entry.sessionId == sessionId).toList();
  }

  /// Update an existing journal entry
  static Future<JournalEntry> updateEntry(JournalEntry entry) async {
    await _saveEntry(entry);
    return entry;
  }

  /// Delete a journal entry
  static Future<void> deleteEntry(String entryId) async {
    final prefs = await SharedPreferences.getInstance();
    final entries = await getAllEntries();
    entries.removeWhere((e) => e.id == entryId);

    final entriesJson = entries.map((e) => json.encode(e.toJson())).toList();
    await prefs.setStringList(_journalKey, entriesJson);
  }

  /// Get recent entries (last 30 days)
  static Future<List<JournalEntry>> getRecentEntries() async {
    final entries = await getAllEntries();
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));

    return entries
        .where((entry) => entry.createdAt.isAfter(thirtyDaysAgo))
        .toList();
  }

  /// Search entries by title or content
  static Future<List<JournalEntry>> searchEntries(String query) async {
    final entries = await getAllEntries();
    final lowerQuery = query.toLowerCase();

    return entries
        .where(
          (entry) =>
              entry.title.toLowerCase().contains(lowerQuery) ||
              entry.content.toLowerCase().contains(lowerQuery),
        )
        .toList();
  }
}
