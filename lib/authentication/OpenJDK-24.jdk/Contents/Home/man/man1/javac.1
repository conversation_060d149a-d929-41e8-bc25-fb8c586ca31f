.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JAVAC" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
javac - read Java declarations and compile them into class files
.SH SYNOPSIS
.PP
\f[V]javac\f[R] [\f[I]options\f[R]]
[\f[I]sourcefiles-or-classnames\f[R]]
.TP
\f[I]options\f[R]
Command-line options.
.TP
\f[I]sourcefiles-or-classnames\f[R]
Source files to be compiled (for example, \f[V]Shape.java\f[R]) or the
names of previously compiled classes to be processed for annotations
(for example, \f[V]geometry.MyShape\f[R]).
.SH DESCRIPTION
.PP
The \f[V]javac\f[R] command reads \f[I]source files\f[R] that contain
module, package and type declarations written in the Java programming
language, and compiles them into \f[I]class files\f[R] that run on the
Java Virtual Machine.
.PP
The \f[V]javac\f[R] command can also \f[B]process annotations\f[R] in
Java source files and classes.
.PP
Source files must have a file name extension of \f[V].java\f[R].
Class files have a file name extension of \f[V].class\f[R].
Both source and class files normally have file names that identify the
contents.
For example, a class called \f[V]Shape\f[R] would be declared in a
source file called \f[V]Shape.java\f[R], and compiled into a class file
called \f[V]Shape.class\f[R].
.PP
There are two ways to specify source files to \f[V]javac\f[R]:
.IP \[bu] 2
For a small number of source files, you can list their file names on the
command line.
.IP \[bu] 2
For a large number of source files, you can use the
\f[B]\f[VB]\[at]\f[B]\f[BI]filename\f[B]\f[R] option on the command line
to specify an \f[I]argument file\f[R] that lists their file names.
See \f[B]Standard Options\f[R] for a description of the option and
\f[B]Command-Line Argument Files\f[R] for a description of
\f[V]javac\f[R] argument files.
.PP
The order of source files specified on the command line or in an
argument file is not important.
\f[V]javac\f[R] will compile the files together, as a group, and will
automatically resolve any dependencies between the declarations in the
various source files.
.PP
\f[V]javac\f[R] expects that source files are arranged in one or more
directory hierarchies on the file system, described in \f[B]Arrangement
of Source Code\f[R].
.PP
To compile a source file, \f[V]javac\f[R] needs to find the declaration
of every class or interface that is used, extended, or implemented by
the code in the source file.
This lets \f[V]javac\f[R] check that the code has the right to access
those classes and interfaces.
Rather than specifying the source files of those classes and interfaces
explicitly, you can use command-line options to tell \f[V]javac\f[R]
where to search for their source files.
If you have compiled those source files previously, you can use options
to tell \f[V]javac\f[R] where to search for the corresponding class
files.
The options, which all have names ending in \[dq]path\[dq], are
described in \f[B]Standard Options\f[R], and further described in
\f[B]Configuring a Compilation\f[R] and \f[B]Searching for Module,
Package and Type Declarations\f[R].
.PP
By default, \f[V]javac\f[R] compiles each source file to a class file in
the same directory as the source file.
However, it is recommended to specify a separate destination directory
with the \f[B]\f[VB]-d\f[B]\f[R] option.
.PP
Command-line \f[B]options\f[R] and \f[B]environment variables\f[R] also
control how \f[V]javac\f[R] performs various tasks:
.IP \[bu] 2
Compiling code to run on earlier releases of the JDK.
.IP \[bu] 2
Compiling code to run under a debugger.
.IP \[bu] 2
Checking for stylistic issues in Java source code.
.IP \[bu] 2
Checking for problems in \f[V]javadoc\f[R] comments
(\f[V]/** ... */\f[R]).
.IP \[bu] 2
Processing annotations in source files and class files.
.IP \[bu] 2
Upgrading and patching modules in the compile-time environment.
.PP
\f[V]javac\f[R] supports \f[B]Compiling for Earlier Releases Of The
Platform\f[R] and can also be invoked from Java code using one of a
number of \f[B]APIs\f[R]
.SH OPTIONS
.PP
\f[V]javac\f[R] provides \f[B]standard options\f[R], and \f[B]extra
options\f[R] that are either non-standard or are for advanced use.
.PP
Some options take one or more arguments.
If an argument contains spaces or other whitespace characters, the value
should be quoted according to the conventions of the environment being
used to invoke javac.
If the option begins with a single dash (\f[V]-\f[R]) the argument
should either directly follow the option name, or should be separated
with a colon (\f[V]:\f[R]) or whitespace, depending on the option.
If the option begins with a double dash (\f[V]--\f[R]), the argument may
be separated either by whitespace or by an equals (\f[V]=\f[R])
character with no additional whitespace.
For example,
.IP
.nf
\f[CB]
-Aname=\[dq]J. Duke\[dq]
-proc:only
-d myDirectory
--module-version 3
--module-version=3
\f[R]
.fi
.PP
In the following lists of options, an argument of \f[I]path\f[R]
represents a search path, composed of a list of file system locations
separated by the platform path separator character, (semicolon
\f[V];\f[R] on Windows, or colon \f[V]:\f[R] on other systems.)
Depending on the option, the file system locations may be directories,
JAR files or JMOD files.
.SS Standard Options
.TP
\f[V]\[at]\f[R]\f[I]filename\f[R]
Reads options and file names from a file.
To shorten or simplify the \f[V]javac\f[R] command, you can specify one
or more files that contain arguments to the \f[V]javac\f[R] command
(except \f[B]\f[VB]-J\f[B]\f[R] options).
This lets you to create \f[V]javac\f[R] commands of any length on any
operating system.
See \f[B]Command-Line Argument Files\f[R].
.TP
\f[V]-A\f[R]\f[I]key\f[R][\f[V]=\f[R]\f[I]value\f[R]]
Specifies options to pass to annotation processors.
These options are not interpreted by \f[V]javac\f[R] directly, but are
made available for use by individual processors.
The \f[I]key\f[R] value should be one or more identifiers separated by a
dot (\f[V].\f[R]).
.TP
\f[V]--add-modules\f[R] \f[I]module\f[R]\f[V],\f[R]\f[I]module\f[R]
Specifies root modules to resolve in addition to the initial modules, or
all modules on the module path if \f[I]module\f[R] is
\f[V]ALL-MODULE-PATH\f[R].
.TP
\f[V]--boot-class-path\f[R] \f[I]path\f[R] or \f[V]-bootclasspath\f[R] \f[I]path\f[R]
Overrides the location of the bootstrap class files.
.RS
.PP
\f[B]Note:\f[R] This can only be used when compiling for releases prior
to JDK 9.
As applicable, see the descriptions in \f[B]\f[VB]--release\f[B]\f[R],
\f[B]\f[VB]-source\f[B]\f[R], or \f[B]\f[VB]-target\f[B]\f[R] for
details.
For JDK 9 or later, see \f[B]\f[VB]--system\f[B]\f[R].
.RE
.TP
\f[V]--class-path\f[R] \f[I]path\f[R], \f[V]-classpath\f[R] \f[I]path\f[R], or \f[V]-cp\f[R] \f[I]path\f[R]
Specifies where to find user class files and annotation processors.
This class path overrides the user class path in the \f[V]CLASSPATH\f[R]
environment variable.
.RS
.IP \[bu] 2
If \f[V]--class-path\f[R], \f[V]-classpath\f[R], or \f[V]-cp\f[R] are
not specified, then the user class path is the value of the
\f[V]CLASSPATH\f[R] environment variable, if that is set, or else the
current directory.
.IP \[bu] 2
If not compiling code for modules, if the
\f[B]\f[VB]--source-path\f[B]\f[R] or -sourcepath\[ga] option is not
specified, then the user class path is also searched for source files.
.IP \[bu] 2
If the \f[B]\f[VB]-processorpath\f[B]\f[R] option is not specified, then
the class path is also searched for annotation processors.
.RE
.TP
\f[V]-d\f[R] \f[I]directory\f[R]
Sets the destination directory (or \f[I]class output directory\f[R]) for
class files.
If a class is part of a package, then \f[V]javac\f[R] puts the class
file in a subdirectory that reflects the module name (if appropriate)
and package name.
The directory, and any necessary subdirectories, will be created if they
do not already exist.
.RS
.PP
If the \f[V]-d\f[R] option is not specified, then \f[V]javac\f[R] puts
each class file in the same directory as the source file from which it
was generated.
.PP
Except when compiling code for multiple modules, the contents of the
class output directory will be organized in a package hierarchy.
When compiling code for multiple modules, the contents of the output
directory will be organized in a module hierarchy, with the contents of
each module in a separate subdirectory, each organized as a package
hierarchy.
.PP
\f[B]Note:\f[R] When compiling code for one or more modules, the class
output directory will automatically be checked when searching for
previously compiled classes.
When not compiling for modules, for backwards compatibility, the
directory is \f[I]not\f[R] automatically checked for previously compiled
classes, and so it is recommended to specify the class output directory
as one of the locations on the user class path, using the
\f[V]--class-path\f[R] option or one of its alternate forms.
.RE
.TP
\f[V]-deprecation\f[R]
Shows a description of each use or override of a deprecated member or
class.
Without the \f[V]-deprecation\f[R] option, \f[V]javac\f[R] shows a
summary of the source files that use or override deprecated members or
classes.
The \f[V]-deprecation\f[R] option is shorthand for
\f[V]-Xlint:deprecation\f[R].
.TP
\f[V]--enable-preview\f[R]
Enables preview language features.
Used in conjunction with either \f[B]\f[VB]-source\f[B]\f[R] or
\f[B]\f[VB]--release\f[B]\f[R].
.TP
\f[V]-encoding\f[R] \f[I]encoding\f[R]
Specifies character encoding used by source files, such as EUC-JP and
UTF-8.
If the \f[V]-encoding\f[R] option is not specified, then the platform
default converter is used.
.TP
\f[V]-endorseddirs\f[R] \f[I]directories\f[R]
Overrides the location of the endorsed standards path.
.RS
.PP
\f[B]Note:\f[R] This can only be used when compiling for releases prior
to JDK 9.
As applicable, see the descriptions in \f[B]\f[VB]--release\f[B]\f[R],
\f[B]\f[VB]-source\f[B]\f[R], or \f[B]\f[VB]-target\f[B]\f[R] for
details.
.RE
.TP
\f[V]-extdirs\f[R] \f[I]directories\f[R]
Overrides the location of the installed extensions.
\f[V]directories\f[R] is a list of directories, separated by the
platform path separator (\f[V];\f[R] on Windows, and \f[V]:\f[R]
otherwise).
Each JAR file in the specified directories is searched for class files.
All JAR files found become part of the class path.
.RS
.PP
If you are compiling for a release of the platform that supports the
Extension Mechanism, then this option specifies the directories that
contain the extension classes.
See [Compiling for Other Releases of the Platform].
.PP
\f[B]Note:\f[R] This can only be used when compiling for releases prior
to JDK 9.
As applicable, see the descriptions in \f[B]\f[VB]--release\f[B]\f[R],
\f[B]\f[VB]-source\f[B]\f[R], or \f[B]\f[VB]-target\f[B]\f[R] for
details.
.RE
.TP
\f[V]-g\f[R]
Generates all debugging information, including local variables.
By default, only line number and source file information is generated.
.TP
\f[V]-g:\f[R][\f[V]lines\f[R], \f[V]vars\f[R], \f[V]source\f[R]]
Generates only the kinds of debugging information specified by the
comma-separated list of keywords.
Valid keywords are:
.RS
.TP
\f[V]lines\f[R]
Line number debugging information.
.TP
\f[V]vars\f[R]
Local variable debugging information.
.TP
\f[V]source\f[R]
Source file debugging information.
.RE
.TP
\f[V]-g:none\f[R]
Does not generate debugging information.
.TP
\f[V]-h\f[R] \f[I]directory\f[R]
Specifies where to place generated native header files.
.RS
.PP
When you specify this option, a native header file is generated for each
class that contains native methods or that has one or more constants
annotated with the \f[B]\f[VB]java.lang.annotation.Native\f[B]\f[R]
annotation.
If the class is part of a package, then the compiler puts the native
header file in a subdirectory that reflects the module name (if
appropriate) and package name.
The directory, and any necessary subdirectories, will be created if they
do not already exist.
.RE
.TP
\f[V]--help\f[R], \f[V]-help\f[R] or \f[V]-?\f[R]
Prints a synopsis of the standard options.
.TP
\f[V]--help-extra\f[R] or \f[V]-X\f[R]
Prints a synopsis of the set of extra options.
.TP
\f[V]--help-lint\f[R]
Prints the supported keys for the \f[V]-Xlint\f[R] option.
.TP
\f[V]-implicit:\f[R][\f[V]none\f[R], \f[V]class\f[R]]
Specifies whether or not to generate class files for implicitly
referenced files:
.RS
.IP \[bu] 2
\f[V]-implicit:class\f[R] --- Automatically generates class files.
.IP \[bu] 2
\f[V]-implicit:none\f[R] --- Suppresses class file generation.
.PP
If this option is not specified, then the default automatically
generates class files.
In this case, the compiler issues a warning if any class files are
generated when also doing annotation processing.
The warning is not issued when the \f[V]-implicit\f[R] option is
explicitly set.
See \f[B]Searching for Module, Package and Type Declarations\f[R].
.RE
.TP
\f[V]-J\f[R]\f[I]option\f[R]
Passes \f[I]option\f[R] to the runtime system, where \f[I]option\f[R] is
one of the Java options described on \f[B]java\f[R] command.
For example, \f[V]-J-Xms48m\f[R] sets the startup memory to 48 MB.
.RS
.PP
\f[B]Note:\f[R] The \f[V]CLASSPATH\f[R] environment variable,
\f[V]-classpath\f[R] option, \f[V]-bootclasspath\f[R] option, and
\f[V]-extdirs\f[R] option do not specify the classes used to run
\f[V]javac\f[R].
Trying to customize the compiler implementation with these options and
variables is risky and often does not accomplish what you want.
If you must customize the compiler implementation, then use the
\f[V]-J\f[R] option to pass options through to the underlying Java
launcher.
.RE
.TP
\f[V]--limit-modules\f[R] \f[I]module\f[R]\f[V],\f[R]\f[I]module\f[R]*
Limits the universe of observable modules.
.TP
\f[V]--module\f[R] \f[I]module-name\f[R] (\f[V],\f[R]\f[I]module-name\f[R])* or \f[V]-m\f[R] \f[I]module-name\f[R] (\f[V],\f[R]\f[I]module-name\f[R])*
Compiles those source files in the named modules that are newer than the
corresponding files in the output directory.
.TP
\f[V]--module-path\f[R] \f[I]path\f[R] or \f[V]-p\f[R] \f[I]path\f[R]
Specifies where to find application modules.
.TP
\f[V]--module-source-path\f[R] \f[I]module-source-path\f[R]
Specifies where to find source files when compiling code in multiple
modules.
See \f[B]The Module Source Path Option\f[R].
.TP
\f[V]--module-version\f[R] \f[I]version\f[R]
Specifies the version of modules that are being compiled.
.TP
\f[V]-nowarn\f[R]
Disables warning messages.
This option operates the same as the \f[V]-Xlint:none\f[R] option.
.TP
\f[V]-parameters\f[R]
Generates metadata for reflection on method parameters.
Stores formal parameter names of constructors and methods in the
generated class file so that the method
\f[V]java.lang.reflect.Executable.getParameters\f[R] from the Reflection
API can retrieve them.
.TP
\f[V]-proc:\f[R][\f[V]none\f[R], \f[V]only\f[R], \f[V]full\f[R]]
Controls whether annotation processing and compilation are done.
.RS
.IP \[bu] 2
\f[V]-proc:none\f[R] means that compilation takes place without
annotation processing
.IP \[bu] 2
\f[V]-proc:only\f[R] means that only annotation processing is done,
without any subsequent compilation.
.IP \[bu] 2
\f[V]-proc:full\f[R] means annotation processing and compilation are
done.
.PP
If this option is not used, annotation processing and compilation are
done if at least one other option is used to explicitly configure
annotation processing.
.RE
.TP
\f[V]-processor\f[R] \f[I]class1\f[R][\f[V],\f[R]\f[I]class2\f[R]\f[V],\f[R]\f[I]class3\f[R]...]
Names of the annotation processors to run.
This bypasses the default discovery process.
.TP
\f[V]--processor-module-path\f[R] \f[I]path\f[R]
Specifies the module path used for finding annotation processors.
.TP
\f[V]--processor-path\f[R] \f[I]path\f[R] or \f[V]-processorpath\f[R] \f[I]path\f[R]
Specifies where to find annotation processors.
If this option is not used, then the class path is searched for
processors.
.TP
\f[V]-profile\f[R] \f[I]profile\f[R]
Checks that the API used is available in the specified profile.
This option is deprecated and may be removed in a future release.
.RS
.PP
\f[B]Note:\f[R] This can only be used when compiling for releases prior
to JDK 9.
As applicable, see the descriptions in \f[B]\f[VB]--release\f[B]\f[R],
\f[B]\f[VB]-source\f[B]\f[R], or \f[B]\f[VB]-target\f[B]\f[R] for
details.
.RE
.TP
\f[V]--release\f[R] \f[I]release\f[R]
Compiles source code according to the rules of the Java programming
language for the specified Java SE release, generating class files which
target that release.
Source code is compiled against the combined Java SE and JDK API for the
specified release.
.RS
.PP
The supported values of \f[I]release\f[R] are the current Java SE
release and a limited number of previous releases, detailed in the
command-line help.
.PP
For the current release, the Java SE API consists of the
\f[V]java.*\f[R], \f[V]javax.*\f[R], and \f[V]org.*\f[R] packages that
are exported by the Java SE modules in the release; the JDK API consists
of the \f[V]com.*\f[R] and \f[V]jdk.*\f[R] packages that are exported by
the JDK modules in the release, plus the \f[V]javax.*\f[R] packages that
are exported by standard, but non-Java SE, modules in the release.
.PP
For previous releases, the Java SE API and the JDK API are as defined in
that release.
.PP
\f[B]Note:\f[R] When using \f[V]--release\f[R], you cannot also use the
\f[B]\f[VB]--source\f[B]\f[R]/\f[V]-source\f[R] or
\f[B]\f[VB]--target\f[B]\f[R]/\f[V]-target\f[R] options.
.PP
\f[B]Note:\f[R] When using \f[V]--release\f[R] to specify a release that
supports the Java Platform Module System, the \f[V]--add-exports\f[R]
option cannot be used to enlarge the set of packages exported by the
Java SE, JDK, and standard modules in the specified release.
.RE
.TP
\f[V]-s\f[R] \f[I]directory\f[R]
Specifies the directory used to place the generated source files.
If a class is part of a package, then the compiler puts the source file
in a subdirectory that reflects the module name (if appropriate) and
package name.
The directory, and any necessary subdirectories, will be created if they
do not already exist.
.RS
.PP
Except when compiling code for multiple modules, the contents of the
source output directory will be organized in a package hierarchy.
When compiling code for multiple modules, the contents of the source
output directory will be organized in a module hierarchy, with the
contents of each module in a separate subdirectory, each organized as a
package hierarchy.
.RE
.TP
\f[V]--source\f[R] \f[I]release\f[R] or \f[V]-source\f[R] \f[I]release\f[R]
Compiles source code according to the rules of the Java programming
language for the specified Java SE release.
The supported values of \f[I]release\f[R] are the current Java SE
release and a limited number of previous releases, detailed in the
command-line help.
.RS
.PP
If the option is not specified, the default is to compile source code
according to the rules of the Java programming language for the current
Java SE release.
.RE
.TP
\f[V]--source-path\f[R] \f[I]path\f[R] or \f[V]-sourcepath\f[R] \f[I]path\f[R]
Specifies where to find source files.
Except when compiling multiple modules together, this is the source code
path used to search for class or interface definitions.
.RS
.PP
\f[B]Note:\f[R] Classes found through the class path might be recompiled
when their source files are also found.
See \f[B]Searching for Module, Package and Type Declarations\f[R].
.RE
.TP
\f[V]--system\f[R] \f[I]jdk\f[R] | \f[V]none\f[R]
Overrides the location of system modules.
.TP
\f[V]--target\f[R] \f[I]release\f[R] or \f[V]-target\f[R] \f[I]release\f[R]
Generates \f[V]class\f[R] files suitable for the specified Java SE
release.
The supported values of \f[I]release\f[R] are the current Java SE
release and a limited number of previous releases, detailed in the
command-line help.
.RS
.PP
\f[B]Note:\f[R] The target release must be equal to or higher than the
source release.
(See \f[B]\f[VB]--source\f[B]\f[R].)
.RE
.TP
\f[V]--upgrade-module-path\f[R] \f[I]path\f[R]
Overrides the location of upgradeable modules.
.TP
\f[V]-verbose\f[R]
Outputs messages about what the compiler is doing.
Messages include information about each class loaded and each source
file compiled.
.TP
\f[V]--version\f[R] or \f[V]-version\f[R]
Prints version information.
.TP
\f[V]-Werror\f[R]
Terminates compilation when warnings occur.
.SS Extra Options
.TP
\f[V]--add-exports\f[R] \f[I]module\f[R]\f[V]/\f[R]\f[I]package\f[R]\f[V]=\f[R]\f[I]other-module\f[R](\f[V],\f[R]\f[I]other-module\f[R])*
Specifies a package to be considered as exported from its defining
module to additional modules or to all unnamed modules when the value of
\f[I]other-module\f[R] is \f[V]ALL-UNNAMED\f[R].
.TP
\f[V]--add-reads\f[R] \f[I]module\f[R]\f[V]=\f[R]\f[I]other-module\f[R](\f[V],\f[R]\f[I]other-module\f[R])*
Specifies additional modules to be considered as required by a given
module.
.TP
\f[V]--default-module-for-created-files\f[R] \f[I]module-name\f[R]
Specifies the fallback target module for files created by annotation
processors, if none is specified or inferred.
.TP
\f[V]--disable-line-doc-comments\f[R]
Disables support for documentation comments with lines beginning ///.
.TP
\f[V]-Djava.endorsed.dirs=\f[R]\f[I]dirs\f[R]
Overrides the location of the endorsed standards path.
.RS
.PP
\f[B]Note:\f[R] This can only be used when compiling for releases prior
to JDK 9.
As applicable, see the descriptions in \f[B]\f[VB]--release\f[B]\f[R],
\f[B]\f[VB]-source\f[B]\f[R], or \f[B]\f[VB]-target\f[B]\f[R] for
details.
.RE
.TP
\f[V]-Djava.ext.dirs=\f[R]\f[I]dirs\f[R]
Overrides the location of installed extensions.
.RS
.PP
\f[B]Note:\f[R] This can only be used when compiling for releases prior
to JDK 9.
As applicable, see the descriptions in \f[B]\f[VB]--release\f[B]\f[R],
\f[B]\f[VB]-source\f[B]\f[R], or \f[B]\f[VB]-target\f[B]\f[R] for
details.
.RE
.TP
\f[V]--patch-module\f[R] \f[I]module\f[R]\f[V]=\f[R]\f[I]path\f[R]
Overrides or augments a module with classes and resources in JAR files
or directories.
.TP
\f[V]-Xbootclasspath:\f[R]\f[I]path\f[R]
Overrides the location of the bootstrap class files.
.RS
.PP
\f[B]Note:\f[R] This can only be used when compiling for releases prior
to JDK 9.
As applicable, see the descriptions in \f[B]\f[VB]--release\f[B]\f[R],
\f[B]\f[VB]-source\f[B]\f[R], or \f[B]\f[VB]-target\f[B]\f[R] for
details.
.RE
.TP
\f[V]-Xbootclasspath/a:\f[R]\f[I]path\f[R]
Adds a suffix to the bootstrap class path.
.RS
.PP
\f[B]Note:\f[R] This can only be used when compiling for releases prior
to JDK 9.
As applicable, see the descriptions in \f[B]\f[VB]--release\f[B]\f[R],
\f[B]\f[VB]-source\f[B]\f[R], or \f[B]\f[VB]-target\f[B]\f[R] for
details.
.RE
.TP
\f[V]-Xbootclasspath/p:\f[R]\f[I]path\f[R]
Adds a prefix to the bootstrap class path.
.RS
.PP
\f[B]Note:\f[R] This can only be used when compiling for releases prior
to JDK 9.
As applicable, see the descriptions in \f[B]\f[VB]--release\f[B]\f[R],
\f[B]\f[VB]-source\f[B]\f[R], or \f[B]\f[VB]-target\f[B]\f[R] for
details.
.RE
.TP
\f[V]-Xdiags:\f[R][\f[V]compact\f[R], \f[V]verbose\f[R]]
Selects a diagnostic mode.
.TP
\f[V]-Xdoclint\f[R]
Enables recommended checks for problems in documentation comments.
.TP
\f[V]-Xdoclint:\f[R](\f[V]all\f[R]|\f[V]none\f[R]|[\f[V]-\f[R]]\f[I]group\f[R])[\f[V]/\f[R]\f[I]access\f[R]]
Enables or disables specific groups of checks in documentation comments.
.RS
.PP
\f[I]group\f[R] can have one of the following values:
\f[V]accessibility\f[R], \f[V]html\f[R], \f[V]missing\f[R],
\f[V]reference\f[R], \f[V]syntax\f[R].
.PP
The variable \f[I]access\f[R] specifies the minimum visibility level of
classes and members that the \f[V]-Xdoclint\f[R] option checks.
It can have one of the following values (in order of most to least
visible): \f[V]public\f[R], \f[V]protected\f[R], \f[V]package\f[R],
\f[V]private\f[R].
.PP
The default \f[I]access\f[R] level is \f[V]private\f[R].
.PP
When prefixed by \f[V]doclint:\f[R], the \f[I]group\f[R] names and
\f[V]all\f[R] can be used with \f[V]\[at]SuppressWarnings\f[R] to
suppress warnings about documentation comments in parts of the code
being compiled.
.PP
For more information about these groups of checks, see the
\f[B]DocLint\f[R] section of the \f[V]javadoc\f[R] command
documentation.
The \f[V]-Xdoclint\f[R] option is disabled by default in the
\f[V]javac\f[R] command.
.PP
For example, the following option checks classes and members (with all
groups of checks) that have the access level of protected and higher
(which includes protected and public):
.RS
.PP
\f[V]-Xdoclint:all/protected\f[R]
.RE
.PP
The following option enables all groups of checks for all access levels,
except it will not check for HTML errors for classes and members that
have the access level of package and higher (which includes package,
protected and public):
.RS
.PP
\f[V]-Xdoclint:all,-html/package\f[R]
.RE
.RE
.TP
\f[V]-Xdoclint/package:\f[R][\f[V]-\f[R]]\f[I]packages\f[R](\f[V],\f[R][\f[V]-\f[R]]\f[I]package\f[R])*
Enables or disables checks in specific packages.
Each \f[I]package\f[R] is either the qualified name of a package or a
package name prefix followed by \f[V].*\f[R], which expands to all
sub-packages of the given package.
Each \f[I]package\f[R] can be prefixed with a hyphen (\f[V]-\f[R]) to
disable checks for a specified package or packages.
.RS
.PP
For more information, see the \f[B]DocLint\f[R] section of the
\f[V]javadoc\f[R] command documentation.
.RE
.TP
\f[V]-Xlint\f[R]
Enables all recommended warnings.
In this release, enabling all available warnings is recommended.
.TP
\f[V]-Xlint:\f[R][\f[V]-\f[R]]\f[I]key\f[R](\f[V],\f[R][\f[V]-\f[R]]\f[I]key\f[R])*
Supplies warnings to enable or disable, separated by comma.
Precede a key by a hyphen (\f[V]-\f[R]) to disable the specified
warning.
.RS
.PP
Supported values for \f[I]key\f[R] are:
.IP \[bu] 2
\f[V]all\f[R]: Enables all warnings.
.IP \[bu] 2
\f[V]auxiliaryclass\f[R]: Warns about an auxiliary class that is hidden
in a source file, and is used from other files.
.IP \[bu] 2
\f[V]cast\f[R]: Warns about the use of unnecessary casts.
.IP \[bu] 2
\f[V]classfile\f[R]: Warns about the issues related to classfile
contents.
.IP \[bu] 2
\f[V]dangling-doc-comments\f[R]: Warns about extra or misplaced
documentation comments near the beginning of a declaration.
.IP \[bu] 2
\f[V]deprecation\f[R]: Warns about the use of deprecated items.
.IP \[bu] 2
\f[V]dep-ann\f[R]: Warns about the items marked as deprecated in
\f[V]javadoc\f[R] but without the \f[V]\[at]Deprecated\f[R] annotation.
.IP \[bu] 2
\f[V]divzero\f[R]: Warns about the division by the constant integer 0.
.IP \[bu] 2
\f[V]empty\f[R]: Warns about an empty statement after \f[V]if\f[R].
.IP \[bu] 2
\f[V]exports\f[R]: Warns about the issues regarding module exports.
.IP \[bu] 2
\f[V]fallthrough\f[R]: Warns about the falling through from one case of
a switch statement to the next.
.IP \[bu] 2
\f[V]finally\f[R]: Warns about \f[V]finally\f[R] clauses that do not
terminate normally.
.IP \[bu] 2
\f[V]incubating\f[R]: Warns about the use of incubating modules.
.IP \[bu] 2
\f[V]lossy-conversions\f[R]: Warns about possible lossy conversions in
compound assignment.
.IP \[bu] 2
\f[V]missing-explicit-ctor\f[R]: Warns about missing explicit
constructors in public and protected classes in exported packages.
.IP \[bu] 2
\f[V]module\f[R]: Warns about the module system-related issues.
.IP \[bu] 2
\f[V]opens\f[R]: Warns about the issues related to module opens.
.IP \[bu] 2
\f[V]options\f[R]: Warns about the issues relating to use of command
line options.
.IP \[bu] 2
\f[V]output-file-clash\f[R]: Warns if any output file is overwritten
during compilation.
This can occur, for example, on case-insensitive filesystems.
.IP \[bu] 2
\f[V]overloads\f[R]: Warns about the issues related to method overloads.
.IP \[bu] 2
\f[V]overrides\f[R]: Warns about the issues related to method overrides.
.IP \[bu] 2
\f[V]path\f[R]: Warns about the invalid path elements on the command
line.
.IP \[bu] 2
\f[V]preview\f[R]: Warns about the use of preview language features.
.IP \[bu] 2
\f[V]processing\f[R]: Warns about the issues related to annotation
processing.
.IP \[bu] 2
\f[V]rawtypes\f[R]: Warns about the use of raw types.
.IP \[bu] 2
\f[V]removal\f[R]: Warns about the use of an API that has been marked
for removal.
.IP \[bu] 2
\f[V]restricted\f[R]: Warns about the use of restricted methods.
.IP \[bu] 2
\f[V]requires-automatic\f[R]: Warns developers about the use of
automatic modules in requires clauses.
.IP \[bu] 2
\f[V]requires-transitive-automatic\f[R]: Warns about automatic modules
in requires transitive.
.IP \[bu] 2
\f[V]serial\f[R]: Warns about the serializable classes that do not
provide a serial version ID.
Also warns about access to non-public members from a serializable
element.
.IP \[bu] 2
\f[V]static\f[R]: Warns about the accessing a static member using an
instance.
.IP \[bu] 2
\f[V]strictfp\f[R]: Warns about unnecessary use of the
\f[V]strictfp\f[R] modifier.
.IP \[bu] 2
\f[V]synchronization\f[R]: Warns about synchronization attempts on
instances of value-based classes.
.IP \[bu] 2
\f[V]text-blocks\f[R]: Warns about inconsistent white space characters
in text block indentation.
.IP \[bu] 2
\f[V]this-escape\f[R]: Warns about constructors leaking \f[V]this\f[R]
prior to subclass initialization.
.IP \[bu] 2
\f[V]try\f[R]: Warns about the issues relating to the use of try blocks
(that is, try-with-resources).
.IP \[bu] 2
\f[V]unchecked\f[R]: Warns about the unchecked operations.
.IP \[bu] 2
\f[V]varargs\f[R]: Warns about the potentially unsafe \f[V]vararg\f[R]
methods.
.IP \[bu] 2
\f[V]none\f[R]: Disables all warnings.
.PP
With the exception of \f[V]all\f[R] and \f[V]none\f[R], the keys can be
used with the \f[V]\[at]SuppressWarnings\f[R] annotation to suppress
warnings in a part of the source code being compiled.
.PP
See \f[B]Examples of Using -Xlint keys\f[R].
.RE
.TP
\f[V]-Xmaxerrs\f[R] \f[I]number\f[R]
Sets the maximum number of errors to print.
.TP
\f[V]-Xmaxwarns\f[R] \f[I]number\f[R]
Sets the maximum number of warnings to print.
.TP
\f[V]-Xpkginfo:\f[R][\f[V]always\f[R], \f[V]legacy\f[R], \f[V]nonempty\f[R]]
Specifies when and how the \f[V]javac\f[R] command generates
\f[V]package-info.class\f[R] files from \f[V]package-info.java\f[R]
files using one of the following options:
.RS
.TP
\f[V]always\f[R]
Generates a \f[V]package-info.class\f[R] file for every
\f[V]package-info.java\f[R] file.
This option may be useful if you use a build system such as Ant, which
checks that each \f[V].java\f[R] file has a corresponding
\f[V].class\f[R] file.
.TP
\f[V]legacy\f[R]
Generates a \f[V]package-info.class\f[R] file only if
\f[V]package-info.java\f[R] contains annotations.
This option does not generate a \f[V]package-info.class\f[R] file if
\f[V]package-info.java\f[R] contains only comments.
.RS
.PP
\f[B]Note:\f[R] A \f[V]package-info.class\f[R] file might be generated
but be empty if all the annotations in the \f[V]package-info.java\f[R]
file have \f[V]RetentionPolicy.SOURCE\f[R].
.RE
.TP
\f[V]nonempty\f[R]
Generates a \f[V]package-info.class\f[R] file only if
\f[V]package-info.java\f[R] contains annotations with
\f[V]RetentionPolicy.CLASS\f[R] or \f[V]RetentionPolicy.RUNTIME\f[R].
.RE
.TP
\f[V]-Xplugin:\f[R]\f[I]name\f[R] \f[I]args\f[R]
Specifies the name and optional arguments for a plug-in to be run.
If \f[I]args\f[R] are provided, \f[I]name\f[R] and \f[I]args\f[R] should
be quoted or otherwise escape the whitespace characters between the name
and all the arguments.
For details on the API for a plugin, see the API documentation for
\f[B]jdk.compiler/com.sun.source.util.Plugin\f[R].
.TP
\f[V]-Xprefer:\f[R][\f[V]source\f[R], \f[V]newer\f[R]]
Specifies which file to read when both a source file and class file are
found for an implicitly compiled class using one of the following
options.
See \f[B]Searching for Module, Package and Type Declarations\f[R].
.RS
.IP \[bu] 2
\f[V]-Xprefer:newer\f[R]: Reads the newer of the source or class files
for a type (default).
.IP \[bu] 2
\f[V]-Xprefer:source\f[R] : Reads the source file.
Use \f[V]-Xprefer:source\f[R] when you want to be sure that any
annotation processors can access annotations declared with a retention
policy of \f[V]SOURCE\f[R].
.RE
.TP
\f[V]-Xprint\f[R]
Prints a textual representation of specified types for debugging
purposes.
This does not perform annotation processing or compilation.
The format of the output could change.
.TP
\f[V]-XprintProcessorInfo\f[R]
Prints information about which annotations a processor is asked to
process.
.TP
\f[V]-XprintRounds\f[R]
Prints information about initial and subsequent annotation processing
rounds.
.TP
\f[V]-Xstdout\f[R] \f[I]filename\f[R]
Sends compiler messages to the named file.
By default, compiler messages go to \f[V]System.err\f[R].
.SH ENVIRONMENT VARIABLES
.SS CLASSPATH
.PP
If the \f[B]\f[VB]--class-path\f[B]\f[R] option or any of its alternate
forms are not specified, the class path will default to the value of the
\f[V]CLASSPATH\f[R] environment variable if it is set.
However, it is recommended that this environment variable should
\f[I]not\f[R] be set, and that the \f[V]--class-path\f[R] option should
be used to provide an explicit value for the class path when one is
required.
.SS JDK_JAVAC_OPTIONS
.PP
The content of the \f[V]JDK_JAVAC_OPTIONS\f[R] environment variable,
separated by white-spaces ( ) or white-space characters
(\f[V]\[rs]n\f[R], \f[V]\[rs]t\f[R], \f[V]\[rs]r\f[R], or
\f[V]\[rs]f\f[R]) is prepended to the command line arguments passed to
\f[V]javac\f[R] as a list of arguments.
.PP
The encoding requirement for the environment variable is the same as the
\f[V]javac\f[R] command line on the system.
\f[V]JDK_JAVAC_OPTIONS\f[R] environment variable content is treated in
the same manner as that specified in the command line.
.PP
Single quotes (\f[V]\[aq]\f[R]) or double quotes (\f[V]\[dq]\f[R]) can
be used to enclose arguments that contain whitespace characters.
All content between the open quote and the first matching close quote
are preserved by simply removing the pair of quotes.
In case a matching quote is not found, the launcher will abort with an
error message.
\f[V]\[at]\f[R]\f[I]files\f[R] are supported as they are specified in
the command line.
However, as in \f[V]\[at]\f[R]\f[I]files\f[R], use of a wildcard is not
supported.
.PP
\f[B]Examples of quoting arguments containing white spaces:\f[R]
.RS
.PP
\f[V]export JDK_JAVAC_OPTIONS=\[aq]\[at]\[dq]C:\[rs]white spaces\[rs]argfile\[dq]\[aq]\f[R]
.RE
.RS
.PP
\f[V]export JDK_JAVAC_OPTIONS=\[aq]\[dq]\[at]C:\[rs]white spaces\[rs]argfile\[dq]\[aq]\f[R]
.RE
.RS
.PP
\f[V]export JDK_JAVAC_OPTIONS=\[aq]\[at]C:\[rs]\[dq]white spaces\[dq]\[rs]argfile\[aq]\f[R]
.RE
.SH COMMAND-LINE ARGUMENT FILES
.PP
An argument file can include command-line options and source file names
in any combination.
The arguments within a file can be separated by spaces or new line
characters.
If a file name contains embedded spaces, then put the whole file name in
double quotation marks.
.PP
File names within an argument file are relative to the current
directory, not to the location of the argument file.
Wildcards (\f[V]*\f[R]) are not allowed in these lists (such as for
specifying \f[V]*.java\f[R]).
Use of the at sign (\f[V]\[at]\f[R]) to recursively interpret files is
not supported.
The \f[V]-J\f[R] options are not supported because they\[aq]re passed to
the launcher, which does not support argument files.
.PP
When executing the \f[V]javac\f[R] command, pass in the path and name of
each argument file with the at sign (\f[V]\[at]\f[R]) leading character.
When the \f[V]javac\f[R] command encounters an argument beginning with
the at sign (\f[V]\[at]\f[R]), it expands the contents of that file into
the argument list.
.SS Examples of Using javac \[at]filename
.TP
Single Argument File
You could use a single argument file named \f[V]argfile\f[R] to hold all
\f[V]javac\f[R] arguments:
.RS
.RS
.PP
\f[V]javac \[at]argfile\f[R]
.RE
.PP
This argument file could contain the contents of both files shown in the
following \f[B]Two Argument Files\f[R] example.
.RE
.TP
Two Argument Files
You can create two argument files: one for the \f[V]javac\f[R] options
and the other for the source file names.
Note that the following lists have no line-continuation characters.
.RS
.PP
Create a file named \f[V]options\f[R] that contains the following:
.PP
\f[B]Linux and macOS:\f[R]
.IP
.nf
\f[CB]
-d classes
-g
-sourcepath /java/pubs/ws/1.3/src/share/classes
\f[R]
.fi
.PP
\f[B]Windows:\f[R]
.IP
.nf
\f[CB]
-d classes
-g
-sourcepath C:\[rs]java\[rs]pubs\[rs]ws\[rs]1.3\[rs]src\[rs]share\[rs]classes
\f[R]
.fi
.PP
Create a file named \f[V]sources\f[R] that contains the following:
.IP
.nf
\f[CB]
MyClass1.java
MyClass2.java
MyClass3.java
\f[R]
.fi
.PP
Then, run the \f[V]javac\f[R] command as follows:
.RS
.PP
\f[V]javac \[at]options \[at]sources\f[R]
.RE
.RE
.TP
Argument Files with Paths
The argument files can have paths, but any file names inside the files
are relative to the current working directory (not \f[V]path1\f[R] or
\f[V]path2\f[R]):
.RS
.RS
.PP
\f[V]javac \[at]path1/options \[at]path2/sources\f[R]
.RE
.RE
.SH ARRANGEMENT OF SOURCE CODE
.PP
In the Java language, classes and interfaces can be organized into
packages, and packages can be organized into modules.
\f[V]javac\f[R] expects that the physical arrangement of source files in
directories of the file system will mirror the organization of classes
into packages, and packages into modules.
.PP
It is a widely adopted convention that module names and package names
begin with a lower-case letter, and that class names begin with an
upper-case letter.
.SS Arrangement of Source Code for a Package
.PP
When classes and interfaces are organized into a package, the package is
represented as a directory, and any subpackages are represented as
subdirectories.
.PP
For example:
.IP \[bu] 2
The package \f[V]p\f[R] is represented as a directory called
\f[V]p\f[R].
.IP \[bu] 2
The package \f[V]p.q\f[R] -- that is, the subpackage \f[V]q\f[R] of
package \f[V]p\f[R] -- is represented as the subdirectory \f[V]q\f[R] of
directory \f[V]p\f[R].
The directory tree representing package \f[V]p.q\f[R] is therefore
\f[V]p\[rs]q\f[R] on Windows, and \f[V]p/q\f[R] on other systems.
.IP \[bu] 2
The package \f[V]p.q.r\f[R] is represented as the directory tree
\f[V]p\[rs]q\[rs]r\f[R] (on Windows) or \f[V]p/q/r\f[R] (on other
systems).
.PP
Within a directory or subdirectory, \f[V].java\f[R] files represent
classes and interfaces in the corresponding package or subpackage.
.PP
For example:
.IP \[bu] 2
The class \f[V]X\f[R] declared in package \f[V]p\f[R] is represented by
the file \f[V]X.java\f[R] in the \f[V]p\f[R] directory.
.IP \[bu] 2
The class \f[V]Y\f[R] declared in package \f[V]p.q\f[R] is represented
by the file \f[V]Y.java\f[R] in the \f[V]q\f[R] subdirectory of
directory \f[V]p\f[R].
.IP \[bu] 2
The class \f[V]Z\f[R] declared in package \f[V]p.q.r\f[R] is represented
by the file \f[V]Z.java\f[R] in the \f[V]r\f[R] subdirectory of
\f[V]p\[rs]q\f[R] (on Windows) or \f[V]p/q\f[R] (on other systems).
.PP
In some situations, it is convenient to split the code into separate
directories, each structured as described above, and the aggregate list
of directories specified to \f[V]javac\f[R].
.SS Arrangement of Source Code for a Module
.PP
In the Java language, a module is a set of packages designed for reuse.
In addition to \f[V].java\f[R] files for classes and interfaces, each
module has a source file called \f[V]module-info.java\f[R] which:
.IP "1." 3
declares the module\[aq]s name;
.IP "2." 3
lists the packages exported by the module (to allow reuse by other
modules);
.IP "3." 3
lists other modules required by the module (to reuse their exported
packages).
.PP
When packages are organized into a module, the module is represented by
one or more directories representing the packages in the module, one of
which contains the \f[V]module-info.java\f[R] file.
It may be convenient, but it is not required, to use a single directory,
named after the module, to contain the \f[V]module-info.java\f[R] file
alongside the directory tree which represents the packages in the module
(i.e., the \f[I]package hierarchy\f[R] described above).
The exact arrangement of source code for a module is typically dictated
by the conventions adopted by a development environment (IDE) or build
system.
.PP
For example:
.IP \[bu] 2
The module \f[V]a.b.c\f[R] may be represented by the directory
\f[V]a.b.c\f[R], on all systems.
.IP \[bu] 2
The module\[aq]s declaration is represented by the file
\f[V]module-info.java\f[R] in the \f[V]a.b.c\f[R] directory.
.IP \[bu] 2
If the module contains package \f[V]p.q.r\f[R], then the \f[V]a.b.c\f[R]
directory contains the directory tree \f[V]p\[rs]q\[rs]r\f[R] (on
Windows) or \f[V]p/q/r\f[R] (on other systems).
.PP
The development environment may prescribe some directory hierarchy
between the directory named for the module and the source files to be
read by \f[V]javac\f[R].
.PP
For example:
.IP \[bu] 2
The module \f[V]a.b.c\f[R] may be represented by the directory
\f[V]a.b.c\f[R]
.IP \[bu] 2
The module\[aq]s declaration and the module\[aq]s packages may be in
some subdirectory of \f[V]a.b.c\f[R], such as
\f[V]src\[rs]main\[rs]java\f[R] (on Windows) or \f[V]src/main/java\f[R]
(on other systems).
.SH CONFIGURING A COMPILATION
.PP
This section describes how to configure \f[V]javac\f[R] to perform a
basic compilation.
.PP
See \f[B]Configuring the Module System\f[R] for additional details for
use when compiling for a release of the platform that supports modules.
.SS Source Files
.IP \[bu] 2
Specify the source files to be compiled on the command line.
.PP
If there are no compilation errors, the corresponding class files will
be placed in the \f[B]output directory\f[R].
.PP
Some systems may limit the amount you can put on a command line; to work
around those limits, you can use \f[B]argument files\f[R].
.PP
When compiling code for modules, you can also specify source files
indirectly, by using the \f[B]\f[VB]--module\f[B]\f[R] or \f[V]-m\f[R]
option.
.SS Output Directory
.IP \[bu] 2
Use the \f[B]\f[VB]-d\f[B]\f[R] option to specify an output directory in
which to put the compiled class files.
.PP
This will normally be organized in a \f[B]package hierarchy\f[R], unless
you are compiling source code from multiple modules, in which case it
will be organized as a \f[B]module hierarchy\f[R].
.PP
When the compilation has been completed, if you are compiling one or
more modules, you can place the output directory on the module path for
the Java \f[B]launcher\f[R]; otherwise, you can place the place the
output directory on the class path for the Java launcher.
.SS Precompiled Code
.PP
The code to be compiled may refer to libraries beyond what is provided
by the platform.
If so, you must place these libraries on the class path or module path.
If the library code is not in a module, place it on the class path; if
it is in a module, place it on the module path.
.IP \[bu] 2
Use the \f[B]\f[VB]--class-path\f[B]\f[R] option to specify libraries to
be placed on the class path.
Locations on the class path should be organized in a \f[B]package
hierarchy\f[R].
You can also use alternate forms of the option: \f[V]-classpath\f[R] or
\f[V]-cp\f[R].
.IP \[bu] 2
Use the \f[B]\f[VB]--module-path\f[B]\f[R] option to specify libraries
to be placed on the module path.
Locations on the module path should either be modules or directories of
modules.
You can also use an alternate form of the option: \f[V]-p\f[R].
.RS 2
.PP
See \f[B]Configuring the Module System\f[R] for details on how to modify
the default configuration of library modules.
.RE
.PP
\f[B]Note\f[R]: the options for the class path and module path are not
mutually exclusive, although it is not common to specify the class path
when compiling code for one or more modules.
.SS Additional Source Files
.PP
The code to be compiled may refer to types in additional source files
that are not specified on the command line.
If so, you must put those source files on either the source path or
module path.
You can only specify one of these options: if you are not compiling code
for a module, or if you are only compiling code for a single module, use
the source path; if you are compiling code for multiple modules, use the
module source path.
.IP \[bu] 2
Use the \f[B]\f[VB]--source-path\f[B]\f[R] option to specify the
locations of additional source files that may be read by javac.
Locations on the source path should be organized in a \f[B]package
hierarchy\f[R].
You can also use an alternate form of the option: \f[V]-sourcepath\f[R].
.IP \[bu] 2
Use the \f[B]\f[VB]--module-source-path\f[B]\f[R] option one or more
times to specify the location of additional source files in different
modules that may be read by javac, or when compiling source files in
multiple modules.
You can either specify the locations for each module
\f[B]individually\f[R], or you can organize the source files so that you
can specify the locations all \f[B]together\f[R].
For more details, see \f[B]The Module Source Path Option\f[R].
.PP
If you want to be able to refer to types in additional source files but
do not want them to be compiled, use the \f[B]\f[VB]-implicit\f[B]\f[R]
option.
.PP
\f[B]Note\f[R]: if you are compiling code for multiple modules, you must
always specify a module source path, and all source files specified on
the command line must be in one of the directories on the module source
path, or in a subdirectory thereof.
.SS Example of Compiling Multiple Source Files
.PP
This example compiles the \f[V]Aloha.java\f[R], \f[V]GutenTag.java\f[R],
\f[V]Hello.java\f[R], and \f[V]Hi.java\f[R] source files in the
\f[V]greetings\f[R] package.
.PP
\f[B]Linux and macOS:\f[R]
.IP
.nf
\f[CB]
% javac greetings/*.java
% ls greetings
Aloha.class         GutenTag.class      Hello.class         Hi.class
Aloha.java          GutenTag.java       Hello.java          Hi.java
\f[R]
.fi
.PP
\f[B]Windows:\f[R]
.IP
.nf
\f[CB]
C:\[rs]>javac greetings\[rs]*.java
C:\[rs]>dir greetings
Aloha.class         GutenTag.class      Hello.class         Hi.class
Aloha.java          GutenTag.java       Hello.java          Hi.java
\f[R]
.fi
.SS Example of Specifying a User Class Path
.PP
After changing one of the source files in the previous example,
recompile it:
.PP
\f[B]Linux and macOS:\f[R]
.IP
.nf
\f[CB]
pwd
/examples
javac greetings/Hi.java
\f[R]
.fi
.PP
\f[B]Windows:\f[R]
.IP
.nf
\f[CB]
C:\[rs]>cd
\[rs]examples
C:\[rs]>javac greetings\[rs]Hi.java
\f[R]
.fi
.PP
Because \f[V]greetings.Hi\f[R] refers to other classes in the
\f[V]greetings\f[R] package, the compiler needs to find these other
classes.
The previous example works because the default user class path is the
directory that contains the package directory.
If you want to recompile this file without concern for which directory
you are in, then add the examples directory to the user class path by
setting \f[V]CLASSPATH\f[R].
This example uses the \f[V]-classpath\f[R] option.
.PP
\f[B]Linux and macOS:\f[R]
.RS
.PP
\f[V]javac -classpath /examples /examples/greetings/Hi.java\f[R]
.RE
.PP
\f[B]Windows:\f[R]
.RS
.PP
\f[V]C:\[rs]>javac -classpath \[rs]examples \[rs]examples\[rs]greetings\[rs]Hi.java\f[R]
.RE
.PP
If you change \f[V]greetings.Hi\f[R] to use a banner utility, then that
utility also needs to be accessible through the user class path.
.PP
\f[B]Linux and macOS:\f[R]
.IP
.nf
\f[CB]
javac -classpath /examples:/lib/Banners.jar \[rs]
            /examples/greetings/Hi.java
\f[R]
.fi
.PP
\f[B]Windows:\f[R]
.IP
.nf
\f[CB]
C:\[rs]>javac -classpath \[rs]examples;\[rs]lib\[rs]Banners.jar \[ha]
            \[rs]examples\[rs]greetings\[rs]Hi.java
\f[R]
.fi
.PP
To execute a class in the \f[V]greetings\f[R] package, the program needs
access to the \f[V]greetings\f[R] package, and to the classes that the
\f[V]greetings\f[R] classes use.
.PP
\f[B]Linux and macOS:\f[R]
.RS
.PP
\f[V]java -classpath /examples:/lib/Banners.jar greetings.Hi\f[R]
.RE
.PP
\f[B]Windows:\f[R]
.RS
.PP
\f[V]C:\[rs]>java -classpath \[rs]examples;\[rs]lib\[rs]Banners.jar greetings.Hi\f[R]
.RE
.SH CONFIGURING THE MODULE SYSTEM
.PP
If you want to include additional modules in your compilation, use the
\f[B]\f[VB]--add-modules\f[B]\f[R] option.
This may be necessary when you are compiling code that is not in a
module, or which is in an automatic module, and the code refers to API
in the additional modules.
.PP
If you want to restrict the set of modules in your compilation, use the
\f[B]\f[VB]--limit-modules\f[B]\f[R] option.
This may be useful if you want to ensure that the code you are compiling
is capable of running on a system with a limited set of modules
installed.
.PP
If you want to break encapsulation and specify that additional packages
should be considered as exported from a module, use the
\f[B]\f[VB]--add-exports\f[B]\f[R] option.
This may be useful when performing white-box testing; relying on access
to internal API in production code is strongly discouraged.
.PP
If you want to specify that additional packages should be considered as
required by a module, use the \f[B]\f[VB]--add-reads\f[B]\f[R] option.
This may be useful when performing white-box testing; relying on access
to internal API in production code is strongly discouraged.
.PP
You can patch additional content into any module using the
\f[B]\f[VB]--patch-module\f[B]\f[R] option.
See [Patching a Module] for more details.
.SH SEARCHING FOR MODULE, PACKAGE AND TYPE DECLARATIONS
.PP
To compile a source file, the compiler often needs information about a
module or type, but the declaration is not in the source files specified
on the command line.
.PP
\f[V]javac\f[R] needs type information for every class or interface
used, extended, or implemented in the source file.
This includes classes and interfaces not explicitly mentioned in the
source file, but that provide information through inheritance.
.PP
For example, when you create a subclass of \f[V]java.awt.Window\f[R],
you are also using the ancestor classes of \f[V]Window\f[R]:
\f[V]java.awt.Container\f[R], \f[V]java.awt.Component\f[R], and
\f[V]java.lang.Object\f[R].
.PP
When compiling code for a module, the compiler also needs to have
available the declaration of that module.
.PP
A successful search may produce a class file, a source file, or both.
If both are found, then you can use the \f[B]\f[VB]-Xprefer\f[B]\f[R]
option to instruct the compiler which to use.
.PP
If a search finds and uses a source file, then by default
\f[V]javac\f[R] compiles that source file.
This behavior can be altered with \f[B]\f[VB]-implicit\f[B]\f[R].
.PP
The compiler might not discover the need for some type information until
after annotation processing completes.
When the type information is found in a source file and no
\f[B]\f[VB]-implicit\f[B]\f[R] option is specified, the compiler gives a
warning that the file is being compiled without being subject to
annotation processing.
To disable the warning, either specify the file on the command line (so
that it will be subject to annotation processing) or use the
\f[B]\f[VB]-implicit\f[B]\f[R] option to specify whether or not class
files should be generated for such source files.
.PP
The way that \f[V]javac\f[R] locates the declarations of those types
depends on whether the reference exists within code for a module or not.
.SS Searching Package Oriented Paths
.PP
When searching for a source or class file on a path composed of package
oriented locations, \f[V]javac\f[R] will check each location on the path
in turn for the possible presence of the file.
The first occurrence of a particular file shadows (hides) any subsequent
occurrences of like-named files.
This shadowing does not affect any search for any files with a different
name.
This can be convenient when searching for source files, which may be
grouped in different locations, such as shared code, platform-specific
code and generated code.
It can also be useful when injecting alternate versions of a class file
into a package, to debugging or other instrumentation reasons.
But, it can also be dangerous, such as when putting incompatible
different versions of a library on the class path.
.SS Searching Module Oriented Paths
.PP
Prior to scanning any module paths for any package or type declarations,
\f[V]javac\f[R] will lazily scan the following paths and locations to
determine the modules that will be used in the compilation.
.IP \[bu] 2
The module source path (see the
\f[B]\f[VB]--module-source-path\f[B]\f[R] option)
.IP \[bu] 2
The path for upgradeable modules (see the
\f[B]\f[VB]--upgrade-module-path\f[B]\f[R] option)
.IP \[bu] 2
The system modules (see the \f[B]\f[VB]--system\f[B]\f[R] option)
.IP \[bu] 2
The user module path ( see the \f[B]\f[VB]--module-path\f[B]\f[R]
option)
.PP
For any module, the first occurrence of the module during the scan
completely shadows (hides) any subsequent appearance of a like-named
module.
While locating the modules, \f[V]javac\f[R] is able to determine the
packages exported by the module and to associate with each module a
package oriented path for the contents of the module.
For any previously compiled module, this path will typically be a single
entry for either a directory or a file that provides an internal
directory-like hierarchy, such as a JAR file.
Thus, when searching for a type that is in a package that is known to be
exported by a module, \f[V]javac\f[R] can locate the declaration
directly and efficiently.
.SS Searching for the Declaration of a Module
.PP
If the module has been previously compiled, the module declaration is
located in a file named \f[V]module-info.class\f[R] in the root of the
package hierarchy for the content of the module.
.PP
If the module is one of those currently being compiled, the module
declaration will be either the file named \f[V]module-info.class\f[R] in
the root of the package hierarchy for the module in the class output
directory, or the file named \f[V]module-info.java\f[R] in one of the
locations on the source path or one the module source path for the
module.
.SS Searching for the Declaration of a Type When the Reference is not in a Module
.PP
When searching for a type that is referenced in code that is not in a
module, \f[V]javac\f[R] will look in the following places:
.IP \[bu] 2
The platform classes (or the types in exported packages of the platform
modules) (This is for compiled class files only.)
.IP \[bu] 2
Types in exported packages of any modules on the module path, if
applicable.
(This is for compiled class files only.)
.IP \[bu] 2
Types in packages on the class path and/or source path:
.RS 2
.IP \[bu] 2
If both are specified, \f[V]javac\f[R] looks for compiled class files on
the class path and for source files on the source path.
.IP \[bu] 2
If the class path is specified, but not source path, \f[V]javac\f[R]
looks for both compiled class files and source files on the class path.
.IP \[bu] 2
If the class path is not specified, it defaults to the current
directory.
.RE
.PP
When looking for a type on the class path and/or source path, if both a
compiled class file and a source file are found, the most recently
modified file will be used by default.
If the source file is newer, it will be compiled and will may override
any previously compiled version of the file.
You can use the \f[B]\f[VB]-Xprefer\f[B]\f[R] option to override the
default behavior.
.SS Searching for the Declaration of a Type When the Reference is in a Module
.PP
When searching for a type that is referenced in code in a module,
\f[V]javac\f[R] will examine the declaration of the enclosing module to
determine if the type is in a package that is exported from another
module that is readable by the enclosing module.
If so, \f[V]javac\f[R] will simply and directly go to the definition of
that module to find the definition of the required type.
Unless the module is another of the modules being compiled,
\f[V]javac\f[R] will only look for compiled class files files.
In other words, \f[V]javac\f[R] will not look for source files in
platform modules or modules on the module path.
.PP
If the type being referenced is not in some other readable module,
\f[V]javac\f[R] will examine the module being compiled to try and find
the declaration of the type.
\f[V]javac\f[R] will look for the declaration of the type as follows:
.IP \[bu] 2
Source files specified on the command line or on the source path or
module source path.
.IP \[bu] 2
Previously compiled files in the output directory.
.SH DIRECTORY HIERARCHIES
.PP
\f[V]javac\f[R] generally assumes that source files and compiled class
files will be organized in a file system directory hierarchy or in a
type of file that supports in an internal directory hierarchy, such as a
JAR file.
Three different kinds of hierarchy are supported: a \f[I]package
hierarchy\f[R], a \f[I]module hierarchy\f[R], and a \f[I]module source
hierarchy\f[R].
.PP
While \f[V]javac\f[R] is fairly relaxed about the organization of source
code, beyond the expectation that source will be organized in one or
package hierarchies, and can generally accommodate organizations
prescribed by development environments and build tools, Java tools in
general, and \f[V]javac\f[R] and the Java launcher in particular, are
more stringent regarding the organization of compiled class files, and
will be organized in package hierarchies or module hierarchies, as
appropriate.
.PP
The location of these hierarchies are specified to \f[V]javac\f[R] with
command-line options, whose names typically end in \[dq]path\[dq], like
\f[B]\f[VB]--source-path\f[B]\f[R] or \f[B]\f[VB]--class-path\f[B]\f[R].
Also as a general rule, path options whose name includes the word
\f[V]module\f[R], like \f[B]\f[VB]--module-path\f[B]\f[R], are used to
specify module hierarchies, although some module-related path options
allow a package hierarchy to be specified on a per-module basis.
All other path options are used to specify package hierarchies.
.SS Package Hierarchy
.PP
In a package hierarchy, directories and subdirectories are used to
represent the component parts of the package name, with the source file
or compiled class file for a type being stored as a file with an
extension of \f[V].java\f[R] or \f[V].class\f[R] in the most nested
directory.
.PP
For example, in a package hierarchy, the source file for a class
\f[V]com.example.MyClass\f[R] will be stored in the file
\f[I]com/example/MyClass.java\f[R]
.SS Module Hierarchy
.PP
In a module hierarchy, the first level of directories are named for the
modules in the hierarchy; within each of those directories the contents
of the module are organized in package hierarchies.
.PP
For example, in a module hierarchy, the compiled class file for a type
called \f[V]com.example.MyClass\f[R] in a module called
\f[V]my.library\f[R] will be stored in
\f[I]my.library/com/example/MyClass.class\f[R].
.PP
The various output directories used by \f[V]javac\f[R] (the class output
directory, the source output directory, and native header output
directory) will all be organized in a module hierarchy when multiple
modules are being compiled.
.SS Module Source Hierarchy
.PP
Although the source for each individual module should always be
organized in a package hierarchy, it may be convenient to group those
hierarchies into a module source hierarchy.
This is similar to a module hierarchy, except that there may be
intervening directories between the directory for the module and the
directory that is the root of the package hierarchy for the source code
of the module.
.PP
For example, in a module source hierarchy, the source file for a type
called \f[V]com.example.MyClass\f[R] in a module called
\f[V]my.library\f[R] may be stored in a file such as
\f[I]my.library/src/main/java/com/example/MyClass.java\f[R].
.SH THE MODULE SOURCE PATH OPTION
.PP
The \f[B]\f[VB]--module-source-path\f[B]\f[R] option has two forms: a
\f[I]module-specific form\f[R], in which a package path is given for
each module containing code to be compiled, and a
\f[I]module-pattern\f[R] form, in which the source path for each module
is specified by a pattern.
The module-specific form is generally simpler to use when only a small
number of modules are involved; the module-pattern form may be more
convenient when the number of modules is large and the modules are
organized in a regular manner that can be described by a pattern.
.PP
Multiple instances of the \f[V]--module-source-path\f[R] option may be
given, each one using either the module-pattern form or the
module-specific form, subject to the following limitations:
.IP \[bu] 2
the module-pattern form may be used at most once
.IP \[bu] 2
the module-specific form may be used at most once for any given module
.PP
If the module-specific form is used for any module, the associated
search path overrides any path that might otherwise have been inferred
from the module-pattern form.
.SS Module-specific form
.PP
The module-specific form allows an explicit search path to be given for
any specific module.
This form is:
.IP \[bu] 2
\f[V]--module-source-path\f[R]
\f[I]module-name\f[R]\f[V]=\f[R]\f[I]file-path\f[R]
(\f[I]path-separator\f[R] \f[I]file-path\f[R])*
.PP
The path separator character is \f[V];\f[R] on Windows, and \f[V]:\f[R]
otherwise.
.PP
\f[B]Note:\f[R] this is similar to the form used for the
\f[B]\f[VB]--patch-module\f[B]\f[R] option.
.SS Module-pattern form
.PP
The module-pattern form allows a concise specification of the module
source path for any number of modules organized in regular manner.
.IP \[bu] 2
\f[V]--module-source-path\f[R] \f[I]pattern\f[R]
.PP
The pattern is defined by the following rules, which are applied in
order:
.IP \[bu] 2
The argument is considered to be a series of segments separated by the
path separator character (\f[V];\f[R] on Windows, and \f[V]:\f[R]
otherwise).
.IP \[bu] 2
Each segment containing curly braces of the form
.RS 2
.IP
.nf
\f[CB]
string1{alt1 ( ,alt2 )* } string2
\f[R]
.fi
.PP
is considered to be replaced by a series of segments formed by
\[dq]expanding\[dq] the braces:
.IP
.nf
\f[CB]
string1 alt1 string2
string1 alt2 string2
and so on...
\f[R]
.fi
.PP
The braces may be nested.
.PP
This rule is applied for all such usages of braces.
.RE
.IP \[bu] 2
Each segment must have at most one asterisk (\f[V]*\f[R]).
If a segment does not contain an asterisk, it is considered to be as
though the file separator character and an asterisk are appended.
.RS 2
.PP
For any module \f[I]M\f[R], the source path for that module is formed
from the series of segments obtained by substituting the module name
\f[I]M\f[R] for the asterisk in each segment.
.PP
\f[B]Note\f[R]: in this context, the asterisk is just used as a special
marker, to denote the position in the path of the module name.
It should not be confused with the use of \f[V]*\f[R] as a file name
wildcard character, as found on most operating systems.
.RE
.SH PATCHING MODULES
.PP
javac allows any content, whether in source or compiled form, to be
patched into any module using the \f[B]\f[VB]--patch-module\f[B]\f[R]
option.
You may want to do this to compile alternative implementations of a
class to be patched at runtime into a JVM, or to inject additional
classes into the module, such as when testing.
.PP
The form of the option is:
.IP \[bu] 2
\f[V]--patch-module\f[R]
\f[I]module-name\f[R]\f[V]=\f[R]\f[I]file-path\f[R]
(\f[I]path-separator\f[R] \f[I]file-path\f[R] )*
.PP
The path separator character is \f[V];\f[R] on Windows, and \f[V]:\f[R]
otherwise.
The paths given for the module must specify the root of a package
hierarchy for the contents of the module
.PP
The option may be given at most once for any given module.
Any content on the path will hide any like-named content later in the
path and in the patched module.
.PP
When patching source code into more than one module, the
\f[B]\f[VB]--module-source-path\f[B]\f[R] must also be used, so that the
output directory is organized in a module hierarchy, and capable of
holding the compiled class files for the modules being compiled.
.SH ANNOTATION PROCESSING
.PP
The \f[V]javac\f[R] command provides direct support for annotation
processing.
.PP
The API for annotation processors is defined in the
\f[V]javax.annotation.processing\f[R] and \f[V]javax.lang.model\f[R]
packages and subpackages.
.SS How Annotation Processing Works
.PP
Annotation processing is requested by using an option to configure
annotation processing, such as \f[B]\f[VB]-processor\f[B]\f[R],
\f[B]\f[VB]--processor-path\f[B]\f[R],
\f[B]\f[VB]--processor-module-path\f[B]\f[R] or by explicitly enabling
processing with the \f[B]\f[VB]-proc:full\f[B]\f[R] or
\f[B]\f[VB]-proc:only\f[B]\f[R] options.
Annotation processing is disabled using the
\f[B]\f[VB]-proc:none\f[B]\f[R] option.
.PP
If annotation processing is requested, the compiler searches for any
annotation processors that are available.
.PP
The search path can be specified with the
\f[B]\f[VB]-processorpath\f[B]\f[R] option.
If no path is specified, then the user class path is used.
Processors are located by means of service provider-configuration files
named \f[V]META-INF/services/javax.annotation.processing.Processor\f[R]
on the search path.
Such files should contain the names of any annotationation processors to
be used, listed one per line.
Alternatively, processors can be specified explicitly, using the
\f[B]\f[VB]-processor\f[B]\f[R] option.
.PP
After scanning the source files and classes on the command line to
determine what annotations are present, the compiler queries the
processors to determine what annotations they process.
When a match is found, the processor is called.
A processor can claim the annotations it processes, in which case no
further attempt is made to find any processors for those annotations.
After all of the annotations are claimed, the compiler does not search
for additional processors.
.PP
If any processors generate new source files, then another round of
annotation processing occurs: Any newly generated source files are
scanned, and the annotations processed as before.
Any processors called on previous rounds are also called on all
subsequent rounds.
This continues until no new source files are generated.
.PP
After a round occurs where no new source files are generated, the
annotation processors are called one last time, to give them a chance to
complete any remaining work.
Finally, unless the \f[B]\f[VB]-proc:only\f[B]\f[R] option is used, the
compiler compiles the original and all generated source files.
.PP
If you use an annotation processor that generates additional source
files to be included in the compilation, you can specify a default
module to be used for the newly generated files, for use when a module
declaration is not also generated.
In this case, use the
\f[B]\f[VB]--default-module-for-created-files\f[B]\f[R] option.
.SS Compilation Environment and Runtime Environment.
.PP
The declarations in source files and previously compiled class files are
analyzed by \f[V]javac\f[R] in a \f[I]compilation environment\f[R] that
is distinct from the \f[I]runtime environment\f[R] used to execute
\f[V]javac\f[R] itself.
Although there is a deliberate similarity between many \f[V]javac\f[R]
options and like-named options for the Java \f[B]launcher\f[R], such as
\f[V]--class-path\f[R], \f[V]--module-path\f[R] and so on, it is
important to understand that in general the \f[V]javac\f[R] options just
affect the environment in which the source files are compiled, and do
not affect the operation of \f[V]javac\f[R] itself.
.PP
The distinction between the compilation environment and runtime
environment is significant when it comes to using annotation processors.
Although annotations processors process elements (declarations) that
exist in the compilation environment, the annotation processor itself is
executed in the runtime environment.
If an annotation processor has dependencies on libraries that are not in
modules, the libraries can be placed, along with the annotation
processor itself, on the processor path.
(See the \f[B]\f[VB]--processor-path\f[B]\f[R] option.)
If the annotation processor and its dependencies are in modules, you
should use the processor module path instead.
(See the \f[B]\f[VB]--processor-module-path\f[B]\f[R] option.)
When those are insufficient, it may be necessary to provide further
configuration of the runtime environment.
This can be done in two ways:
.IP "1." 3
If \f[V]javac\f[R] is invoked from the command line, options can be
passed to the underlying runtime by prefixing the option with
\f[B]\f[VB]-J\f[B]\f[R].
.IP "2." 3
You can start an instance of a Java Virtual Machine directly and use
command line options and API to configure an environment in which
\f[V]javac\f[R] can be invoked via one of its \f[B]APIs\f[R].
.SH COMPILING FOR EARLIER RELEASES OF THE PLATFORM
.PP
\f[V]javac\f[R] can compile code that is to be used on other releases of
the platform, using either the \f[B]\f[VB]--release\f[B]\f[R] option, or
the \f[B]\f[VB]--source\f[B]\f[R]/\f[V]-source\f[R] and
\f[B]\f[VB]--target\f[B]\f[R]/\f[V]-target\f[R] options, together with
additional options to specify the platform classes.
.PP
Depending on the desired platform release, there are some restrictions
on some of the options that can be used.
.IP \[bu] 2
When compiling for JDK 8 and earlier releases, you cannot use any option
that is intended for use with the module system.
This includes all of the following options:
.RS 2
.IP \[bu] 2
\f[B]\f[VB]--module-source-path\f[B]\f[R],
\f[B]\f[VB]--upgrade-module-path\f[B]\f[R],
\f[B]\f[VB]--system\f[B]\f[R], \f[B]\f[VB]--module-path\f[B]\f[R],
\f[B]\f[VB]--add-modules\f[B]\f[R], \f[B]\f[VB]--add-exports\f[B]\f[R],
\f[V]--add-opens\f[R], \f[B]\f[VB]--add-reads\f[B]\f[R],
\f[B]\f[VB]--limit-modules\f[B]\f[R],
\f[B]\f[VB]--patch-module\f[B]\f[R]
.PP
If you use the \f[V]--source\f[R]/\f[V]-source\f[R] or
\f[V]--target\f[R]/\f[V]-target\f[R] options, you should also set the
appropriate platform classes using the boot class path family of
options.
.RE
.IP \[bu] 2
When compiling for JDK 9 and later releases, you cannot use any option
that is intended to configure the boot class path.
This includes all of the following options:
.RS 2
.IP \[bu] 2
\f[B]\f[VB]-Xbootclasspath/p:\f[B]\f[R],
\f[B]\f[VB]-Xbootclasspath\f[B]\f[R],
\f[B]\f[VB]-Xbootclasspath/a:\f[B]\f[R],
\f[B]\f[VB]-endorseddirs\f[B]\f[R],
\f[B]\f[VB]-Djava.endorsed.dirs\f[B]\f[R],
\f[B]\f[VB]-extdirs\f[B]\f[R], \f[B]\f[VB]-Djava.ext.dirs\f[B]\f[R],
\f[B]\f[VB]-profile\f[B]\f[R]
.PP
If you use the \f[V]--source\f[R]/\f[V]-source\f[R] or
\f[V]--target\f[R]/\f[V]-target\f[R] options, you should also set the
appropriate platform classes using the \f[V]--system\f[R] option to give
the location of an appropriate installed release of JDK.
.RE
.PP
When using the \f[V]--release\f[R] option, only the supported documented
API for that release may be used; you cannot use any options to break
encapsulation to access any internal classes.
.SH APIS
.PP
The \f[V]javac\f[R] compiler can be invoked using an API in three
different ways:
.TP
The \f[B]Java Compiler API\f[R]
This provides the most flexible way to invoke the compiler, including
the ability to compile source files provided in memory buffers or other
non-standard file systems.
.TP
The \f[B]ToolProvider API\f[R]
A \f[V]ToolProvider\f[R] for \f[V]javac\f[R] can be obtained by calling
\f[V]ToolProvider.findFirst(\[dq]javac\[dq])\f[R].
This returns an object with the equivalent functionality of the
command-line tool.
.RS
.PP
\f[B]Note\f[R]: This API should not be confused with the like-named API
in the \f[B]\f[VB]javax.tools\f[B]\f[R] package.
.RE
.TP
The \f[V]javac\f[R] \f[B]Legacy API\f[R]
This API is retained for backward compatibility only.
All new code should use either the Java Compiler API or the ToolProvider
API.
.PP
\f[B]Note:\f[R] All other classes and methods found in a package with
names that start with \f[V]com.sun.tools.javac\f[R] (subpackages of
\f[V]com.sun.tools.javac\f[R]) are strictly internal and subject to
change at any time.
.SH EXAMPLES OF USING -XLINT KEYS
.TP
\f[V]cast\f[R]
Warns about unnecessary and redundant casts, for example:
.RS
.RS
.PP
\f[V]String s = (String) \[dq]Hello!\[dq]\f[R]
.RE
.RE
.TP
\f[V]classfile\f[R]
Warns about issues related to class file contents.
.TP
\f[V]deprecation\f[R]
Warns about the use of deprecated items.
For example:
.RS
.IP
.nf
\f[CB]
java.util.Date myDate = new java.util.Date();
int currentDay = myDate.getDay();
\f[R]
.fi
.PP
The method \f[V]java.util.Date.getDay\f[R] has been deprecated since JDK
1.1.
.RE
.TP
\f[V]dep-ann\f[R]
Warns about items that are documented with the \f[V]\[at]deprecated\f[R]
Javadoc comment, but do not have the \f[V]\[at]Deprecated\f[R]
annotation, for example:
.RS
.IP
.nf
\f[CB]
/**
  * \[at]deprecated As of Java SE 7, replaced by {\[at]link #newMethod()}
  */
public static void deprecatedMethod() { }
public static void newMethod() { }
\f[R]
.fi
.RE
.TP
\f[V]divzero\f[R]
Warns about division by the constant integer 0, for example:
.RS
.RS
.PP
\f[V]int divideByZero = 42 / 0;\f[R]
.RE
.RE
.TP
\f[V]empty\f[R]
Warns about empty statements after \f[V]if\f[R]statements, for example:
.RS
.IP
.nf
\f[CB]
class E {
    void m() {
         if (true) ;
    }
}
\f[R]
.fi
.RE
.TP
\f[V]fallthrough\f[R]
Checks the switch blocks for fall-through cases and provides a warning
message for any that are found.
Fall-through cases are cases in a switch block, other than the last case
in the block, whose code does not include a \f[V]break\f[R] statement,
allowing code execution to fall through from that case to the next case.
For example, the code following the case 1 label in this switch block
does not end with a \f[V]break\f[R] statement:
.RS
.IP
.nf
\f[CB]
switch (x) {
case 1:
  System.out.println(\[dq]1\[dq]);
  // No break statement here.
case 2:
  System.out.println(\[dq]2\[dq]);
}
\f[R]
.fi
.PP
If the \f[V]-Xlint:fallthrough\f[R] option was used when compiling this
code, then the compiler emits a warning about possible fall-through into
case, with the line number of the case in question.
.RE
.TP
\f[V]finally\f[R]
Warns about \f[V]finally\f[R] clauses that cannot be completed normally,
for example:
.RS
.IP
.nf
\f[CB]
public static int m() {
  try {
     throw new NullPointerException();
  }  catch (NullPointerException(); {
     System.err.println(\[dq]Caught NullPointerException.\[dq]);
     return 1;
   } finally {
     return 0;
   }
  }
\f[R]
.fi
.PP
The compiler generates a warning for the \f[V]finally\f[R] block in this
example.
When the \f[V]int\f[R] method is called, it returns a value of 0.
A \f[V]finally\f[R] block executes when the \f[V]try\f[R] block exits.
In this example, when control is transferred to the \f[V]catch\f[R]
block, the \f[V]int\f[R] method exits.
However, the \f[V]finally\f[R] block must execute, so it\[aq]s executed,
even though control was transferred outside the method.
.RE
.TP
\f[V]options\f[R]
Warns about issues that related to the use of command-line options.
See \f[B]Compiling for Earlier Releases of the Platform\f[R].
.TP
\f[V]overrides\f[R]
Warns about issues related to method overrides.
For example, consider the following two classes:
.RS
.IP
.nf
\f[CB]
public class ClassWithVarargsMethod {
  void varargsMethod(String... s) { }
}

public class ClassWithOverridingMethod extends ClassWithVarargsMethod {
   \[at]Override
   void varargsMethod(String[] s) { }
}
\f[R]
.fi
.PP
The compiler generates a warning similar to the following:.
.IP
.nf
\f[CB]
warning: [override] varargsMethod(String[]) in ClassWithOverridingMethod
overrides varargsMethod(String...) in ClassWithVarargsMethod; overriding
method is missing \[aq]...\[aq]
\f[R]
.fi
.PP
When the compiler encounters a \f[V]varargs\f[R] method, it translates
the \f[V]varargs\f[R] formal parameter into an array.
In the method \f[V]ClassWithVarargsMethod.varargsMethod\f[R], the
compiler translates the \f[V]varargs\f[R] formal parameter
\f[V]String... s\f[R] to the formal parameter \f[V]String[] s\f[R], an
array that matches the formal parameter of the method
\f[V]ClassWithOverridingMethod.varargsMethod\f[R].
Consequently, this example compiles.
.RE
.TP
\f[V]path\f[R]
Warns about invalid path elements and nonexistent path directories on
the command line (with regard to the class path, the source path, and
other paths).
Such warnings cannot be suppressed with the
\f[V]\[at]SuppressWarnings\f[R] annotation.
For example:
.RS
.IP \[bu] 2
\f[B]Linux and macOS:\f[R]
\f[V]javac -Xlint:path -classpath /nonexistentpath Example.java\f[R]
.IP \[bu] 2
\f[B]Windows:\f[R]
\f[V]javac -Xlint:path -classpath C:\[rs]nonexistentpath Example.java\f[R]
.RE
.TP
\f[V]processing\f[R]
Warns about issues related to annotation processing.
The compiler generates this warning when you have a class that has an
annotation, and you use an annotation processor that cannot handle that
type of annotation.
For example, the following is a simple annotation processor:
.RS
.PP
\f[B]Source file AnnoProc.java\f[R]:
.IP
.nf
\f[CB]
import java.util.*;
import javax.annotation.processing.*;
import javax.lang.model.*;
import javax.lang.model.element.*;

\[at]SupportedAnnotationTypes(\[dq]NotAnno\[dq])
public class AnnoProc extends AbstractProcessor {
  public boolean process(Set<? extends TypeElement> elems, RoundEnvironment renv){
     return true;
  }

  public SourceVersion getSupportedSourceVersion() {
     return SourceVersion.latest();
   }
}
\f[R]
.fi
.PP
\f[B]Source file AnnosWithoutProcessors.java\f[R]:
.IP
.nf
\f[CB]
\[at]interface Anno { }

\[at]Anno
class AnnosWithoutProcessors { }
\f[R]
.fi
.PP
The following commands compile the annotation processor
\f[V]AnnoProc\f[R], then run this annotation processor against the
source file \f[V]AnnosWithoutProcessors.java\f[R]:
.IP
.nf
\f[CB]
javac AnnoProc.java
javac -cp . -Xlint:processing -processor AnnoProc -proc:only AnnosWithoutProcessors.java
\f[R]
.fi
.PP
When the compiler runs the annotation processor against the source file
\f[V]AnnosWithoutProcessors.java\f[R], it generates the following
warning:
.IP
.nf
\f[CB]
warning: [processing] No processor claimed any of these annotations: Anno
\f[R]
.fi
.PP
To resolve this issue, you can rename the annotation defined and used in
the class \f[V]AnnosWithoutProcessors\f[R] from \f[V]Anno\f[R] to
\f[V]NotAnno\f[R].
.RE
.TP
\f[V]rawtypes\f[R]
Warns about unchecked operations on raw types.
The following statement generates a \f[V]rawtypes\f[R] warning:
.RS
.RS
.PP
\f[V]void countElements(List l) { ... }\f[R]
.RE
.PP
The following example does not generate a \f[V]rawtypes\f[R] warning:
.RS
.PP
\f[V]void countElements(List<?> l) { ... }\f[R]
.RE
.PP
\f[V]List\f[R] is a raw type.
However, \f[V]List<?>\f[R] is an unbounded wildcard parameterized type.
Because \f[V]List\f[R] is a parameterized interface, always specify its
type argument.
In this example, the \f[V]List\f[R] formal argument is specified with an
unbounded wildcard (\f[V]?\f[R]) as its formal type parameter, which
means that the \f[V]countElements\f[R] method can accept any
instantiation of the \f[V]List\f[R] interface.
.RE
.TP
\f[V]serial\f[R]
Warns about missing \f[V]serialVersionUID\f[R] definitions on
serializable classes.
For example:
.RS
.IP
.nf
\f[CB]
public class PersistentTime implements Serializable
{
  private Date time;

   public PersistentTime() {
     time = Calendar.getInstance().getTime();
   }

   public Date getTime() {
     return time;
   }
}
\f[R]
.fi
.PP
The compiler generates the following warning:
.IP
.nf
\f[CB]
warning: [serial] serializable class PersistentTime has no definition of
serialVersionUID
\f[R]
.fi
.PP
If a serializable class does not explicitly declare a field named
\f[V]serialVersionUID\f[R], then the serialization runtime environment
calculates a default \f[V]serialVersionUID\f[R] value for that class
based on various aspects of the class, as described in the Java Object
Serialization Specification.
However, it\[aq]s strongly recommended that all serializable classes
explicitly declare \f[V]serialVersionUID\f[R] values because the default
process of computing \f[V]serialVersionUID\f[R] values is highly
sensitive to class details that can vary depending on compiler
implementations.
As a result, this might cause an unexpected
\f[V]InvalidClassExceptions\f[R] during deserialization.
To guarantee a consistent \f[V]serialVersionUID\f[R] value across
different Java compiler implementations, a serializable class must
declare an explicit \f[V]serialVersionUID\f[R] value.
.RE
.TP
\f[V]static\f[R]
Warns about issues relating to the use of static variables, for example:
.RS
.IP
.nf
\f[CB]
class XLintStatic {
    static void m1() { }
    void m2() { this.m1(); }
}
\f[R]
.fi
.PP
The compiler generates the following warning:
.IP
.nf
\f[CB]
warning: [static] static method should be qualified by type name,
XLintStatic, instead of by an expression
\f[R]
.fi
.PP
To resolve this issue, you can call the \f[V]static\f[R] method
\f[V]m1\f[R] as follows:
.RS
.PP
\f[V]XLintStatic.m1();\f[R]
.RE
.PP
Alternately, you can remove the \f[V]static\f[R] keyword from the
declaration of the method \f[V]m1\f[R].
.RE
.TP
\f[V]this-escape\f[R]
Warns about constructors leaking \f[V]this\f[R] prior to subclass
initialization.
For example, this class:
.RS
.IP
.nf
\f[CB]
public class MyClass {
  public MyClass() {
    System.out.println(this.hashCode());
  }
}
\f[R]
.fi
.PP
generates the following warning:
.IP
.nf
\f[CB]
MyClass.java:3: warning: [this-escape] possible \[aq]this\[aq] escape
                         before subclass is fully initialized
    System.out.println(this.hashCode());
                                    \[ha]
\f[R]
.fi
.PP
A \[aq]this\[aq] escape warning is generated when a constructor does
something that might result in a subclass method being invoked before
the constructor returns.
In such cases the subclass method would be operating on an incompletely
initialized instance.
In the above example, a subclass of \f[V]MyClass\f[R] that overrides
\f[V]hashCode()\f[R] to incorporate its own fields would likely produce
an incorrect result when invoked as shown.
.PP
Warnings are only generated if a subclass could exist that is outside of
the current module (or package, if no module) being compiled.
So, for example, constructors in final and non-public classes do not
generate warnings.
.RE
.TP
\f[V]try\f[R]
Warns about issues relating to the use of \f[V]try\f[R] blocks,
including try-with-resources statements.
For example, a warning is generated for the following statement because
the resource \f[V]ac\f[R] declared in the \f[V]try\f[R] block is not
used:
.RS
.IP
.nf
\f[CB]
try ( AutoCloseable ac = getResource() ) {    // do nothing}
\f[R]
.fi
.RE
.TP
\f[V]unchecked\f[R]
Gives more detail for unchecked conversion warnings that are mandated by
the Java Language Specification, for example:
.RS
.IP
.nf
\f[CB]
List l = new ArrayList<Number>();
List<String> ls = l;       // unchecked warning
\f[R]
.fi
.PP
During type erasure, the types \f[V]ArrayList<Number>\f[R] and
\f[V]List<String>\f[R] become \f[V]ArrayList\f[R] and \f[V]List\f[R],
respectively.
.PP
The \f[V]ls\f[R] command has the parameterized type
\f[V]List<String>\f[R].
When the \f[V]List\f[R] referenced by \f[V]l\f[R] is assigned to
\f[V]ls\f[R], the compiler generates an unchecked warning.
At compile time, the compiler and JVM cannot determine whether
\f[V]l\f[R] refers to a \f[V]List<String>\f[R] type.
In this case, \f[V]l\f[R] does not refer to a \f[V]List<String>\f[R]
type.
As a result, heap pollution occurs.
.PP
A heap pollution situation occurs when the \f[V]List\f[R] object
\f[V]l\f[R], whose static type is \f[V]List<Number>\f[R], is assigned to
another \f[V]List\f[R] object, \f[V]ls\f[R], that has a different static
type, \f[V]List<String>\f[R].
However, the compiler still allows this assignment.
It must allow this assignment to preserve backward compatibility with
releases of Java SE that do not support generics.
Because of type erasure, \f[V]List<Number>\f[R] and
\f[V]List<String>\f[R] both become \f[V]List\f[R].
Consequently, the compiler allows the assignment of the object
\f[V]l\f[R], which has a raw type of \f[V]List\f[R], to the object
\f[V]ls\f[R].
.RE
.TP
\f[V]varargs\f[R]
Warns about unsafe use of variable arguments (\f[V]varargs\f[R])
methods, in particular, those that contain non-reifiable arguments, for
example:
.RS
.IP
.nf
\f[CB]
public class ArrayBuilder {
  public static <T> void addToList (List<T> listArg, T... elements) {
    for (T x : elements) {
      listArg.add(x);
    }
  }
}
\f[R]
.fi
.PP
A non-reifiable type is a type whose type information is not fully
available at runtime.
.PP
The compiler generates the following warning for the definition of the
method \f[V]ArrayBuilder.addToList\f[R]:
.IP
.nf
\f[CB]
warning: [varargs] Possible heap pollution from parameterized vararg type T
\f[R]
.fi
.PP
When the compiler encounters a varargs method, it translates the
\f[V]varargs\f[R] formal parameter into an array.
However, the Java programming language does not permit the creation of
arrays of parameterized types.
In the method \f[V]ArrayBuilder.addToList\f[R], the compiler translates
the \f[V]varargs\f[R] formal parameter \f[V]T...\f[R] elements to the
formal parameter \f[V]T[]\f[R] elements, an array.
However, because of type erasure, the compiler converts the
\f[V]varargs\f[R] formal parameter to \f[V]Object[]\f[R] elements.
Consequently, there\[aq]s a possibility of heap pollution.
.RE
