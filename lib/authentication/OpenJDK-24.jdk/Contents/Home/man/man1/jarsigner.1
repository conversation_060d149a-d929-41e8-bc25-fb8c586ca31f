'\" t
.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JARSIGNER" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jarsigner - sign and verify Java Archive (JAR) files
.SH SYNOPSIS
.PP
\f[V]jarsigner\f[R] [\f[I]options\f[R]] \f[I]jar-file\f[R]
\f[I]alias\f[R]
.PP
\f[V]jarsigner\f[R] \f[V]-verify\f[R] [\f[I]options\f[R]]
\f[I]jar-file\f[R] [\f[I]alias\f[R] ...]
.PP
\f[V]jarsigner\f[R] \f[V]-version\f[R]
.TP
\f[I]options\f[R]
The command-line options.
See \f[B]Options for jarsigner\f[R].
.TP
\f[V]-verify\f[R]
The \f[V]-verify\f[R] option can take zero or more keystore alias names
after the JAR file name.
When the \f[V]-verify\f[R] option is specified, the \f[V]jarsigner\f[R]
command checks that the certificate used to verify each signed entry in
the JAR file matches one of the keystore aliases.
The aliases are defined in the keystore specified by \f[V]-keystore\f[R]
or the default keystore.
.RS
.PP
If you also specify the \f[V]-strict\f[R] option, and the
\f[V]jarsigner\f[R] command detects severe warnings, the message,
\[dq]jar verified, with signer errors\[dq] is displayed.
.RE
.TP
\f[I]jar-file\f[R]
The JAR file to be signed.
.RS
.PP
If you also specified the \f[V]-strict\f[R] option, and the
\f[V]jarsigner\f[R] command detected severe warnings, the message,
\[dq]jar signed, with signer errors\[dq] is displayed.
.RE
.TP
\f[I]alias\f[R]
The aliases are defined in the keystore specified by \f[V]-keystore\f[R]
or the default keystore.
.TP
\f[V]-version\f[R]
The \f[V]-version\f[R] option prints the program version of
\f[V]jarsigner\f[R].
.SH DESCRIPTION
.PP
The \f[V]jarsigner\f[R] tool has two purposes:
.IP \[bu] 2
To sign Java Archive (JAR) files.
.IP \[bu] 2
To verify the signatures and integrity of signed JAR files.
.PP
The JAR feature enables the packaging of class files, images, sounds,
and other digital data in a single file for faster and easier
distribution.
A tool named \f[V]jar\f[R] enables developers to produce JAR files.
(Technically, any ZIP file can also be considered a JAR file, although
when created by the \f[V]jar\f[R] command or processed by the
\f[V]jarsigner\f[R] command, JAR files also contain a
\f[V]META-INF/MANIFEST.MF\f[R] file.)
.PP
A digital signature is a string of bits that is computed from some data
(the data being signed) and the private key of an entity (a person,
company, and so on).
Similar to a handwritten signature, a digital signature has many useful
characteristics:
.IP \[bu] 2
Its authenticity can be verified by a computation that uses the public
key corresponding to the private key used to generate the signature.
.IP \[bu] 2
It can\[aq]t be forged, assuming the private key is kept secret.
.IP \[bu] 2
It is a function of the data signed and thus can\[aq]t be claimed to be
the signature for other data as well.
.IP \[bu] 2
The signed data can\[aq]t be changed.
If the data is changed, then the signature can\[aq]t be verified as
authentic.
.PP
To generate an entity\[aq]s signature for a file, the entity must first
have a public/private key pair associated with it and one or more
certificates that authenticate its public key.
A certificate is a digitally signed statement from one entity that says
that the public key of another entity has a particular value.
.PP
The \f[V]jarsigner\f[R] command uses key and certificate information
from a keystore to generate digital signatures for JAR files.
A keystore is a database of private keys and their associated X.509
certificate chains that authenticate the corresponding public keys.
The \f[V]keytool\f[R] command is used to create and administer
keystores.
.PP
The \f[V]jarsigner\f[R] command uses an entity\[aq]s private key to
generate a signature.
The signed JAR file contains, among other things, a copy of the
certificate from the keystore for the public key corresponding to the
private key used to sign the file.
The \f[V]jarsigner\f[R] command can verify the digital signature of the
signed JAR file using the certificate inside it (in its signature block
file).
.PP
The \f[V]jarsigner\f[R] command can generate signatures that include a
time stamp that enables a systems or deployer to check whether the JAR
file was signed while the signing certificate was still valid.
.PP
In addition, APIs allow applications to obtain the timestamp
information.
.PP
At this time, the \f[V]jarsigner\f[R] command can only sign JAR files
created by the \f[V]jar\f[R] command or zip files.
JAR files are the same as zip files, except they also have a
\f[V]META-INF/MANIFEST.MF\f[R] file.
A \f[V]META-INF/MANIFEST.MF\f[R] file is created when the
\f[V]jarsigner\f[R] command signs a zip file.
.PP
The default \f[V]jarsigner\f[R] command behavior is to sign a JAR or zip
file.
Use the \f[V]-verify\f[R] option to verify a signed JAR file.
.PP
The \f[V]jarsigner\f[R] command also attempts to validate the
signer\[aq]s certificate after signing or verifying.
During validation, it checks the revocation status of each certificate
in the signer\[aq]s certificate chain when the \f[V]-revCheck\f[R]
option is specified.
If there is a validation error or any other problem, the command
generates warning messages.
If you specify the \f[V]-strict\f[R] option, then the command treats
severe warnings as errors.
See \f[B]Errors and Warnings\f[R].
.SH KEYSTORE ALIASES
.PP
All keystore entities are accessed with unique aliases.
.PP
When you use the \f[V]jarsigner\f[R] command to sign a JAR file, you
must specify the alias for the keystore entry that contains the private
key needed to generate the signature.
If no output file is specified, it overwrites the original JAR file with
the signed JAR file.
.PP
Keystores are protected with a password, so the store password must be
specified.
You are prompted for it when you don\[aq]t specify it on the command
line.
Similarly, private keys are protected in a keystore with a password, so
the private key\[aq]s password must be specified, and you are prompted
for the password when you don\[aq]t specify it on the command line and
it isn\[aq]t the same as the store password.
.SH KEYSTORE LOCATION
.PP
The \f[V]jarsigner\f[R] command has a \f[V]-keystore\f[R] option for
specifying the URL of the keystore to be used.
The keystore is by default stored in a file named \f[V].keystore\f[R] in
the user\[aq]s home directory, as determined by the \f[V]user.home\f[R]
system property.
.PP
\f[B]Linux and macOS:\f[R] \f[V]user.home\f[R] defaults to the
user\[aq]s home directory.
.PP
The input stream from the \f[V]-keystore\f[R] option is passed to the
\f[V]KeyStore.load\f[R] method.
If \f[V]NONE\f[R] is specified as the URL, then a null stream is passed
to the \f[V]KeyStore.load\f[R] method.
\f[V]NONE\f[R] should be specified when the \f[V]KeyStore\f[R] class
isn\[aq]t file based, for example, when it resides on a hardware token
device.
.SH KEYSTORE IMPLEMENTATION
.PP
The \f[V]KeyStore\f[R] class provided in the \f[V]java.security\f[R]
package supplies a number of well-defined interfaces to access and
modify the information in a keystore.
You can have multiple different concrete implementations, where each
implementation is for a particular type of keystore.
.PP
Currently, there are two command-line tools that use keystore
implementations (\f[V]keytool\f[R] and \f[V]jarsigner\f[R]).
.PP
The default keystore implementation is \f[V]PKCS12\f[R].
This is a cross platform keystore based on the RSA PKCS12 Personal
Information Exchange Syntax Standard.
This standard is primarily meant for storing or transporting a
user\[aq]s private keys, certificates, and miscellaneous secrets.
There is another built-in implementation, provided by Oracle.
It implements the keystore as a file with a proprietary keystore type
(format) named \f[V]JKS\f[R].
It protects each private key with its individual password, and also
protects the integrity of the entire keystore with a (possibly
different) password.
.PP
Keystore implementations are provider-based, which means the application
interfaces supplied by the \f[V]KeyStore\f[R] class are implemented in
terms of a Service Provider Interface (SPI).
There is a corresponding abstract \f[V]KeystoreSpi\f[R] class, also in
the \f[V]java.security package\f[R], that defines the Service Provider
Interface methods that providers must implement.
The term provider refers to a package or a set of packages that supply a
concrete implementation of a subset of services that can be accessed by
the Java Security API.
To provide a keystore implementation, clients must implement a provider
and supply a \f[V]KeystoreSpi\f[R] subclass implementation, as described
in \f[B]How to Implement a Provider in the Java Cryptography
Architecture\f[R]
[https://www.oracle.com/pls/topic/lookup?ctx=en/java/javase&id=security_guide_implement_provider_jca].
.PP
Applications can choose different types of keystore implementations from
different providers, with the \f[V]getInstance\f[R] factory method in
the \f[V]KeyStore\f[R] class.
A keystore type defines the storage and data format of the keystore
information and the algorithms used to protect private keys in the
keystore and the integrity of the keystore itself.
Keystore implementations of different types aren\[aq]t compatible.
.PP
The \f[V]jarsigner\f[R] commands can read file-based keystores from any
location that can be specified using a URL.
In addition, these commands can read non-file-based keystores such as
those provided by MSCAPI on Windows and PKCS11 on all platforms.
.PP
For the \f[V]jarsigner\f[R] and \f[V]keytool\f[R] commands, you can
specify a keystore type at the command line with the
\f[V]-storetype\f[R] option.
.PP
If you don\[aq]t explicitly specify a keystore type, then the tools
choose a keystore implementation based on the value of the
\f[V]keystore.type\f[R] property specified in the security properties
file.
The security properties file is called \f[V]java.security\f[R], and it
resides in the JDK security properties directory,
\f[V]java.home/conf/security\f[R].
.PP
Each tool gets the \f[V]keystore.type\f[R] value and then examines all
the installed providers until it finds one that implements keystores of
that type.
It then uses the keystore implementation from that provider.
.PP
The \f[V]KeyStore\f[R] class defines a static method named
\f[V]getDefaultType\f[R] that lets applications retrieve the value of
the \f[V]keystore.type\f[R] property.
The following line of code creates an instance of the default keystore
type as specified in the \f[V]keystore.type\f[R] property:
.RS
.PP
\f[V]KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());\f[R]
.RE
.PP
The default keystore type is \f[V]pkcs12\f[R], which is a cross platform
keystore based on the RSA PKCS12 Personal Information Exchange Syntax
Standard.
This is specified by the following line in the security properties file:
.RS
.PP
\f[V]keystore.type=pkcs12\f[R]
.RE
.PP
Case doesn\[aq]t matter in keystore type designations.
For example, \f[V]JKS\f[R] is the same as \f[V]jks\f[R].
.PP
To have the tools utilize a keystore implementation other than the
default, you can change that line to specify a different keystore type.
For example, if you want to use the Oracle\[aq]s \f[V]jks\f[R] keystore
implementation, then change the line to the following:
.RS
.PP
\f[V]keystore.type=jks\f[R]
.RE
.SH SUPPORTED ALGORITHMS
.PP
By default, the \f[V]jarsigner\f[R] command signs a JAR file using one
of the following algorithms and block file extensions depending on the
type and size of the private key:
.PP
Default Signature Algorithms and Block File Extensions
.TS
tab(@);
l l l l.
T{
keyalg
T}@T{
key size
T}@T{
default sigalg
T}@T{
block file extension
T}
_
T{
DSA
T}@T{
any size
T}@T{
SHA256withDSA
T}@T{
\&.DSA
T}
T{
RSA
T}@T{
< 624
T}@T{
SHA256withRSA
T}@T{
\&.RSA
T}
T{
T}@T{
<= 7680
T}@T{
SHA384withRSA
T}@T{
T}
T{
T}@T{
> 7680
T}@T{
SHA512withRSA
T}@T{
T}
T{
EC
T}@T{
< 512
T}@T{
SHA384withECDSA
T}@T{
\&.EC
T}
T{
T}@T{
>= 512
T}@T{
SHA512withECDSA
T}@T{
T}
T{
RSASSA-PSS
T}@T{
< 624
T}@T{
RSASSA-PSS (with SHA-256)
T}@T{
\&.RSA
T}
T{
T}@T{
<= 7680
T}@T{
RSASSA-PSS (with SHA-384)
T}@T{
T}
T{
T}@T{
> 7680
T}@T{
RSASSA-PSS (with SHA-512)
T}@T{
T}
T{
EdDSA
T}@T{
255
T}@T{
Ed25519
T}@T{
\&.EC
T}
T{
T}@T{
448
T}@T{
Ed448
T}@T{
T}
.TE
.IP \[bu] 2
If an RSASSA-PSS key is encoded with parameters, then jarsigner will use
the same parameters in the signature.
Otherwise, jarsigner will use parameters that are determined by the size
of the key as specified in the table above.
For example, an 3072-bit RSASSA-PSS key will use RSASSA-PSS as the
signature algorithm and SHA-384 as the hash and MGF1 algorithms.
.IP \[bu] 2
If a key algorithm is not listed in this table, the \f[V].DSA\f[R]
extension is used when signing a JAR file.
.PP
These default signature algorithms can be overridden by using the
\f[V]-sigalg\f[R] option.
.PP
The \f[V]jarsigner\f[R] command uses the
\f[V]jdk.jar.disabledAlgorithms\f[R] and
\f[V]jdk.security.legacyAlgorithms\f[R] security properties to determine
which algorithms are considered a security risk.
If the JAR file was signed with any algorithms that are disabled, it
will be treated as an unsigned JAR file.
If the JAR file was signed with any legacy algorithms, it will be
treated as signed with an informational warning to inform users that the
legacy algorithm will be disabled in a future update.
For detailed verification output, include
\f[V]-J-Djava.security.debug=jar\f[R].
The \f[V]jdk.jar.disabledAlgorithms\f[R] and
\f[V]jdk.security.legacyAlgorithms\f[R] security properties are defined
in the \f[V]java.security\f[R] file (located in the JDK\[aq]s
\f[V]$JAVA_HOME/conf/security\f[R] directory).
.PP
\f[B]Note:\f[R]
.PP
In order to improve out of the box security, default key size and
signature algorithm names are periodically updated to stronger values
with each release of the JDK.
If interoperability with older releases of the JDK is important, please
make sure the defaults are supported by those releases, or alternatively
use the \f[V]-sigalg\f[R] option to override the default values at your
own risk.
.SH THE SIGNED JAR FILE
.PP
When the \f[V]jarsigner\f[R] command is used to sign a JAR file, the
output signed JAR file is exactly the same as the input JAR file, except
that it has two additional files placed in the META-INF directory:
.IP \[bu] 2
A signature file with an \f[V].SF\f[R] extension
.IP \[bu] 2
A signature block file with a \f[V].DSA\f[R], \f[V].RSA\f[R], or
\f[V].EC\f[R] extension
.PP
The base file names for these two files come from the value of the
\f[V]-sigfile\f[R] option.
For example, when the option is \f[V]-sigfile MKSIGN\f[R], the files are
named \f[V]MKSIGN.SF\f[R] and \f[V]MKSIGN.RSA\f[R].
In this document, we assume the signer always uses an RSA key.
.PP
If no \f[V]-sigfile\f[R] option appears on the command line, then the
base file name for the \f[V].SF\f[R] and the signature block files is
the first 8 characters of the alias name specified on the command line,
all converted to uppercase.
If the alias name has fewer than 8 characters, then the full alias name
is used.
If the alias name contains any characters that aren\[aq]t allowed in a
signature file name, then each such character is converted to an
underscore (_) character in forming the file name.
Valid characters include letters, digits, underscores, and hyphens.
.SH SIGNATURE FILE
.PP
A signature file (\f[V].SF\f[R] file) looks similar to the manifest file
that is always included in a JAR file when the \f[V]jarsigner\f[R]
command is used to sign the file.
For each source file included in the JAR file, the \f[V].SF\f[R] file
has two lines, such as in the manifest file, that list the following:
.IP \[bu] 2
File name
.IP \[bu] 2
Name of the digest algorithm (SHA)
.IP \[bu] 2
SHA digest value
.PP
\f[B]Note:\f[R]
.PP
The name of the digest algorithm (SHA) and the SHA digest value are on
the same line.
.PP
In the manifest file, the SHA digest value for each source file is the
digest (hash) of the binary data in the source file.
In the \f[V].SF\f[R] file, the digest value for a specified source file
is the hash of the two lines in the manifest file for the source file.
.PP
The signature file, by default, includes a header with a hash of the
whole manifest file.
The header also contains a hash of the manifest header.
The presence of the header enables verification optimization.
See \f[B]JAR File Verification\f[R].
.SH SIGNATURE BLOCK FILE
.PP
The \f[V].SF\f[R] file is signed and the signature is placed in the
signature block file.
This file also contains, encoded inside it, the certificate or
certificate chain from the keystore that authenticates the public key
corresponding to the private key used for signing.
The file has the extension \f[V].DSA\f[R], \f[V].RSA\f[R], or
\f[V].EC\f[R], depending on the key algorithm used.
See the table in \f[B]Supported Algorithms\f[R].
.SH SIGNATURE TIME STAMP
.PP
The \f[V]jarsigner\f[R] command used with the following options
generates and stores a signature time stamp when signing a JAR file:
.IP \[bu] 2
\f[V]-tsa\f[R] \f[I]url\f[R]
.IP \[bu] 2
\f[V]-tsacert\f[R] \f[I]alias\f[R]
.IP \[bu] 2
\f[V]-tsapolicyid\f[R] \f[I]policyid\f[R]
.IP \[bu] 2
\f[V]-tsadigestalg\f[R] \f[I]algorithm\f[R]
.PP
See \f[B]Options for jarsigner\f[R].
.SH JAR FILE VERIFICATION
.PP
A successful JAR file verification occurs when the signatures are valid,
and none of the files that were in the JAR file when the signatures were
generated have changed since then.
JAR file verification involves the following steps:
.IP "1." 3
Verify the signature of the \f[V].SF\f[R] file.
.RS 4
.PP
The verification ensures that the signature stored in each signature
block file was generated using the private key corresponding to the
public key whose certificate (or certificate chain) also appears in the
signature block file.
It also ensures that the signature is a valid signature of the
corresponding signature (\f[V].SF\f[R]) file, and thus the \f[V].SF\f[R]
file wasn\[aq]t tampered with.
.RE
.IP "2." 3
Verify the digest listed in each entry in the \f[V].SF\f[R] file with
each corresponding section in the manifest.
.RS 4
.PP
The \f[V].SF\f[R] file by default includes a header that contains a hash
of the entire manifest file.
When the header is present, the verification can check to see whether or
not the hash in the header matches the hash of the manifest file.
If there is a match, then verification proceeds to the next step.
.PP
If there is no match, then a less optimized verification is required to
ensure that the hash in each source file information section in the
\f[V].SF\f[R] file equals the hash of its corresponding section in the
manifest file.
See Signature File.
.PP
One reason the hash of the manifest file that is stored in the
\f[V].SF\f[R] file header might not equal the hash of the current
manifest file is that it might contain sections for newly added files
after the file was signed.
For example, suppose one or more files were added to the signed JAR file
(using the \f[V]jar\f[R] tool) that already contains a signature and a
\f[V].SF\f[R] file.
If the JAR file is signed again by a different signer, then the manifest
file is changed (sections are added to it for the new files by the
\f[V]jarsigner\f[R] tool) and a new \f[V].SF\f[R] file is created, but
the original \f[V].SF\f[R] file is unchanged.
A verification is still considered successful if none of the files that
were in the JAR file when the original signature was generated have been
changed since then.
This is because the hashes in the non-header sections of the
\f[V].SF\f[R] file equal the hashes of the corresponding sections in the
manifest file.
.RE
.IP "3." 3
Read each file in the JAR file that has an entry in the \f[V].SF\f[R]
file.
While reading, compute the file\[aq]s digest and compare the result with
the digest for this file in the manifest section.
The digests should be the same or verification fails.
.RS 4
.PP
If any serious verification failures occur during the verification
process, then the process is stopped and a security exception is thrown.
The \f[V]jarsigner\f[R] command catches and displays the exception.
.RE
.IP "4." 3
Check for disabled algorithm usage.
See \f[B]Supported Algorithms\f[R].
.PP
\f[B]Note:\f[R]
.PP
You should read any addition warnings (or errors if you specified the
\f[V]-strict\f[R] option), as well as the content of the certificate (by
specifying the \f[V]-verbose\f[R] and \f[V]-certs\f[R] options) to
determine if the signature can be trusted.
.SH MULTIPLE SIGNATURES FOR A JAR FILE
.PP
A JAR file can be signed by multiple people by running the
\f[V]jarsigner\f[R] command on the file multiple times and specifying
the alias for a different person each time, as follows:
.IP
.nf
\f[CB]
jarsigner myBundle.jar susan
jarsigner myBundle.jar kevin
\f[R]
.fi
.PP
When a JAR file is signed multiple times, there are multiple
\f[V].SF\f[R] and signature block files in the resulting JAR file, one
pair for each signature.
In the previous example, the output JAR file includes files with the
following names:
.IP
.nf
\f[CB]
SUSAN.SF
SUSAN.RSA
KEVIN.SF
KEVIN.RSA
\f[R]
.fi
.SH OPTIONS FOR JARSIGNER
.PP
The following sections describe the options for the \f[V]jarsigner\f[R].
Be aware of the following standards:
.IP \[bu] 2
All option names are preceded by a hyphen sign (-).
.IP \[bu] 2
The options can be provided in any order.
.IP \[bu] 2
Items that are in italics or underlined (option values) represent the
actual values that must be supplied.
.IP \[bu] 2
The \f[V]-storepass\f[R], \f[V]-keypass\f[R], \f[V]-sigfile\f[R],
\f[V]-sigalg\f[R], \f[V]-digestalg\f[R], \f[V]-signedjar\f[R], and
TSA-related options are only relevant when signing a JAR file; they
aren\[aq]t relevant when verifying a signed JAR file.
The \f[V]-keystore\f[R] option is relevant for signing and verifying a
JAR file.
In addition, aliases are specified when signing and verifying a JAR
file.
.TP
\f[V]-keystore\f[R] \f[I]url\f[R]
Specifies the URL that tells the keystore location.
This defaults to the file \f[V].keystore\f[R] in the user\[aq]s home
directory, as determined by the \f[V]user.home\f[R] system property.
.RS
.PP
A keystore is required when signing.
You must explicitly specify a keystore when the default keystore
doesn\[aq]t exist or if you want to use one other than the default.
.PP
A keystore isn\[aq]t required when verifying, but if one is specified or
the default exists and the \f[V]-verbose\f[R] option was also specified,
then additional information is output regarding whether or not any of
the certificates used to verify the JAR file are contained in that
keystore.
.PP
The \f[V]-keystore\f[R] argument can be a file name and path
specification rather than a URL, in which case it is treated the same as
a file: URL, for example, the following are equivalent:
.IP \[bu] 2
\f[V]-keystore\f[R] \f[I]filePathAndName\f[R]
.IP \[bu] 2
\f[V]-keystore file:\f[R]\f[I]filePathAndName\f[R]
.PP
If the Sun PKCS #11 provider was configured in the
\f[V]java.security\f[R] security properties file (located in the
JDK\[aq]s \f[V]$JAVA_HOME/conf/security\f[R] directory), then the
\f[V]keytool\f[R] and \f[V]jarsigner\f[R] tools can operate on the PKCS
#11 token by specifying these options:
.RS
.PP
\f[V]-keystore NONE -storetype PKCS11\f[R]
.RE
.PP
For example, the following command lists the contents of the configured
PKCS#11 token:
.RS
.PP
\f[V]keytool -keystore NONE -storetype PKCS11 -list\f[R]
.RE
.RE
.TP
\f[V]-storepass\f[R] [\f[V]:env\f[R] | \f[V]:file\f[R]] \f[I]argument\f[R]
Specifies the password that is required to access the keystore.
This is only needed when signing (not verifying) a JAR file.
In that case, if a \f[V]-storepass\f[R] option isn\[aq]t provided at the
command line, then the user is prompted for the password.
.RS
.PP
If the modifier \f[V]env\f[R] or \f[V]file\f[R] isn\[aq]t specified,
then the password has the value \f[V]argument\f[R].
Otherwise, the password is retrieved as follows:
.IP \[bu] 2
\f[V]env\f[R]: Retrieve the password from the environment variable named
\f[I]argument\f[R].
.IP \[bu] 2
\f[V]file\f[R]: Retrieve the password from the file named
\f[I]argument\f[R].
.PP
\f[B]Note:\f[R]
.PP
The password shouldn\[aq]t be specified on the command line or in a
script unless it is for testing purposes, or you are on a secure system.
.RE
.TP
\f[V]-storetype\f[R] \f[I]storetype\f[R]
Specifies the type of keystore to be instantiated.
The default keystore type is the one that is specified as the value of
the \f[V]keystore.type\f[R] property in the security properties file,
which is returned by the static \f[V]getDefaultType\f[R] method in
\f[V]java.security.KeyStore\f[R].
.RS
.PP
The PIN for a PKCS #11 token can also be specified with the
\f[V]-storepass\f[R] option.
If none is specified, then the \f[V]keytool\f[R] and \f[V]jarsigner\f[R]
commands prompt for the token PIN.
If the token has a protected authentication path (such as a dedicated
PIN-pad or a biometric reader), then the \f[V]-protected\f[R] option
must be specified and no password options can be specified.
.RE
.TP
\f[V]-keypass\f[R] [\f[V]:env\f[R] | \f[V]:file\f[R]] \f[I]argument\f[R] \f[V]-certchain\f[R] \f[I]file\f[R]
Specifies the password used to protect the private key of the keystore
entry addressed by the alias specified on the command line.
The password is required when using \f[V]jarsigner\f[R] to sign a JAR
file.
If no password is provided on the command line, and the required
password is different from the store password, then the user is prompted
for it.
.RS
.PP
If the modifier \f[V]env\f[R] or \f[V]file\f[R] isn\[aq]t specified,
then the password has the value \f[V]argument\f[R].
Otherwise, the password is retrieved as follows:
.IP \[bu] 2
\f[V]env\f[R]: Retrieve the password from the environment variable named
\f[I]argument\f[R].
.IP \[bu] 2
\f[V]file\f[R]: Retrieve the password from the file named
\f[I]argument\f[R].
.PP
\f[B]Note:\f[R]
.PP
The password shouldn\[aq]t be specified on the command line or in a
script unless it is for testing purposes, or you are on a secure system.
.RE
.TP
\f[V]-certchain\f[R] \f[I]file\f[R]
Specifies the certificate chain to be used when the certificate chain
associated with the private key of the keystore entry that is addressed
by the alias specified on the command line isn\[aq]t complete.
This can happen when the keystore is located on a hardware token where
there isn\[aq]t enough capacity to hold a complete certificate chain.
The file can be a sequence of concatenated X.509 certificates, or a
single PKCS#7 formatted data block, either in binary encoding format or
in printable encoding format (also known as Base64 encoding) as defined
by \f[B]Internet RFC 1421 Certificate Encoding Standard\f[R]
[http://tools.ietf.org/html/rfc1421].
.TP
\f[V]-sigfile\f[R] \f[I]file\f[R]
Specifies the base file name to be used for the generated \f[V].SF\f[R]
and signature block files.
For example, if file is \f[V]DUKESIGN\f[R], then the generated
\f[V].SF\f[R] and signature block files are named \f[V]DUKESIGN.SF\f[R]
and \f[V]DUKESIGN.RSA\f[R], and placed in the \f[V]META-INF\f[R]
directory of the signed JAR file.
.RS
.PP
The characters in the file must come from the set \f[V]a-zA-Z0-9_-\f[R].
Only letters, numbers, underscore, and hyphen characters are allowed.
All lowercase characters are converted to uppercase for the
\f[V].SF\f[R] and signature block file names.
.PP
If no \f[V]-sigfile\f[R] option appears on the command line, then the
base file name for the \f[V].SF\f[R] and signature block files is the
first 8 characters of the alias name specified on the command line, all
converted to upper case.
If the alias name has fewer than 8 characters, then the full alias name
is used.
If the alias name contains any characters that aren\[aq]t valid in a
signature file name, then each such character is converted to an
underscore (_) character to form the file name.
.RE
.TP
\f[V]-signedjar\f[R] \f[I]file\f[R]
Specifies the name of signed JAR file.
.TP
\f[V]-digestalg\f[R] \f[I]algorithm\f[R]
Specifies the name of the message digest algorithm to use when digesting
the entries of a JAR file.
.RS
.PP
For a list of standard message digest algorithm names, see the Java
Security Standard Algorithm Names Specification.
.PP
If this option isn\[aq]t specified, then \f[V]SHA-384\f[R] is used.
There must either be a statically installed provider supplying an
implementation of the specified algorithm or the user must specify one
with the \f[V]-addprovider\f[R] or \f[V]-providerClass\f[R] options;
otherwise, the command will not succeed.
.RE
.TP
\f[V]-sigalg\f[R] \f[I]algorithm\f[R]
Specifies the name of the signature algorithm to use to sign the JAR
file.
.RS
.PP
This algorithm must be compatible with the private key used to sign the
JAR file.
If this option isn\[aq]t specified, then use a default algorithm
matching the private key as described in the \f[B]Supported
Algorithms\f[R] section.
There must either be a statically installed provider supplying an
implementation of the specified algorithm or you must specify one with
the \f[V]-addprovider\f[R] or \f[V]-providerClass\f[R] option;
otherwise, the command doesn\[aq]t succeed.
.PP
For a list of standard signature algorithm names, see the Java Security
Standard Algorithm Names Specification.
.RE
.TP
\f[V]-verify\f[R]
Verifies a signed JAR file.
.TP
\f[V]-verbose\f[R][\f[V]:\f[R]\f[I]suboptions\f[R]]
When the \f[V]-verbose\f[R] option appears on the command line, it
indicates that the \f[V]jarsigner\f[R] use the verbose mode when signing
or verifying with the suboptions determining how much information is
shown.
This causes the , which causes \f[V]jarsigner\f[R] to output extra
information about the progress of the JAR signing or verification.
The \f[I]suboptions\f[R] can be \f[V]all\f[R], \f[V]grouped\f[R], or
\f[V]summary\f[R].
.RS
.PP
If the \f[V]-certs\f[R] option is also specified, then the default mode
(or suboption \f[V]all\f[R]) displays each entry as it is being
processed, and after that, the certificate information for each signer
of the JAR file.
.PP
If the \f[V]-certs\f[R] and the \f[V]-verbose:grouped\f[R] suboptions
are specified, then entries with the same signer info are grouped and
displayed together with their certificate information.
.PP
If \f[V]-certs\f[R] and the \f[V]-verbose:summary\f[R] suboptions are
specified, then entries with the same signer information are grouped and
displayed together with their certificate information.
.PP
Details about each entry are summarized and displayed as \f[I]one entry
(and more)\f[R].
See \f[B]Example of Verifying a Signed JAR File\f[R] and \f[B]Example of
Verification with Certificate Information\f[R].
.RE
.TP
\f[V]-certs\f[R]
If the \f[V]-certs\f[R] option appears on the command line with the
\f[V]-verify\f[R] and \f[V]-verbose\f[R] options, then the output
includes certificate information for each signer of the JAR file.
This information includes the name of the type of certificate (stored in
the signature block file) that certifies the signer\[aq]s public key,
and if the certificate is an X.509 certificate (an instance of the
\f[V]java.security.cert.X509Certificate\f[R]), then the distinguished
name of the signer.
.RS
.PP
The keystore is also examined.
If no keystore value is specified on the command line, then the default
keystore file (if any) is checked.
If the public key certificate for a signer matches an entry in the
keystore, then the alias name for the keystore entry for that signer is
displayed in parentheses.
.RE
.TP
\f[V]-revCheck\f[R]
This option enables revocation checking of certificates when signing or
verifying a JAR file.
The \f[V]jarsigner\f[R] command attempts to make network connections to
fetch OCSP responses and CRLs if the \f[V]-revCheck\f[R] option is
specified on the command line.
Note that revocation checks are not enabled unless this option is
specified.
.TP
\f[V]-tsa\f[R] \f[I]url\f[R]
If \f[V]-tsa http://example.tsa.url\f[R] appears on the command line
when signing a JAR file then a time stamp is generated for the
signature.
The URL, \f[V]http://example.tsa.url\f[R], identifies the location of
the Time Stamping Authority (TSA) and overrides any URL found with the
\f[V]-tsacert\f[R] option.
The \f[V]-tsa\f[R] option doesn\[aq]t require the TSA public key
certificate to be present in the keystore.
.RS
.PP
To generate the time stamp, \f[V]jarsigner\f[R] communicates with the
TSA with the Time-Stamp Protocol (TSP) defined in RFC 3161.
When successful, the time stamp token returned by the TSA is stored with
the signature in the signature block file.
.RE
.TP
\f[V]-tsacert\f[R] \f[I]alias\f[R]
When \f[V]-tsacert\f[R] \f[I]alias\f[R] appears on the command line when
signing a JAR file, a time stamp is generated for the signature.
The alias identifies the TSA public key certificate in the keystore that
is in effect.
The entry\[aq]s certificate is examined for a Subject Information Access
extension that contains a URL identifying the location of the TSA.
.RS
.PP
The TSA public key certificate must be present in the keystore when
using the \f[V]-tsacert\f[R] option.
.RE
.TP
\f[V]-tsapolicyid\f[R] \f[I]policyid\f[R]
Specifies the object identifier (OID) that identifies the policy ID to
be sent to the TSA server.
If this option isn\[aq]t specified, no policy ID is sent and the TSA
server will choose a default policy ID.
.RS
.PP
Object identifiers are defined by X.696, which is an ITU
Telecommunication Standardization Sector (ITU-T) standard.
These identifiers are typically period-separated sets of non-negative
digits like \f[V]1.2.3.4\f[R], for example.
.RE
.TP
\f[V]-tsadigestalg\f[R] \f[I]algorithm\f[R]
Specifies the message digest algorithm that is used to generate the
message imprint to be sent to the TSA server.
If this option isn\[aq]t specified, SHA-384 will be used.
.RS
.PP
See \f[B]Supported Algorithms\f[R].
.PP
For a list of standard message digest algorithm names, see the Java
Security Standard Algorithm Names Specification.
.RE
.TP
\f[V]-internalsf\f[R]
In the past, the signature block file generated when a JAR file was
signed included a complete encoded copy of the \f[V].SF\f[R] file
(signature file) also generated.
This behavior has been changed.
To reduce the overall size of the output JAR file, the signature block
file by default doesn\[aq]t contain a copy of the \f[V].SF\f[R] file
anymore.
If \f[V]-internalsf\f[R] appears on the command line, then the old
behavior is utilized.
This option is useful for testing.
In practice, don\[aq]t use the \f[V]-internalsf\f[R] option because it
incurs higher overhead.
.TP
\f[V]-sectionsonly\f[R]
If the \f[V]-sectionsonly\f[R] option appears on the command line, then
the \f[V].SF\f[R] file (signature file) generated when a JAR file is
signed doesn\[aq]t include a header that contains a hash of the whole
manifest file.
It contains only the information and hashes related to each individual
source file included in the JAR file.
See Signature File.
.RS
.PP
By default, this header is added, as an optimization.
When the header is present, whenever the JAR file is verified, the
verification can first check to see whether the hash in the header
matches the hash of the whole manifest file.
When there is a match, verification proceeds to the next step.
When there is no match, it is necessary to do a less optimized
verification that the hash in each source file information section in
the \f[V].SF\f[R] file equals the hash of its corresponding section in
the manifest file.
See \f[B]JAR File Verification\f[R].
.PP
The \f[V]-sectionsonly\f[R] option is primarily used for testing.
It shouldn\[aq]t be used other than for testing because using it incurs
higher overhead.
.RE
.TP
\f[V]-protected\f[R]
Values can be either \f[V]true\f[R] or \f[V]false\f[R].
Specify \f[V]true\f[R] when a password must be specified through a
protected authentication path such as a dedicated PIN reader.
.TP
\f[V]-providerName\f[R] \f[I]providerName\f[R]
If more than one provider was configured in the \f[V]java.security\f[R]
security properties file, then you can use the \f[V]-providerName\f[R]
option to target a specific provider instance.
The argument to this option is the name of the provider.
.RS
.PP
For the Oracle PKCS #11 provider, \f[I]providerName\f[R] is of the form
\f[V]SunPKCS11-\f[R]\f[I]TokenName\f[R], where \f[I]TokenName\f[R] is
the name suffix that the provider instance has been configured with, as
detailed in the configuration attributes table.
For example, the following command lists the contents of the
\f[V]PKCS #11\f[R] keystore provider instance with name suffix
\f[V]SmartCard\f[R]:
.RS
.PP
\f[V]jarsigner -keystore NONE -storetype PKCS11 -providerName SunPKCS11-SmartCard -list\f[R]
.RE
.RE
.TP
\f[V]-addprovider\f[R] \f[I]name\f[R] [\f[V]-providerArg\f[R] \f[I]arg\f[R]]
Adds a security provider by name (such as SunPKCS11) and an optional
configure argument.
The value of the security provider is the name of a security provider
that is defined in a module.
.RS
.PP
Used with the \f[V]-providerArg ConfigFilePath\f[R] option, the
\f[V]keytool\f[R] and \f[V]jarsigner\f[R] tools install the provider
dynamically and use \f[V]ConfigFilePath\f[R] for the path to the token
configuration file.
The following example shows a command to list a \f[V]PKCS #11\f[R]
keystore when the Oracle PKCS #11 provider wasn\[aq]t configured in the
security properties file.
.RS
.PP
\f[V]jarsigner -keystore NONE -storetype PKCS11 -addprovider SunPKCS11 -providerArg /mydir1/mydir2/token.config\f[R]
.RE
.RE
.TP
\f[V]-providerClass\f[R] \f[I]provider-class-name\f[R] [\f[V]-providerArg\f[R] \f[I]arg\f[R]]
Used to specify the name of cryptographic service provider\[aq]s master
class file when the service provider isn\[aq]t listed in the
\f[V]java.security\f[R] security properties file.
Adds a security provider by fully-qualified class name and an optional
configure argument.
.RS
.PP
\f[B]Note:\f[R]
.PP
The preferred way to load PKCS11 is by using modules.
See \f[V]-addprovider\f[R].
.RE
.TP
\f[V]-providerPath\f[R] \f[I]classpath\f[R]
Used to specify the classpath for providers specified by the
\f[V]-providerClass\f[R] option.
Multiple paths should be separated by the system-dependent
path-separator character.
.TP
\f[V]-J\f[R]\f[I]javaoption\f[R]
Passes through the specified \f[I]javaoption\f[R] string directly to the
Java interpreter.
The \f[V]jarsigner\f[R] command is a wrapper around the interpreter.
This option shouldn\[aq]t contain any spaces.
It is useful for adjusting the execution environment or memory usage.
For a list of possible interpreter options, type \f[V]java -h\f[R] or
\f[V]java -X\f[R] at the command line.
.TP
\f[V]-strict\f[R]
During the signing or verifying process, the command may issue warning
messages.
If you specify this option, the exit code of the tool reflects the
severe warning messages that this command found.
See \f[B]Errors and Warnings\f[R].
.TP
\f[V]-conf\f[R] \f[I]url\f[R]
Specifies a pre-configured options file.
Read the \f[B]keytool documentation\f[R] for details.
The property keys supported are \[dq]jarsigner.all\[dq] for all actions,
\[dq]jarsigner.sign\[dq] for signing, and \[dq]jarsigner.verify\[dq] for
verification.
\f[V]jarsigner\f[R] arguments including the JAR file name and alias
name(s) cannot be set in this file.
.TP
\f[V]-version\f[R]
Prints the program version.
.SH ERRORS AND WARNINGS
.PP
During the signing or verifying process, the \f[V]jarsigner\f[R] command
may issue various errors or warnings.
.PP
If there is a failure, the \f[V]jarsigner\f[R] command exits with code
1.
If there is no failure, but there are one or more severe warnings, the
\f[V]jarsigner\f[R] command exits with code 0 when the \f[V]-strict\f[R]
option is \f[B]not\f[R] specified, or exits with the OR-value of the
warning codes when the \f[V]-strict\f[R] is specified.
If there is only informational warnings or no warning at all, the
command always exits with code 0.
.PP
For example, if a certificate used to sign an entry is expired and has a
KeyUsage extension that doesn\[aq]t allow it to sign a file, the
\f[V]jarsigner\f[R] command exits with code 12 (=4+8) when the
\f[V]-strict\f[R] option is specified.
.PP
\f[B]Note:\f[R] Exit codes are reused because only the values from 0 to
255 are legal on Linux and macOS.
.PP
The following sections describes the names, codes, and descriptions of
the errors and warnings that the \f[V]jarsigner\f[R] command can issue.
.SH FAILURE
.PP
Reasons why the \f[V]jarsigner\f[R] command fails include (but
aren\[aq]t limited to) a command line parsing error, the inability to
find a keypair to sign the JAR file, or the verification of a signed JAR
fails.
.TP
failure
Code 1.
The signing or verifying fails.
.SH SEVERE WARNINGS
.PP
\f[B]Note:\f[R]
.PP
Severe warnings are reported as errors if you specify the
\f[V]-strict\f[R] option.
.PP
Reasons why the \f[V]jarsigner\f[R] command issues a severe warning
include the certificate used to sign the JAR file has an error or the
signed JAR file has other problems.
.TP
hasExpiredCert
Code 4.
This JAR contains entries whose signer certificate has expired.
.TP
hasExpiredTsaCert
Code 4.
The timestamp has expired.
.TP
notYetValidCert
Code 4.
This JAR contains entries whose signer certificate isn\[aq]t yet valid.
.TP
chainNotValidated
Code 4.
This JAR contains entries whose certificate chain isn\[aq]t validated.
.TP
tsaChainNotValidated
Code 64.
The timestamp is invalid.
.TP
signerSelfSigned
Code 4.
This JAR contains entries whose signer certificate is self signed.
.TP
disabledAlg
Code 4.
An algorithm used is considered a security risk and is disabled.
.TP
badKeyUsage
Code 8.
This JAR contains entries whose signer certificate\[aq]s KeyUsage
extension doesn\[aq]t allow code signing.
.TP
badExtendedKeyUsage
Code 8.
This JAR contains entries whose signer certificate\[aq]s
ExtendedKeyUsage extension doesn\[aq]t allow code signing.
.TP
badNetscapeCertType
Code 8.
This JAR contains entries whose signer certificate\[aq]s
NetscapeCertType extension doesn\[aq]t allow code signing.
.TP
hasUnsignedEntry
Code 16.
This JAR contains unsigned entries which haven\[aq]t been
integrity-checked.
.TP
notSignedByAlias
Code 32.
This JAR contains signed entries which aren\[aq]t signed by the
specified alias(es).
.TP
aliasNotInStore
Code 32.
This JAR contains signed entries that aren\[aq]t signed by alias in this
keystore.
.TP
tsaChainNotValidated
Code 64.
This JAR contains entries whose TSA certificate chain is invalid.
.SH INFORMATIONAL WARNINGS
.PP
Informational warnings include those that aren\[aq]t errors but regarded
as bad practice.
They don\[aq]t have a code.
.TP
extraAttributesDetected
The POSIX file permissions and/or symlink attributes are detected during
signing or verifying a JAR file.
The \f[V]jarsigner\f[R] tool preserves these attributes in the newly
signed file but warns that these attributes are unsigned and not
protected by the signature.
.TP
hasExpiringCert
This JAR contains entries whose signer certificate expires within six
months.
.TP
hasExpiringTsaCert
The timestamp will expire within one year on \f[V]YYYY-MM-DD\f[R].
.TP
hasNonexistentEntries
This JAR contains signed entries for files that do not exist.
.TP
legacyAlg
An algorithm used is considered a security risk but not disabled.
.TP
noTimestamp
This JAR contains signatures that doesn\[aq]t include a timestamp.
Without a timestamp, users may not be able to validate this JAR file
after the signer certificate\[aq]s expiration date
(\f[V]YYYY-MM-DD\f[R]) or after any future revocation date.
.SH EXAMPLE OF SIGNING A JAR FILE
.PP
Use the following command to sign \f[V]bundle.jar\f[R] with the private
key of a user whose keystore alias is \f[V]jane\f[R] in a keystore named
\f[V]mystore\f[R] in the \f[V]working\f[R] directory and name the signed
JAR file \f[V]sbundle.jar\f[R]:
.RS
.PP
\f[V]jarsigner -keystore /working/mystore -storepass\f[R]
\f[I]keystore_password\f[R] \f[V]-keypass\f[R]
\f[I]private_key_password\f[R]
\f[V]-signedjar sbundle.jar bundle.jar jane\f[R]
.RE
.PP
There is no \f[V]-sigfile\f[R] specified in the previous command so the
generated \f[V].SF\f[R] and signature block files to be placed in the
signed JAR file have default names based on the alias name.
They are named \f[V]JANE.SF\f[R] and \f[V]JANE.RSA\f[R].
.PP
If you want to be prompted for the store password and the private key
password, then you could shorten the previous command to the following:
.RS
.PP
\f[V]jarsigner -keystore /working/mystore -signedjar sbundle.jar bundle.jar jane\f[R]
.RE
.PP
If the \f[V]keystore\f[R] is the default \f[V]keystore\f[R]
(\f[V].keystore\f[R] in your home directory), then you don\[aq]t need to
specify a \f[V]keystore\f[R], as follows:
.RS
.PP
\f[V]jarsigner -signedjar sbundle.jar bundle.jar jane\f[R]
.RE
.PP
If you want the signed JAR file to overwrite the input JAR file
(\f[V]bundle.jar\f[R]), then you don\[aq]t need to specify a
\f[V]-signedjar\f[R] option, as follows:
.RS
.PP
\f[V]jarsigner bundle.jar jane\f[R]
.RE
.SH EXAMPLE OF VERIFYING A SIGNED JAR FILE
.PP
To verify a signed JAR file to ensure that the signature is valid and
the JAR file wasn\[aq]t been tampered with, use a command such as the
following:
.RS
.PP
\f[V]jarsigner -verify ButtonDemo.jar\f[R]
.RE
.PP
When the verification is successful, \f[V]jar verified\f[R] is
displayed.
Otherwise, an error message is displayed.
You can get more information when you use the \f[V]-verbose\f[R] option.
A sample use of \f[V]jarsigner\f[R] with the \f[V]-verbose\f[R] option
follows:
.IP
.nf
\f[CB]
jarsigner -verify -verbose ButtonDemo.jar

s       866 Tue Sep 12 20:08:48 EDT 2017 META-INF/MANIFEST.MF
        825 Tue Sep 12 20:08:48 EDT 2017 META-INF/ORACLE_C.SF
       7475 Tue Sep 12 20:08:48 EDT 2017 META-INF/ORACLE_C.RSA
          0 Tue Sep 12 20:07:54 EDT 2017 META-INF/
          0 Tue Sep 12 20:07:16 EDT 2017 components/
          0 Tue Sep 12 20:07:16 EDT 2017 components/images/
sm      523 Tue Sep 12 20:07:16 EDT 2017 components/ButtonDemo$1.class
sm     3440 Tue Sep 12 20:07:16 EDT 2017 components/ButtonDemo.class
sm     2346 Tue Sep 12 20:07:16 EDT 2017 components/ButtonDemo.jnlp
sm      172 Tue Sep 12 20:07:16 EDT 2017 components/images/left.gif
sm      235 Tue Sep 12 20:07:16 EDT 2017 components/images/middle.gif
sm      172 Tue Sep 12 20:07:16 EDT 2017 components/images/right.gif

  s = signature was verified
  m = entry is listed in manifest
  k = at least one certificate was found in keystore

- Signed by \[dq]CN=\[dq]Oracle America, Inc.\[dq], OU=Software Engineering, O=\[dq]Oracle America, Inc.\[dq], L=Redwood City, ST=California, C=US\[dq]
    Digest algorithm: SHA-256
    Signature algorithm: SHA256withRSA, 2048-bit key
  Timestamped by \[dq]CN=Symantec Time Stamping Services Signer - G4, O=Symantec Corporation, C=US\[dq] on Tue Sep 12 20:08:49 UTC 2017
    Timestamp digest algorithm: SHA-1
    Timestamp signature algorithm: SHA1withRSA, 2048-bit key

jar verified.

The signer certificate expired on 2018-02-01. However, the JAR will be valid until the timestamp expires on 2020-12-29.
\f[R]
.fi
.SH EXAMPLE OF VERIFICATION WITH CERTIFICATE INFORMATION
.PP
If you specify the \f[V]-certs\f[R] option with the \f[V]-verify\f[R]
and \f[V]-verbose\f[R] options, then the output includes certificate
information for each signer of the JAR file.
The information includes the certificate type, the signer distinguished
name information (when it is an X.509 certificate), and in parentheses,
the keystore alias for the signer when the public key certificate in the
JAR file matches the one in a keystore entry, for example:
.IP
.nf
\f[CB]
jarsigner -keystore $JAVA_HOME/lib/security/cacerts -verify -verbose -certs ButtonDemo.jar

s k     866 Tue Sep 12 20:08:48 EDT 2017 META-INF/MANIFEST.MF

      >>> Signer
      X.509, CN=\[dq]Oracle America, Inc.\[dq], OU=Software Engineering, O=\[dq]Oracle America, Inc.\[dq], L=Redwood City, ST=California, C=US
      [certificate is valid from 2017-01-30, 7:00 PM to 2018-02-01, 6:59 PM]
      X.509, CN=Symantec Class 3 SHA256 Code Signing CA, OU=Symantec Trust Network, O=Symantec Corporation, C=US
      [certificate is valid from 2013-12-09, 7:00 PM to 2023-12-09, 6:59 PM]
      X.509, CN=VeriSign Class 3 Public Primary Certification Authority - G5, OU=\[dq](c) 2006 VeriSign, Inc. - For authorized use only\[dq], OU=VeriSign Trust Network, O=\[dq]VeriSign, Inc.\[dq], C=US (verisignclass3g5ca [jdk])
      [trusted certificate]
      >>> TSA
      X.509, CN=Symantec Time Stamping Services Signer - G4, O=Symantec Corporation, C=US
      [certificate is valid from 2012-10-17, 8:00 PM to 2020-12-29, 6:59 PM]
      X.509, CN=Symantec Time Stamping Services CA - G2, O=Symantec Corporation, C=US
      [certificate is valid from 2012-12-20, 7:00 PM to 2020-12-30, 6:59 PM]

        825 Tue Sep 12 20:08:48 EDT 2017 META-INF/ORACLE_C.SF
       7475 Tue Sep 12 20:08:48 EDT 2017 META-INF/ORACLE_C.RSA
          0 Tue Sep 12 20:07:54 EDT 2017 META-INF/
          0 Tue Sep 12 20:07:16 EDT 2017 components/
          0 Tue Sep 12 20:07:16 EDT 2017 components/images/
smk     523 Tue Sep 12 20:07:16 EDT 2017 components/ButtonDemo$1.class

      [entry was signed on 2017-09-12, 4:08 PM]
      >>> Signer
      X.509, CN=\[dq]Oracle America, Inc.\[dq], OU=Software Engineering, O=\[dq]Oracle America, Inc.\[dq], L=Redwood City, ST=California, C=US
      [certificate is valid from 2017-01-30, 7:00 PM to 2018-02-01, 6:59 PM]
      X.509, CN=Symantec Class 3 SHA256 Code Signing CA, OU=Symantec Trust Network, O=Symantec Corporation, C=US
      [certificate is valid from 2013-12-09, 7:00 PM to 2023-12-09, 6:59 PM]
      X.509, CN=VeriSign Class 3 Public Primary Certification Authority - G5, OU=\[dq](c) 2006 VeriSign, Inc. - For authorized use only\[dq], OU=VeriSign Trust Network, O=\[dq]VeriSign, Inc.\[dq], C=US (verisignclass3g5ca [jdk])
      [trusted certificate]
      >>> TSA
      X.509, CN=Symantec Time Stamping Services Signer - G4, O=Symantec Corporation, C=US
      [certificate is valid from 2012-10-17, 8:00 PM to 2020-12-29, 6:59 PM]
      X.509, CN=Symantec Time Stamping Services CA - G2, O=Symantec Corporation, C=US
      [certificate is valid from 2012-12-20, 7:00 PM to 2020-12-30, 6:59 PM]

smk    3440 Tue Sep 12 20:07:16 EDT 2017 components/ButtonDemo.class
\&...
smk    2346 Tue Sep 12 20:07:16 EDT 2017 components/ButtonDemo.jnlp
\&...
smk     172 Tue Sep 12 20:07:16 EDT 2017 components/images/left.gif
\&...
smk     235 Tue Sep 12 20:07:16 EDT 2017 components/images/middle.gif
\&...
smk     172 Tue Sep 12 20:07:16 EDT 2017 components/images/right.gif
\&...

  s = signature was verified
  m = entry is listed in manifest
  k = at least one certificate was found in keystore

- Signed by \[dq]CN=\[dq]Oracle America, Inc.\[dq], OU=Software Engineering, O=\[dq]Oracle America, Inc.\[dq], L=Redwood City, ST=California, C=US\[dq]
    Digest algorithm: SHA-256
    Signature algorithm: SHA256withRSA, 2048-bit key
  Timestamped by \[dq]CN=Symantec Time Stamping Services Signer - G4, O=Symantec Corporation, C=US\[dq] on Tue Sep 12 20:08:49 UTC 2017
    Timestamp digest algorithm: SHA-1
    Timestamp signature algorithm: SHA1withRSA, 2048-bit key

jar verified.

The signer certificate expired on 2018-02-01. However, the JAR will be valid until the timestamp expires on 2020-12-29.
\f[R]
.fi
.PP
If the certificate for a signer isn\[aq]t an X.509 certificate, then
there is no distinguished name information.
In that case, just the certificate type and the alias are shown.
For example, if the certificate is a PGP certificate, and the alias is
\f[V]bob\f[R], then you would get: \f[V]PGP, (bob)\f[R].
