import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'deepseek_api_service.dart';
import 'counselor_personalities.dart';
import 'safety_guidelines.dart';
import 'safety_service.dart';

class ChatMessage {
  final String id;
  final String content;
  final bool isUser;
  final DateTime timestamp;

  ChatMessage({
    required this.id,
    required this.content,
    required this.isUser,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'content': content,
    'isUser': isUser,
    'timestamp': timestamp.millisecondsSinceEpoch,
  };

  factory ChatMessage.fromJson(Map<String, dynamic> json) => ChatMessage(
    id: json['id'],
    content: json['content'],
    isUser: json['isUser'],
    timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp']),
  );
}

class SessionProgress {
  final List<String> keyInsights;
  final List<String> goals;
  final List<String> achievements;
  final Map<String, dynamic> moodTracking;
  final DateTime lastUpdated;

  SessionProgress({
    required this.keyInsights,
    required this.goals,
    required this.achievements,
    required this.moodTracking,
    required this.lastUpdated,
  });

  Map<String, dynamic> toJson() => {
    'keyInsights': keyInsights,
    'goals': goals,
    'achievements': achievements,
    'moodTracking': moodTracking,
    'lastUpdated': lastUpdated.millisecondsSinceEpoch,
  };

  factory SessionProgress.fromJson(Map<String, dynamic> json) =>
      SessionProgress(
        keyInsights: List<String>.from(json['keyInsights'] ?? []),
        goals: List<String>.from(json['goals'] ?? []),
        achievements: List<String>.from(json['achievements'] ?? []),
        moodTracking: Map<String, dynamic>.from(json['moodTracking'] ?? {}),
        lastUpdated: DateTime.fromMillisecondsSinceEpoch(json['lastUpdated']),
      );
}

enum SessionStatus { active, completed, paused }

class CounselorSession {
  final String id;
  final String counselorName;
  final String counselorImage;
  final String counselorImagePath;
  final int counselorColor; // Store as int to avoid Color import issues
  final DateTime createdAt;
  final DateTime lastActive;
  final List<ChatMessage> messages;
  final SessionProgress progress;
  final SessionStatus status;
  final String? customTitle;
  final DateTime? completedAt;
  final List<String>? journalingPrompts;

  CounselorSession({
    required this.id,
    required this.counselorName,
    required this.counselorImage,
    required this.counselorImagePath,
    required this.counselorColor,
    required this.createdAt,
    required this.lastActive,
    required this.messages,
    required this.progress,
    this.status = SessionStatus.active,
    this.customTitle,
    this.completedAt,
    this.journalingPrompts,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'counselorName': counselorName,
    'counselorImage': counselorImage,
    'counselorImagePath': counselorImagePath,
    'counselorColor': counselorColor,
    'createdAt': createdAt.millisecondsSinceEpoch,
    'lastActive': lastActive.millisecondsSinceEpoch,
    'messages': messages.map((m) => m.toJson()).toList(),
    'progress': progress.toJson(),
    'status': status.toString(),
    'customTitle': customTitle,
    'completedAt': completedAt?.millisecondsSinceEpoch,
    'journalingPrompts': journalingPrompts,
  };

  factory CounselorSession.fromJson(Map<String, dynamic> json) =>
      CounselorSession(
        id: json['id'],
        counselorName: json['counselorName'],
        counselorImage: json['counselorImage'],
        counselorImagePath: json['counselorImagePath'] ?? '',
        counselorColor: json['counselorColor'],
        createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
        lastActive: DateTime.fromMillisecondsSinceEpoch(json['lastActive']),
        messages:
            (json['messages'] as List)
                .map((m) => ChatMessage.fromJson(m))
                .toList(),
        progress: SessionProgress.fromJson(json['progress']),
        status:
            json['status'] != null
                ? SessionStatus.values.firstWhere(
                  (e) => e.toString() == json['status'],
                  orElse: () => SessionStatus.active,
                )
                : SessionStatus.active,
        customTitle: json['customTitle'],
        completedAt:
            json['completedAt'] != null
                ? DateTime.fromMillisecondsSinceEpoch(json['completedAt'])
                : null,
        journalingPrompts:
            json['journalingPrompts'] != null
                ? List<String>.from(json['journalingPrompts'])
                : null,
      );

  String get lastMessage =>
      messages.isNotEmpty ? messages.last.content : 'Session started';
  int get messageCount => messages.length;

  /// Get the display title for this session
  String get displayTitle {
    if (customTitle != null && customTitle!.isNotEmpty) {
      return customTitle!;
    }

    // Generate title from first user message or session content
    final userMessages = messages.where((m) => m.isUser).toList();
    if (userMessages.isNotEmpty) {
      final firstMessage = userMessages.first.content;
      if (firstMessage.length > 50) {
        return '${firstMessage.substring(0, 47)}...';
      }
      return firstMessage;
    }

    return 'Session with $counselorName';
  }

  /// Check if session is completed (reached message limit or explicitly completed)
  bool get isCompleted =>
      status == SessionStatus.completed ||
      messages.length >= SessionService.maxMessagesPerSession;

  /// Check if session can be continued today
  bool get canContinueToday {
    if (status != SessionStatus.completed) return true;

    final now = DateTime.now();
    final completionDate = completedAt ?? lastActive;

    // Can continue if completed on a different day
    return completionDate.day != now.day ||
        completionDate.month != now.month ||
        completionDate.year != now.year;
  }

  /// Check if session has reached the 10-question limit for completion
  bool get hasReachedQuestionLimit {
    // Count counselor responses (questions)
    final counselorMessages = messages.where((m) => !m.isUser).toList();
    return counselorMessages.length >= 10;
  }

  /// Create a copy of this session with updated fields
  CounselorSession copyWith({
    String? id,
    String? counselorName,
    String? counselorImage,
    String? counselorImagePath,
    int? counselorColor,
    DateTime? createdAt,
    DateTime? lastActive,
    List<ChatMessage>? messages,
    SessionProgress? progress,
    SessionStatus? status,
    String? customTitle,
    DateTime? completedAt,
    List<String>? journalingPrompts,
  }) {
    return CounselorSession(
      id: id ?? this.id,
      counselorName: counselorName ?? this.counselorName,
      counselorImage: counselorImage ?? this.counselorImage,
      counselorImagePath: counselorImagePath ?? this.counselorImagePath,
      counselorColor: counselorColor ?? this.counselorColor,
      createdAt: createdAt ?? this.createdAt,
      lastActive: lastActive ?? this.lastActive,
      messages: messages ?? this.messages,
      progress: progress ?? this.progress,
      status: status ?? this.status,
      customTitle: customTitle ?? this.customTitle,
      completedAt: completedAt ?? this.completedAt,
      journalingPrompts: journalingPrompts ?? this.journalingPrompts,
    );
  }
}

class SessionService {
  static const String _sessionsKey = 'counselor_sessions_v2';
  static const String _dailyUsageKey = 'daily_usage';
  static const Uuid _uuid = Uuid();
  static const int maxSessionsPerDay = 3;
  static const int maxMessagesPerSession = 50; // Roughly 45-60 minutes

  /// Create a new counselor session
  static Future<CounselorSession> createSession({
    required String counselorName,
    required String counselorImage,
    required String counselorImagePath,
    required int counselorColor,
  }) async {
    // Check daily limits
    final exceedsLimit = await SafetyService.checkDailyLimits();
    if (exceedsLimit) {
      throw Exception(
        'Daily session limit reached. Please try again tomorrow or contact a licensed professional for immediate support.',
      );
    }

    final sessionId = _uuid.v4();
    final session = CounselorSession(
      id: sessionId,
      counselorName: counselorName,
      counselorImage: counselorImage,
      counselorImagePath: counselorImagePath,
      counselorColor: counselorColor,
      createdAt: DateTime.now(),
      lastActive: DateTime.now(),
      messages: [],
      progress: SessionProgress(
        keyInsights: [],
        goals: [],
        achievements: [],
        moodTracking: {},
        lastUpdated: DateTime.now(),
      ),
    );

    // Record session start for daily limits
    await SafetyService.recordSessionStart(sessionId);

    // Add welcome message from counselor
    final welcomeMessage = await _generateWelcomeMessage(counselorName);
    final updatedSession = await addMessage(session, welcomeMessage, false);

    await _saveSession(updatedSession);
    return updatedSession;
  }

  /// Add a message to a session and get AI response
  static Future<CounselorSession> addMessage(
    CounselorSession session,
    String content,
    bool isUser,
  ) async {
    // Check session limits
    if (session.messages.length >= maxMessagesPerSession) {
      final limitMessage = ChatMessage(
        id: _uuid.v4(),
        content:
            '''Our session has reached its recommended length for today. This helps ensure quality care and gives you time to process what we've discussed.

I encourage you to:
• Reflect on our conversation
• Practice any coping strategies we discussed
• Consider scheduling with a human therapist for ongoing support

You can start a new session tomorrow, or reach out to a licensed professional if you need immediate support.

Crisis resources are always available:
• 988 Suicide & Crisis Lifeline: Call or text 988
• Crisis Text Line: Text HOME to 741741''',
        isUser: false,
        timestamp: DateTime.now(),
      );

      final updatedSession = CounselorSession(
        id: session.id,
        counselorName: session.counselorName,
        counselorImage: session.counselorImage,
        counselorImagePath: session.counselorImagePath,
        counselorColor: session.counselorColor,
        createdAt: session.createdAt,
        lastActive: DateTime.now(),
        messages: [...session.messages, limitMessage],
        progress: session.progress,
      );

      await _saveSession(updatedSession);
      return updatedSession;
    }

    // Safety check for user messages
    if (isUser) {
      // Check for crisis indicators
      if (SafetyGuidelines.isCrisisMessage(content)) {
        final crisisResponse = SafetyGuidelines.getCrisisResponse(content);

        // Log safety incident
        await SafetyService.logSafetyIncident(
          sessionId: session.id,
          counselorName: session.counselorName,
          userMessage: content,
          incidentType: 'crisis_message',
          response: crisisResponse,
        );

        final crisisMessage = ChatMessage(
          id: _uuid.v4(),
          content: crisisResponse,
          isUser: false,
          timestamp: DateTime.now(),
        );

        final updatedSession = CounselorSession(
          id: session.id,
          counselorName: session.counselorName,
          counselorImage: session.counselorImage,
          counselorImagePath: session.counselorImagePath,
          counselorColor: session.counselorColor,
          createdAt: session.createdAt,
          lastActive: DateTime.now(),
          messages: [
            ...session.messages,
            ChatMessage(
              id: _uuid.v4(),
              content: content,
              isUser: true,
              timestamp: DateTime.now(),
            ),
            crisisMessage,
          ],
          progress: session.progress,
        );

        await _saveSession(updatedSession);
        return updatedSession;
      }

      // Check for prohibited content
      if (SafetyGuidelines.containsProhibitedContent(content)) {
        final prohibitedResponse =
            SafetyGuidelines.getProhibitedTechniqueResponse('this technique');

        // Log safety incident
        await SafetyService.logSafetyIncident(
          sessionId: session.id,
          counselorName: session.counselorName,
          userMessage: content,
          incidentType: 'prohibited_content',
          response: prohibitedResponse,
        );

        final safetyMessage = ChatMessage(
          id: _uuid.v4(),
          content: prohibitedResponse,
          isUser: false,
          timestamp: DateTime.now(),
        );

        final updatedSession = CounselorSession(
          id: session.id,
          counselorName: session.counselorName,
          counselorImage: session.counselorImage,
          counselorImagePath: session.counselorImagePath,
          counselorColor: session.counselorColor,
          createdAt: session.createdAt,
          lastActive: DateTime.now(),
          messages: [
            ...session.messages,
            ChatMessage(
              id: _uuid.v4(),
              content: content,
              isUser: true,
              timestamp: DateTime.now(),
            ),
            safetyMessage,
          ],
          progress: session.progress,
        );

        await _saveSession(updatedSession);
        return updatedSession;
      }
    }

    final message = ChatMessage(
      id: _uuid.v4(),
      content: content,
      isUser: isUser,
      timestamp: DateTime.now(),
    );

    final updatedMessages = [...session.messages, message];

    CounselorSession updatedSession = CounselorSession(
      id: session.id,
      counselorName: session.counselorName,
      counselorImage: session.counselorImage,
      counselorImagePath: session.counselorImagePath,
      counselorColor: session.counselorColor,
      createdAt: session.createdAt,
      lastActive: DateTime.now(),
      messages: updatedMessages,
      progress: session.progress,
    );

    // If user message, generate AI response
    if (isUser) {
      final aiResponse = await _generateAIResponse(updatedSession);
      final aiMessage = ChatMessage(
        id: _uuid.v4(),
        content: aiResponse,
        isUser: false,
        timestamp: DateTime.now(),
      );

      updatedSession = CounselorSession(
        id: session.id,
        counselorName: session.counselorName,
        counselorImage: session.counselorImage,
        counselorImagePath: session.counselorImagePath,
        counselorColor: session.counselorColor,
        createdAt: session.createdAt,
        lastActive: DateTime.now(),
        messages: [...updatedMessages, aiMessage],
        progress: session.progress,
      );
    }

    await _saveSession(updatedSession);
    return updatedSession;
  }

  /// Generate welcome message for a counselor
  static Future<String> _generateWelcomeMessage(String counselorName) async {
    final personality = CounselorPersonalities.getPersonality(counselorName);

    try {
      final messages = [
        DeepSeekApiService.createSystemMessage(personality),
        DeepSeekApiService.createUserMessage(
          'Please provide a warm, welcoming first message to start our therapy session. Keep it brief (2-3 sentences) and true to your personality. This is the very first interaction.',
        ),
      ];

      return await DeepSeekApiService.sendMessage(messages: messages);
    } catch (e) {
      // Fallback welcome messages
      switch (counselorName) {
        case 'Dr. Sage':
          return "Welcome. I'm Dr. Sage, and I'm here to walk alongside you on your journey of self-discovery. Take a moment to breathe, and when you're ready, share what's on your mind.";
        case 'Luna':
          return "Hello, beautiful soul. I'm Luna, and this is your safe space to be exactly who you are. What would you like to explore together today?";
        case 'Kai':
          return "Hey there! I'm Kai, and I'm excited to help you take action toward your goals. What's one thing you'd like to work on or change in your life?";
        case 'Theo':
          return "Hello. I'm Theo, and I'm here to help you think through whatever is on your mind with clarity and logic. What situation would you like to examine together?";
        case 'Dr. Elena':
          return "Hi, I'm Dr. Elena. I'm here to provide you with evidence-based support and practical tools. What challenge are you facing that we can work on together?";
        case 'Zuri':
          return "Welcome, friend. I'm Zuri, and I want you to know that all of who you are is welcome here. What's been on your heart lately?";
        default:
          return "Hello! I'm here to support you on your journey. What would you like to talk about today?";
      }
    }
  }

  /// Generate AI response based on session context
  static Future<String> _generateAIResponse(CounselorSession session) async {
    final personality = CounselorPersonalities.getPersonality(
      session.counselorName,
    );

    // Add boundary reminder every 3-4 exchanges
    final userMessageCount = session.messages.where((m) => m.isUser).length;
    String boundaryReminder = '';
    if (userMessageCount % 4 == 0 && userMessageCount > 0) {
      boundaryReminder =
          '\n\nReminder: I\'m an AI counselor providing supportive conversation, not a replacement for professional mental health treatment. For serious concerns, please consider connecting with a licensed therapist.';
    }

    // Build conversation context (last 10 messages to manage token usage)
    final recentMessages =
        session.messages.length > 10
            ? session.messages.sublist(session.messages.length - 10)
            : session.messages;

    final conversationHistory =
        recentMessages.map((msg) {
          return msg.isUser
              ? DeepSeekApiService.createUserMessage(msg.content)
              : DeepSeekApiService.createAssistantMessage(msg.content);
        }).toList();

    try {
      final messages = [
        DeepSeekApiService.createSystemMessage(personality),
        ...conversationHistory,
      ];

      final response = await DeepSeekApiService.sendMessage(
        messages: messages,
        temperature: 0.8, // Slightly higher for more personality
        maxTokens: 800,
      );

      return response + boundaryReminder;
    } catch (e) {
      return "I apologize, but I'm having trouble connecting right now. Please try again in a moment, and I'll be here to support you.";
    }
  }

  /// Save session to local storage
  static Future<void> _saveSession(CounselorSession session) async {
    final prefs = await SharedPreferences.getInstance();
    final sessions = await getAllSessions();

    // Update existing session or add new one
    final index = sessions.indexWhere((s) => s.id == session.id);
    if (index >= 0) {
      sessions[index] = session;
    } else {
      sessions.add(session);
    }

    // Keep only the 5 most recent sessions
    sessions.sort((a, b) => b.lastActive.compareTo(a.lastActive));
    final sessionsToKeep = sessions.take(5).toList();

    final sessionsJson =
        sessionsToKeep.map((s) => json.encode(s.toJson())).toList();
    await prefs.setStringList(_sessionsKey, sessionsJson);
  }

  /// Get all saved sessions
  static Future<List<CounselorSession>> getAllSessions() async {
    final prefs = await SharedPreferences.getInstance();
    final sessionsJson = prefs.getStringList(_sessionsKey) ?? [];

    return sessionsJson
        .map((sessionStr) => CounselorSession.fromJson(json.decode(sessionStr)))
        .toList();
  }

  /// Get a specific session by ID
  static Future<CounselorSession?> getSession(String sessionId) async {
    final sessions = await getAllSessions();
    try {
      return sessions.firstWhere((s) => s.id == sessionId);
    } catch (e) {
      return null;
    }
  }

  /// Delete a session
  static Future<void> deleteSession(String sessionId) async {
    final prefs = await SharedPreferences.getInstance();
    final sessions = await getAllSessions();
    sessions.removeWhere((s) => s.id == sessionId);

    final sessionsJson = sessions.map((s) => json.encode(s.toJson())).toList();
    await prefs.setStringList(_sessionsKey, sessionsJson);
  }

  /// Complete a session and generate journaling prompts
  static Future<CounselorSession> completeSession(
    CounselorSession session,
    List<String> journalingPrompts,
  ) async {
    final completedSession = session.copyWith(
      status: SessionStatus.completed,
      completedAt: DateTime.now(),
      journalingPrompts: journalingPrompts,
      lastActive: DateTime.now(),
    );

    await _saveSession(completedSession);
    return completedSession;
  }

  /// Rename a session
  static Future<CounselorSession> renameSession(
    String sessionId,
    String newTitle,
  ) async {
    final session = await getSession(sessionId);
    if (session == null) {
      throw Exception('Session not found');
    }

    final renamedSession = session.copyWith(
      customTitle: newTitle,
      lastActive: DateTime.now(),
    );

    await _saveSession(renamedSession);
    return renamedSession;
  }

  /// Pause a session (save and exit)
  static Future<CounselorSession> pauseSession(CounselorSession session) async {
    final pausedSession = session.copyWith(
      status: SessionStatus.paused,
      lastActive: DateTime.now(),
    );

    await _saveSession(pausedSession);
    return pausedSession;
  }

  /// Resume a paused session
  static Future<CounselorSession> resumeSession(String sessionId) async {
    final session = await getSession(sessionId);
    if (session == null) {
      throw Exception('Session not found');
    }

    final resumedSession = session.copyWith(
      status: SessionStatus.active,
      lastActive: DateTime.now(),
    );

    await _saveSession(resumedSession);
    return resumedSession;
  }

  /// Generate journaling prompts based on session content
  static List<String> generateJournalingPrompts(CounselorSession session) {
    // Extract key themes from the conversation
    final userMessages = session.messages.where((m) => m.isUser).toList();

    if (userMessages.isEmpty) {
      return _getDefaultPrompts(session.counselorName);
    }

    // Generate personalized prompts based on session content
    return [
      'Reflecting on our conversation today, what was the most important insight you gained?',
      'What emotions came up for you during our session, and how did they feel in your body?',
      'If you could give advice to someone facing a similar situation, what would you tell them?',
      'What is one small step you can take this week based on what we discussed?',
      'How has your perspective on this situation changed since we started talking?',
    ];
  }

  /// Get default prompts for each counselor
  static List<String> _getDefaultPrompts(String counselorName) {
    switch (counselorName) {
      case 'Dr. Sage':
        return [
          'What thoughts are you observing without judgment today?',
          'How can you find stillness in this moment?',
          'What wisdom is your inner voice sharing with you?',
          'What are you grateful for in this present moment?',
          'How can you practice self-compassion today?',
        ];
      case 'Luna':
        return [
          'What does your inner child need to hear right now?',
          'How can you show yourself kindness today?',
          'What feelings are asking for your attention?',
          'What would healing look like for you?',
          'How can you honor your journey today?',
        ];
      case 'Kai':
        return [
          'What action will you take today to move forward?',
          'What obstacles are you ready to overcome?',
          'How will you celebrate your progress?',
          'What strength did you discover in yourself today?',
          'What goal feels most important to focus on now?',
        ];
      case 'Theo':
        return [
          'What patterns are you noticing in your thoughts?',
          'How can you approach this situation more logically?',
          'What evidence supports your current perspective?',
          'What would an objective observer notice?',
          'How can you organize your thoughts more clearly?',
        ];
      case 'Dr. Elena':
        return [
          'What strategies have proven most effective for you?',
          'How can you apply what research shows about this topic?',
          'What specific techniques will you practice this week?',
          'How will you measure your progress?',
          'What does the science tell us about your situation?',
        ];
      case 'Zuri':
        return [
          'How does your identity influence your experience?',
          'What parts of yourself do you want to honor today?',
          'How can you create more belonging in your life?',
          'What would full self-acceptance look like?',
          'How can you celebrate your authentic self?',
        ];
      default:
        return [
          'What was most meaningful about our conversation today?',
          'How are you feeling right now?',
          'What would you like to remember from this session?',
          'What step will you take next?',
          'How can you be kind to yourself today?',
        ];
    }
  }
}
