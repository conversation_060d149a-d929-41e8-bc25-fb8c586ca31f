.\" Automatically generated by Pandoc 2.19.2
.\"
.\" Define V font for inline verbatim, using C font in formats
.\" that render this, and otherwise B font.
.ie "\f[CB]x\f[R]"x" \{\
. ftr V B
. ftr VI BI
. ftr VB B
. ftr VBI BI
.\}
.el \{\
. ftr V CR
. ftr VI CI
. ftr VB CB
. ftr VBI CBI
.\}
.TH "JMOD" "1" "2025" "JDK 24" "JDK Commands"
.hy
.SH NAME
.PP
jmod - create JMOD files and list the content of existing JMOD files
.SH SYNOPSIS
.PP
\f[V]jmod\f[R]
(\f[V]create\f[R]|\f[V]extract\f[R]|\f[V]list\f[R]|\f[V]describe\f[R]|\f[V]hash\f[R])
[\f[I]options\f[R]] \f[I]jmod-file\f[R]
.PP
Includes the following:
.PP
\f[B]Main operation modes\f[R]
.TP
\f[V]create\f[R]
Creates a new JMOD archive file.
.TP
\f[V]extract\f[R]
Extracts all the files from the JMOD archive file.
.TP
\f[V]list\f[R]
Prints the names of all the entries.
.TP
\f[V]describe\f[R]
Prints the module details.
.TP
\f[V]hash\f[R]
Determines leaf modules and records the hashes of the dependencies that
directly and indirectly require them.
.PP
\f[B]Options\f[R]
.TP
\f[I]options\f[R]
See \f[B]Options for jmod\f[R].
.PP
\f[B]Required\f[R]
.TP
\f[I]jmod-file\f[R]
Specifies the name of the JMOD file to create or from which to retrieve
information.
.SH DESCRIPTION
.PP
\f[B]Note:\f[R] For most development tasks, including deploying modules
on the module path or publishing them to a Maven repository, continue to
package modules in modular JAR files.
The \f[V]jmod\f[R] tool is intended for modules that have native
libraries or other configuration files or for modules that you intend to
link, with the \f[B]jlink\f[R] tool, to a runtime image.
.PP
The JMOD file format lets you aggregate files other than
\f[V].class\f[R] files, metadata, and resources.
This format is transportable but not executable, which means that you
can use it during compile time or link time but not at run time.
.PP
Many \f[V]jmod\f[R] options involve specifying a path whose contents are
copied into the resulting JMOD files.
These options copy all the contents of the specified path, including
subdirectories and their contents, but exclude files whose names match
the pattern specified by the \f[V]--exclude\f[R] option.
.PP
With the \f[V]--hash-modules\f[R] option or the \f[V]jmod hash\f[R]
command, you can, in each module\[aq]s descriptor, record hashes of the
content of the modules that are allowed to depend upon it, thus
\[dq]tying\[dq] together these modules.
This enables a package to be exported to one or more specifically-named
modules and to no others through qualified exports.
The runtime verifies if the recorded hash of a module matches the one
resolved at run time; if not, the runtime returns an error.
.SH OPTIONS FOR JMOD
.TP
\f[V]--class-path\f[R] \f[I]path\f[R]
Specifies the location of application JAR files or a directory
containing classes to copy into the resulting JMOD file.
.TP
\f[V]--cmds\f[R] \f[I]path\f[R]
Specifies the location of native commands to copy into the resulting
JMOD file.
.TP
\f[V]--compress\f[R] \f[I]compress\f[R]
Specifies the compression to use in creating the JMOD file.
The accepted values are \f[V]zip-[0-9]\f[R], where \f[V]zip-0\f[R]
provides no compression, and \f[V]zip-9\f[R] provides the best
compression.
Default is \f[V]zip-6\f[R].
.TP
\f[V]--config\f[R] \f[I]path\f[R]
Specifies the location of user-editable configuration files to copy into
the resulting JMOD file.
.TP
\f[V]--date\f[R]\f[I]TIMESTAMP\f[R]
The timestamp in ISO-8601 extended offset date-time with optional
time-zone format, to use for the timestamp of the entries, e.g.
\[dq]2022-02-12T12:30:00-05:00\[dq].
.TP
\f[V]--dir\f[R] \f[I]path\f[R]
Specifies the location where \f[V]jmod\f[R] puts extracted files from
the specified JMOD archive.
.TP
\f[V]--dry-run\f[R]
Performs a dry run of hash mode.
It identifies leaf modules and their required modules without recording
any hash values.
.TP
\f[V]--exclude\f[R] \f[I]pattern-list\f[R]
Excludes files matching the supplied comma-separated pattern list, each
element using one the following forms:
.RS
.IP \[bu] 2
\f[I]glob-pattern\f[R]
.IP \[bu] 2
\f[V]glob:\f[R]\f[I]glob-pattern\f[R]
.IP \[bu] 2
\f[V]regex:\f[R]\f[I]regex-pattern\f[R]
.PP
See the \f[B]\f[VB]FileSystem.getPathMatcher\f[B]\f[R] method for the
syntax of \f[I]glob-pattern\f[R].
See the \f[B]\f[VB]Pattern\f[B]\f[R] class for the syntax of
\f[I]regex-pattern\f[R], which represents a regular expression.
.RE
.TP
\f[V]--hash-modules\f[R] \f[I]regex-pattern\f[R]
Determines the leaf modules and records the hashes of the dependencies
directly and indirectly requiring them, based on the module graph of the
modules matching the given \f[I]regex-pattern\f[R].
The hashes are recorded in the JMOD archive file being created, or a
JMOD archive or modular JAR on the module path specified by the
\f[V]jmod hash\f[R] command.
.TP
\f[V]--header-files\f[R] \f[I]path\f[R]
Specifies the location of header files to copy into the resulting JMOD
file.
.TP
\f[V]--help\f[R] or \f[V]-h\f[R]
Prints a usage message.
.TP
\f[V]--help-extra\f[R]
Prints help for extra options.
.TP
\f[V]--legal-notices\f[R] \f[I]path\f[R]
Specifies the location of legal notices to copy into the resulting JMOD
file.
.TP
\f[V]--libs\f[R] \f[I]path\f[R]
Specifies location of native libraries to copy into the resulting JMOD
file.
.TP
\f[V]--main-class\f[R] \f[I]class-name\f[R]
Specifies main class to record in the module-info.class file.
.TP
\f[V]--man-pages\f[R] \f[I]path\f[R]
Specifies the location of man pages to copy into the resulting JMOD
file.
.TP
\f[V]--module-version\f[R] \f[I]module-version\f[R]
Specifies the module version to record in the module-info.class file.
.TP
\f[V]--module-path\f[R] \f[I]path\f[R] or \f[V]-p\f[R] \f[I]path\f[R]
Specifies the module path.
This option is required if you also specify \f[V]--hash-modules\f[R].
.TP
\f[V]--target-platform\f[R] \f[I]platform\f[R]
Specifies the target platform.
.TP
\f[V]--version\f[R]
Prints version information of the \f[V]jmod\f[R] tool.
.TP
\f[V]\[at]\f[R]\f[I]filename\f[R]
Reads options from the specified file.
.RS
.PP
An options file is a text file that contains the options and values that
you would ordinarily enter in a command prompt.
Options may appear on one line or on several lines.
You may not specify environment variables for path names.
You may comment out lines by prefixinga hash symbol (\f[V]#\f[R]) to the
beginning of the line.
.PP
The following is an example of an options file for the \f[V]jmod\f[R]
command:
.IP
.nf
\f[CB]
#Wed Dec 07 00:40:19 EST 2016
create --class-path mods/com.greetings --module-path mlib
  --cmds commands --config configfiles --header-files src/h
  --libs lib --main-class com.greetings.Main
  --man-pages man --module-version 1.0
  --os-arch \[dq]x86_x64\[dq] --os-name \[dq]macOS\[dq]
  --os-version \[dq]10.10.5\[dq] greetingsmod
\f[R]
.fi
.RE
.SH EXTRA OPTIONS FOR JMOD
.PP
In addition to the options described in \f[B]Options for jmod\f[R], the
following are extra options that can be used with the command.
.TP
\f[V]--do-not-resolve-by-default\f[R]
Exclude from the default root set of modules
.TP
\f[V]--warn-if-resolved\f[R]
Hint for a tool to issue a warning if the module is resolved.
One of deprecated, deprecated-for-removal, or incubating.
.SH JMOD CREATE EXAMPLE
.PP
The following is an example of creating a JMOD file:
.IP
.nf
\f[CB]
jmod create --class-path mods/com.greetings --cmds commands
  --config configfiles --header-files src/h --libs lib
  --main-class com.greetings.Main --man-pages man --module-version 1.0
  --os-arch \[dq]x86_x64\[dq] --os-name \[dq]macOS\[dq]
  --os-version \[dq]10.10.5\[dq] greetingsmod
\f[R]
.fi
.PP
Create a JMOD file specifying the date for the entries as
\f[V]2022 March 15 00:00:00\f[R]:
.IP
.nf
\f[CB]
jmod create --class-path build/foo/classes --date 2022-03-15T00:00:00Z
   jmods/foo1.jmod
\f[R]
.fi
.SH JMOD HASH EXAMPLE
.PP
The following example demonstrates what happens when you try to link a
leaf module (in this example, \f[V]ma\f[R]) with a required module
(\f[V]mb\f[R]), and the hash value recorded in the required module
doesn\[aq]t match that of the leaf module.
.IP "1." 3
Create and compile the following \f[V].java\f[R] files:
.RS 4
.IP \[bu] 2
\f[V]jmodhashex/src/ma/module-info.java\f[R]
.RS 2
.IP
.nf
\f[CB]
module ma {
  requires mb;
}
\f[R]
.fi
.RE
.IP \[bu] 2
\f[V]jmodhashex/src/mb/module-info.java\f[R]
.RS 2
.IP
.nf
\f[CB]
module mb {
}
\f[R]
.fi
.RE
.IP \[bu] 2
\f[V]jmodhashex2/src/ma/module-info.java\f[R]
.RS 2
.IP
.nf
\f[CB]
module ma {
  requires mb;
}
\f[R]
.fi
.RE
.IP \[bu] 2
\f[V]jmodhashex2/src/mb/module-info.java\f[R]
.RS 2
.IP
.nf
\f[CB]
module mb {
}
\f[R]
.fi
.RE
.RE
.IP "2." 3
Create a JMOD archive for each module.
Create the directories \f[V]jmodhashex/jmods\f[R] and
\f[V]jmodhashex2/jmods\f[R], and then run the following commands from
the \f[V]jmodhashex\f[R] directory, then from the \f[V]jmodhashex2\f[R]
directory:
.RS 4
.IP \[bu] 2
\f[V]jmod create --class-path mods/ma jmods/ma.jmod\f[R]
.IP \[bu] 2
\f[V]jmod create --class-path mods/mb jmods/mb.jmod\f[R]
.RE
.IP "3." 3
Optionally preview the \f[V]jmod hash\f[R] command.
Run the following command from the \f[V]jmodhashex\f[R] directory:
.RS 4
.PP
\f[V]jmod hash --dry-run -module-path jmods --hash-modules .*\f[R]
.PP
The command prints the following:
.IP
.nf
\f[CB]
Dry run:
mb
  hashes ma SHA-256 07667d5032004b37b42ec2bb81b46df380cf29e66962a16481ace2e71e74073a
\f[R]
.fi
.PP
This indicates that the \f[V]jmod hash\f[R] command (without the
\f[V]--dry-run\f[R] option) will record the hash value of the leaf
module \f[V]ma\f[R] in the module \f[V]mb\f[R].
.RE
.IP "4." 3
Record hash values in the JMOD archive files contained in the
\f[V]jmodhashex\f[R] directory.
Run the following command from the \f[V]jmodhashex\f[R] directory:
.RS 4
.RS
.PP
\f[V]jmod hash --module-path jmods --hash-modules .*\f[R]
.RE
.PP
The command prints the following:
.RS
.PP
\f[V]Hashes are recorded in module mb\f[R]
.RE
.RE
.IP "5." 3
Print information about each JMOD archive contained in the
\f[V]jmodhashex\f[R] directory.
Run the highlighted commands from the \f[V]jmodhashex\f[R] directory:
.RS 4
.IP
.nf
\f[CB]
jmod describe jmods/ma.jmod

ma
  requires mandated java.base
  requires mb

jmod describe jmods/mb.jmod

mb
  requires mandated java.base
  hashes ma SHA-256 07667d5032004b37b42ec2bb81b46df380cf29e66962a16481ace2e71e74073a
\f[R]
.fi
.RE
.IP "6." 3
Attempt to create a runtime image that contains the module \f[V]ma\f[R]
from the directory \f[V]jmodhashex2\f[R] but the module \f[V]mb\f[R]
from the directory \f[V]jmodhashex\f[R].
Run the following command from the \f[V]jmodhashex2\f[R] directory:
.RS 4
.IP \[bu] 2
\f[B]Linux and macOS:\f[R]
.RS 2
.RS
.PP
\f[V]jlink --module-path $JAVA_HOME/jmods:jmods/ma.jmod:../jmodhashex/jmods/mb.jmod --add-modules ma --output ma-app\f[R]
.RE
.RE
.IP \[bu] 2
\f[B]Windows:\f[R]
.RS 2
.RS
.PP
\f[V]jlink --module-path %JAVA_HOME%/jmods;jmods/ma.jmod;../jmodhashex/jmods/mb.jmod --add-modules ma --output ma-app\f[R]
.RE
.RE
.PP
The command prints an error message similar to the following:
.IP
.nf
\f[CB]
Error: Hash of ma (a2d77889b0cb067df02a3abc39b01ac1151966157a68dc4241562c60499150d2) differs to
expected hash (07667d5032004b37b42ec2bb81b46df380cf29e66962a16481ace2e71e74073a) recorded in mb
\f[R]
.fi
.RE
